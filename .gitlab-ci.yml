stages:
  - build

build-mac:
  stage: build
  tags:
    - macos
  before_script:
    - flutter doctor
    - flutter pub get
    - brew install node
    - npm install -g appdmg
  script:
    - flutter build macos --release
    - appdmg ./appdmg-config.json ./app.dmg
  artifacts:
    paths:
      - ./app.dmg
  when: manual

build-windows:
  stage: build
  tags:
    - windows
  before_script:
    - flutter doctor
    - flutter pub get
  script:
    - flutter build windows --release
    - "\"C:\\Program Files (x86)\\Inno Setup 6\\ISCC.exe\" ./installer.iss"
  artifacts:
    paths:
      - ./Output/setup.exe
  when: manual