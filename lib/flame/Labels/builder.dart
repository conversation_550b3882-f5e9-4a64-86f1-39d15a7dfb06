import 'package:flame/components.dart';
import 'package:flame_svg/flame_svg.dart';
import 'package:flutter/material.dart';

final _defaultSize = Vector2(230, 230);

PolygonComponent buildUnderlyingShape(String type, Vector2 size) {
  switch (type) {
    case "Hazard ID Num":
      // return PolygonComponent.relative([Vector2(-1, 0.43), Vector2(1, 0.43), Vector2(1, -0.33), Vector2(-1, -1)], parentSize: size);
      return RectangleComponent.fromRect(
        Rect.fromCenter(
          center: Offset(size.x * 0.5, size.y * 0.5),
          width: size.x,
          height: size.y,
        ),
        anchor: Anchor.center,
      );
    default:
      // return PolygonComponent.relative([Vector2(1.0, 0.0), Vector2(0.0, -1.0), Vector2(-1.0, 0.0), Vector2(0.0, 1.0)], parentSize: size);
      return RectangleComponent.fromRect(Rect.fromLTWH(0, 0, size.x, size.y));
  }
}

Future<PositionComponent> buildBaseLabel({required String svgPath, Vector2? size}) async {
  final svg = await Svg.load(svgPath);
  return PositionComponent(
    size: size ?? _defaultSize,
    anchor: Anchor.center,
    children: [
      SvgComponent(
        size: size ?? _defaultSize,
        anchor: Anchor.topLeft,
        svg: svg,
      )
    ],
  );
}

Future<PositionComponent> buildLabelFromType(String type, {Map<String, dynamic>? variables, Vector2? size}) async {
  switch (type) {
    case "Corrosive":
      return buildCorrosive(variables?["Label Type"] ?? 0, materialNumber: int.tryParse(variables?["Material Number"] ?? "") ?? 0, size: size);
    case "Explosives":
      return buildExplosives(variables?["Label Type"] ?? 0, size: size);
    case "Flammable Gas":
      return buildFlamGas(variables?["Label Type"] ?? 0, size: size);
    case "Flammable Liquid":
      return buildFlamLiq(variables?["Label Type"] ?? 0, materialNumber: int.tryParse(variables?["Material Number"] ?? "") ?? 0, size: size);
    case "Flammable Solid":
      return buildFlamSolid(variables?["Label Type"] ?? 0, size: size);
    case "Hazard ID Num":
      return buildHazIDNum(variables?["Label Type"] ?? 0, hazardNumber: int.tryParse(variables?["Hazard Number"] ?? "") ?? 0, size: size);
    case "NFPA 704":
      return buildNFPA704(
        flammability: variables?["Flammability"] ?? 0,
        health: variables?["Health"] ?? 0,
        instability: variables?["Instability"] ?? 0,
        specialHazard: variables?["Special Hazard"] ?? 0,
        size: size,
      );
    case "Non-Flammable Gas":
      return buildNonFlamGas(variables?["Label Type"] ?? 0, materialNumber: int.tryParse(variables?["Material Number"] ?? "") ?? 0, size: size);
    case "Oxidizer-Org Perox":
      return buildOxOrg(variables?["Label Type"] ?? 0, size: size);
    case "Oxidizing Gas":
      return buildOxGas(variables?["Label Type"] ?? 0, size: size);
    case "Radioactive":
      return buildRadioactive(variables?["Label Type"] ?? 0, size: size);
    case "Specialty":
      return buildSpecialty(variables?["Label Type"] ?? 0, size: size);
    case "Substances Class 9":
      return buildSubstances(variables?["Label Type"] ?? 0, size: size);
    case "Tox-Corr Gas":
      return buildToxCorGas(variables?["Label Type"] ?? 0, materialNumber: int.tryParse(variables?["Material Number"] ?? "") ?? 0, size: size);
    case "Tocix-Corr Substance":
      return buildToxCorSubst(variables?["Label Type"] ?? 0, size: size);
    default:
      throw Exception("Unknown label type: $type");
  }
}

Future<PositionComponent> buildCorrosive(int type, {int materialNumber = 0, Vector2? size}) async {
  final effectiveSize = size ?? _defaultSize;
  final comp = await buildBaseLabel(svgPath: "labels/corrosive/base.svg", size: effectiveSize);
  final secondarySvg = await Svg.load("labels/corrosive/corrsym.svg");
  final secondary = SvgComponent(
    size: Vector2(effectiveSize.x, effectiveSize.y * 0.22),
    anchor: Anchor.center,
    position: Vector2(effectiveSize.x * 0.5, effectiveSize.y * 0.29),
    svg: secondarySvg,
  );
  final eight = TextComponent(
    text: "8",
    anchor: Anchor.center,
    size: Vector2(effectiveSize.x * 0.5, effectiveSize.y * 0.5),
    position: Vector2(effectiveSize.x * 0.5, effectiveSize.y * 0.85),
    textRenderer: TextPaint(
      style: TextStyle(
        color: Colors.white,
        fontWeight: FontWeight.bold,
        fontSize: effectiveSize.x * 0.1,
      ),
    ),
  );
  comp.addAll([secondary, eight]);
  PositionComponent? child;
  final containerSize = Vector2(effectiveSize.x * 0.4, effectiveSize.y * 0.2);
  final containerPosition = Vector2(effectiveSize.x * 0.5, effectiveSize.y * 0.6);
  switch (type) {
    case 0:
      child = RectangleComponent(
        size: containerSize,
        paint: Paint()..color = const Color(0xFF000000),
        position: containerPosition,
        anchor: Anchor.center,
      )..add(
          TextComponent(
            text: "CORROSIVE",
            anchor: Anchor.center,
            size: Vector2(containerSize.x * 0.5, containerSize.y * 0.5),
            position: Vector2(containerSize.x * 0.5, containerSize.y * 0.1),
            textRenderer: TextPaint(
              style: TextStyle(
                fontSize: containerSize.y * 0.6,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        );
      break;
    case 1:
      // Blank label
      child = null;
      break;
    case 2:
      child = RectangleComponent(
        size: containerSize,
        paint: Paint()..color = const Color(0xFF000000),
        position: containerPosition,
        anchor: Anchor.center,
      )..add(
          TextComponent(
            text: materialNumber.toString().padRight(4, "0"),
            anchor: Anchor.center,
            size: Vector2(containerSize.x * 0.5, containerSize.y * 0.5),
            position: Vector2(containerSize.x * 0.5, containerSize.y * 0.1),
          ),
        );
      break;
    default:
      throw Exception("Unknown corrosive type: $type");
  }
  if (child != null) {
    await comp.add(child);
  }
  return comp;
}

Future<PositionComponent> buildExplosives(int type, {Vector2? size}) async {
  PositionComponent? top;
  PositionComponent? middle;
  PositionComponent? bottom;

  int? label;
  int? secLabel;
  int? num4splat;
  bool splat = true;
  switch (type) {
    case 0:
      label = 0;
      secLabel = 0;
      break;

    case 1:
      label = 0;
      secLabel = 1;
      break;

    case 2:
      label = 0;
      secLabel = 2;
      break;

    case 3:
      label = 0;
      secLabel = 3;
      break;

    case 4:
      label = 0;
      secLabel = 4;
      break;

    case 5:
      label = 0;
      secLabel = 5;
      break;

    case 6:
      label = 0;
      splat = false;
      secLabel = 2;
      num4splat = 0;
      break;

    case 7:
      label = 2;
      splat = false;
      num4splat = 1;
      secLabel = 6;
      break;

    case 8:
      label = 0;
      splat = false;
      secLabel = 7;
      num4splat = 2;
      break;

    case 9:
      label = null;
      break;

    case 10:
      label = null;
      splat = false;
      break;
  }

  final effectiveSize = (size ?? _defaultSize);
  final comp = await buildBaseLabel(svgPath: "labels/Explosives/base.svg", size: effectiveSize);

  if (splat) {
    final splatSvg = await Svg.load("labels/Explosives/splat.svg");
    top = SvgComponent(
      size: Vector2(effectiveSize.x * 0.4, effectiveSize.y * 0.4),
      anchor: Anchor.center,
      position: Vector2(effectiveSize.x * 0.5, effectiveSize.y * 0.3),
      svg: splatSvg,
    );
  } else {
    if (num4splat != null) {
      final containerSize = (size ?? _defaultSize) * 0.3;
      final position = Vector2(effectiveSize.x * 0.5, effectiveSize.y * 0.3);
      final textPaint = TextPaint(
        style: TextStyle(
          fontSize: containerSize.x * 0.8,
          color: Colors.black,
        ),
      );
      switch (num4splat) {
        case 0:
          top = TextComponent(
            text: "1.4",
            anchor: Anchor.center,
            size: containerSize,
            position: position,
            textRenderer: textPaint,
          );
          break;
        case 1:
          top = TextComponent(
            text: "1.5",
            anchor: Anchor.center,
            size: containerSize,
            position: position,
            textRenderer: textPaint,
          );
          break;
        case 2:
          top = TextComponent(
            text: "1.6",
            anchor: Anchor.center,
            size: containerSize,
            position: position,
            textRenderer: textPaint,
          );
          break;
        default:
          throw Exception("Unknown splat type: $num4splat");
      }
    }
  }
  if (top != null) {
    comp.add(top);
  }

  if (label != null) {
    final containerSize = (size ?? _defaultSize) * 0.28;
    final position = Vector2(effectiveSize.x * 0.5, effectiveSize.y * 0.56);
    print("containerSize.y * 0.8 ${containerSize.y * 0.8}");
    final textPaint = TextPaint(
      style: TextStyle(
        fontSize: containerSize.y * 0.4,
        fontWeight: FontWeight.bold,
        color: Colors.black,
      ),
    );
    switch (label) {
      case 0:
      case 1:
        middle = TextComponent(
          text: "EXPLOSIVES",
          anchor: Anchor.center,
          size: containerSize,
          position: position,
          textRenderer: textPaint,
        );
        break;
      case 2:
        middle = TextComponent(
          text: "BLASTING AGENTS",
          anchor: Anchor.center,
          size: containerSize,
          position: position,
          textRenderer: textPaint,
        );
        break;
      default:
        throw Exception("Unknown label type: $label");
    }
  }
  if (middle != null) {
    comp.add(middle);
  }

  if (secLabel != null) {
    final containerSize = (size ?? _defaultSize) * 0.3;
    final position = Vector2(effectiveSize.x * 0.5, effectiveSize.y * 0.7);
    final textPaint = TextPaint(
      style: TextStyle(
        fontSize: containerSize.x * 0.6,
        fontWeight: FontWeight.bold,
        color: Colors.black,
      ),
    );
    String text = "";
    switch (label) {
      case 0:
        text = "A";
        break;
      case 1:
        text = "B";
        break;
      case 2:
        text = "*";
        break;
      case 3:
        text = "1.1*";
        break;
      case 4:
        text = "1.2*";
        break;
      case 5:
        text = "1.3*";
        break;
      case 6:
        text = "D";
        break;
      case 7:
        text = "N";
        break;
      default:
        throw Exception("Unknown label type: $label");
    }
    bottom = TextComponent(
      text: text,
      anchor: Anchor.center,
      size: containerSize,
      position: position,
      textRenderer: textPaint,
    );
  }
  if (bottom != null) {
    comp.add(bottom);
  }

  comp.add(TextComponent(
    text: "1",
    anchor: Anchor.center,
    size: effectiveSize * 0.3,
    position: Vector2(effectiveSize.x * 0.5, effectiveSize.y * 0.89),
    textRenderer: TextPaint(
      style: TextStyle(
        fontSize: effectiveSize.x * 0.12,
        fontWeight: FontWeight.bold,
        color: Colors.black,
      ),
    ),
  ));

  return comp;
}

Future<PositionComponent> buildFlamGas(int type, {Vector2? size}) async {
  final effectiveSize = size ?? _defaultSize;
  final comp = await buildBaseLabel(svgPath: "labels/FlamGas/base.svg", size: effectiveSize);
  final flame = await Svg.load("labels/FlamGas/flame.svg");
  final flameComp = SvgComponent(
    size: Vector2(effectiveSize.x * 0.4, effectiveSize.y * 0.4),
    anchor: Anchor.center,
    position: Vector2(effectiveSize.x * 0.5, effectiveSize.y * 0.3),
    svg: flame,
  );
  comp.add(flameComp);
  if (type == 0) {
    final flammableText = TextComponent(
      text: "FLAMMABLE",
      anchor: Anchor.center,
      size: Vector2(effectiveSize.x * 0.4, effectiveSize.y * 0.4),
      position: Vector2(effectiveSize.x * 0.5, effectiveSize.y * 0.55),
      textRenderer: TextPaint(
        style: TextStyle(
          fontSize: effectiveSize.x * 0.1,
          fontWeight: FontWeight.bold,
          color: Colors.white,
        ),
      ),
    );
    final gasText = TextComponent(
      text: "GAS",
      anchor: Anchor.center,
      size: Vector2(effectiveSize.x * 0.4, effectiveSize.y * 0.4),
      position: Vector2(effectiveSize.x * 0.5, effectiveSize.y * 0.65),
      textRenderer: TextPaint(
        style: TextStyle(
          fontSize: effectiveSize.x * 0.1,
          fontWeight: FontWeight.bold,
          color: Colors.white,
        ),
      ),
    );
    comp.add(flammableText);
    comp.add(gasText);
  }
  final numberText = TextComponent(
    text: "2",
    anchor: Anchor.center,
    size: Vector2(effectiveSize.x * 0.4, effectiveSize.y * 0.4),
    position: Vector2(effectiveSize.x * 0.5, effectiveSize.y * 0.85),
    textRenderer: TextPaint(
      style: TextStyle(
        fontSize: effectiveSize.x * 0.1,
        fontWeight: FontWeight.bold,
        color: Colors.white,
      ),
    ),
  );
  comp.add(numberText);
  return comp;
}

Future<PositionComponent> buildFlamLiq(int type, {int materialNumber = 0, Vector2? size}) async {
  final effectiveSize = size ?? _defaultSize;
  final comp = await buildBaseLabel(svgPath: "labels/FlamLiq/base.svg", size: effectiveSize);
  final flame = await Svg.load("labels/FlamLiq/flame.svg");
  const _labels = ['flammable', 'combustible', 'gasoline', 'fuel-oil'];
  final flameComp = SvgComponent(
    size: Vector2(effectiveSize.x * 0.4, effectiveSize.y * 0.4),
    anchor: Anchor.center,
    position: Vector2(effectiveSize.x * 0.5, effectiveSize.y * 0.3),
    svg: flame,
  );
  comp.add(flameComp);
  switch (type) {
    case 0:
    case 1:
    case 2:
    case 3:
      final flammableText = TextComponent(
        text: _labels[type].toUpperCase(),
        anchor: Anchor.center,
        size: Vector2(effectiveSize.x * 0.4, effectiveSize.y * 0.4),
        position: Vector2(effectiveSize.x * 0.5, effectiveSize.y * 0.55),
        textRenderer: TextPaint(
          style: TextStyle(
            fontSize: effectiveSize.x * 0.1,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
      );
      comp.add(flammableText);
      break;
    case 4:
    case 5:
      final materialContainer = RectangleComponent(
        size: Vector2(effectiveSize.x * 0.4, effectiveSize.y * 0.15),
        position: Vector2(effectiveSize.x * 0.5, effectiveSize.y * 0.6),
        anchor: Anchor.center,
        paint: Paint()..color = const Color(0xFFFFFFFF),
      );
      final materialText = TextComponent(
        text: materialNumber.toString().padRight(4, "0"),
        anchor: Anchor.center,
        size: Vector2(effectiveSize.x * 0.35, effectiveSize.y * 0.35),
        position: Vector2(effectiveSize.x * 0.5, effectiveSize.y * 0.6),
        textRenderer: TextPaint(
          style: TextStyle(
            fontSize: effectiveSize.x * 0.15,
            fontWeight: FontWeight.bold,
            color: Colors.black,
          ),
        ),
      );
      comp.addAll([
        materialContainer,
        materialText,
      ]);
      break;
    default:
      // throw Exception("Unknown flammable liquid type: $type");
      // Don't draw anything
      break;
  }
  if (type == 5) {
    final numberBlackTriangle = await Svg.load("labels/FlamLiq/bottom-triangle.svg");
    final numberBlackTriangleComp = SvgComponent(
      size: Vector2(effectiveSize.x * 0.54, effectiveSize.y * 0.54),
      anchor: Anchor.center,
      position: Vector2(effectiveSize.x * 0.5, effectiveSize.y * 0.85),
      svg: numberBlackTriangle,
    );
    final residue = TextComponent(
      text: "RESIDUE",
      anchor: Anchor.center,
      size: Vector2(effectiveSize.x * 0.4, effectiveSize.y * 0.4),
      position: Vector2(effectiveSize.x * 0.5, effectiveSize.y * 0.76),
      textRenderer: TextPaint(
        style: TextStyle(
          fontSize: effectiveSize.x * 0.06,
          fontWeight: FontWeight.bold,
          color: Colors.white,
        ),
      ),
    );
    comp.addAll([numberBlackTriangleComp, residue]);
  }
  final numberText = TextComponent(
    text: "3",
    anchor: Anchor.center,
    size: Vector2(effectiveSize.x * 0.4, effectiveSize.y * 0.4),
    position: Vector2(effectiveSize.x * 0.5, effectiveSize.y * 0.85),
    textRenderer: TextPaint(
      style: TextStyle(
        fontSize: effectiveSize.x * 0.1,
        fontWeight: FontWeight.bold,
        color: Colors.white,
      ),
    ),
  );
  comp.add(numberText);
  return comp;
}

Future<PositionComponent> buildFlamSolid(int type, {Vector2? size}) async {
  final effectiveSize = size ?? _defaultSize;
  String path = "";
  Color fourColor;
  PositionComponent topComponent;
  switch (type) {
    case 0:
    case 1:
    case 2:
      path = "labels/FlamSolid/stripes.svg";
      fourColor = Colors.black;
      break;
    case 3:
    case 4:
      path = "labels/FlamSolid/red-white.svg";
      fourColor = Colors.black;
      break;
    case 5:
    case 6:
      path = "labels/FlamSolid/blue.svg";
      fourColor = Colors.white;
      break;
    case 7:
      path = "labels/FlamSolid/blue-stripes.svg";
      fourColor = Colors.black;
      break;
    default:
      throw Exception("Unknown flammable solid type: $type");
  }
  final comp = await buildBaseLabel(svgPath: path, size: effectiveSize);
  final four = TextComponent(
    text: "4",
    anchor: Anchor.center,
    size: Vector2(effectiveSize.x * 0.4, effectiveSize.y * 0.4),
    position: Vector2(effectiveSize.x * 0.5, effectiveSize.y * 0.85),
    textRenderer: TextPaint(
      style: TextStyle(
        fontSize: effectiveSize.x * 0.1,
        fontWeight: FontWeight.bold,
        color: fourColor,
      ),
    ),
  );
  comp.add(four);
  Svg topSvg = await Svg.load("labels/FlamSolid/black.svg");
  if (type >= 5 && type < 7) {
    topSvg = await Svg.load("labels/FlamSolid/white.svg");
  }
  if (type < 7) {
    topComponent = SvgComponent(
      size: Vector2(effectiveSize.x * 0.25, effectiveSize.y * 0.25),
      anchor: Anchor.center,
      position: Vector2(effectiveSize.x * 0.5, effectiveSize.y * 0.2),
      svg: topSvg,
    );
  } else {
    topComponent = TextComponent(
      text: "W",
      anchor: Anchor.center,
      size: Vector2(effectiveSize.x * 0.4, effectiveSize.y * 0.4),
      position: Vector2(effectiveSize.x * 0.5, effectiveSize.y * 0.25),
      textRenderer: TextPaint(
        style: TextStyle(
          fontSize: effectiveSize.x * 0.15,
          fontWeight: FontWeight.bold,
          color: Colors.white,
        ),
      ),
    );
  }
  comp.add(topComponent);
  List<PositionComponent>? middleText;
  if (type == 0 || type == 1 || type == 7) {
    middleText = [
      TextComponent(
        text: "FLAMMABLE",
        anchor: Anchor.center,
        size: Vector2(effectiveSize.x * 0.4, effectiveSize.y * 0.4),
        position: Vector2(effectiveSize.x * 0.5, effectiveSize.y * 0.45),
        textRenderer: TextPaint(
          style: TextStyle(
            fontSize: effectiveSize.x * 0.1,
            fontWeight: FontWeight.bold,
            color: Colors.black,
            backgroundColor: type == 1 || type == 7 ? Colors.white : null,
          ),
        ),
      ),
      TextComponent(
        text: "SOLID",
        anchor: Anchor.center,
        size: Vector2(effectiveSize.x * 0.4, effectiveSize.y * 0.4),
        position: Vector2(effectiveSize.x * 0.5, effectiveSize.y * 0.55),
        textRenderer: TextPaint(
          style: TextStyle(
            fontSize: effectiveSize.x * 0.1,
            fontWeight: FontWeight.bold,
            color: Colors.black,
            backgroundColor: type == 1 || type == 7 ? Colors.white : null,
          ),
        ),
      ),
    ];
  }
  if (type == 3) {
    middleText = [
      TextComponent(
        text: "SPONTANEOUSLY",
        anchor: Anchor.center,
        size: Vector2(effectiveSize.x * 0.3, effectiveSize.y * 0.3),
        position: Vector2(effectiveSize.x * 0.5, effectiveSize.y * 0.45),
        textRenderer: TextPaint(
          style: TextStyle(
            fontSize: effectiveSize.x * 0.07,
            fontWeight: FontWeight.bold,
            color: Colors.black,
          ),
        ),
      ),
      TextComponent(
        text: "COMBUSTIBLE",
        anchor: Anchor.center,
        size: Vector2(effectiveSize.x * 0.3, effectiveSize.y * 0.3),
        position: Vector2(effectiveSize.x * 0.5, effectiveSize.y * 0.55),
        textRenderer: TextPaint(
          style: TextStyle(
            fontSize: effectiveSize.x * 0.09,
            fontWeight: FontWeight.bold,
            color: Colors.black,
          ),
        ),
      ),
    ];
  }
  if (type == 5) {
    middleText = [
      TextComponent(
        text: "DANGEROUS",
        anchor: Anchor.center,
        size: Vector2(effectiveSize.x * 0.4, effectiveSize.y * 0.4),
        position: Vector2(effectiveSize.x * 0.37, effectiveSize.y * 0.5),
        textRenderer: TextPaint(
          style: TextStyle(
            fontSize: effectiveSize.x * 0.08,
            fontWeight: FontWeight.w800,
            color: Colors.white,
            backgroundColor: type == 1 || type == 7 ? Colors.white : null,
          ),
        ),
      ),
      TextComponent(
        text: "WHEN",
        anchor: Anchor.center,
        size: Vector2(effectiveSize.x * 0.4, effectiveSize.y * 0.4),
        position: Vector2(effectiveSize.x * 0.75, effectiveSize.y * 0.45),
        textRenderer: TextPaint(
          style: TextStyle(
            fontSize: effectiveSize.x * 0.06,
            fontWeight: FontWeight.w800,
            color: Colors.white,
            backgroundColor: type == 1 || type == 7 ? Colors.white : null,
          ),
        ),
      ),
      TextComponent(
        text: "WET",
        anchor: Anchor.center,
        size: Vector2(effectiveSize.x * 0.4, effectiveSize.y * 0.4),
        position: Vector2(effectiveSize.x * 0.75, effectiveSize.y * 0.55),
        textRenderer: TextPaint(
          style: TextStyle(
            fontSize: effectiveSize.x * 0.06,
            fontWeight: FontWeight.w800,
            color: Colors.white,
            backgroundColor: type == 1 || type == 7 ? Colors.white : null,
          ),
        ),
      ),
    ];
  }
  if (middleText != null) {
    comp.addAll(middleText);
  }
  return comp;
}

Future<PositionComponent> buildHazIDNum(int type, {int hazardNumber = 0, Vector2? size}) async {
  final s = size ?? _defaultSize;
  final effectiveSize = Vector2(s.x, s.x * 0.35);
  // String path = "";
  Color bgColor = Colors.transparent;
  switch (type) {
    case 0:
      // path = "labels/HazIDNum/orange-back.svg";
      bgColor = const Color(0xFFED702D);
      break;
    case 1:
      // path = "labels/HazIDNum/white-back.svg";
      bgColor = Colors.white;
      break;
    default:
      throw Exception("Unknown HazIDNum type: $type");
  }
  // final comp = await buildBaseLabel(svgPath: path, size: effectiveSize);
  final comp = PositionComponent(
    size: effectiveSize,
    anchor: Anchor.center,
    position: Vector2(0, s.y * 0.35),
    children: [
      RectangleComponent(
        size: effectiveSize,
        anchor: Anchor.topLeft,
        paint: Paint()..color = bgColor,
      )
    ],
  );
  final numberText = TextComponent(
    text: hazardNumber.toString().padRight(4, "0"),
    anchor: Anchor.center,
    size: Vector2(effectiveSize.x * 0.4, effectiveSize.y * 0.4),
    position: Vector2(effectiveSize.x * 0.5, effectiveSize.y * 0.5),
    textRenderer: TextPaint(
      style: TextStyle(
        fontSize: effectiveSize.x * 0.2,
        fontWeight: FontWeight.bold,
        color: Colors.black,
      ),
    ),
  );
  final frame = RectangleComponent(
    size: Vector2(effectiveSize.x * 0.97, effectiveSize.y),
    position: effectiveSize * 0.5,
    anchor: Anchor.center,
    paint: Paint()
      ..color = Colors.black
      ..style = PaintingStyle.stroke
      ..strokeWidth = 3,
  );
  comp.addAll([
    frame,
    numberText,
  ]);
  return comp;
}

Future<PositionComponent> buildNFPA704({int flammability = 0, int health = 0, int instability = 0, int specialHazard = 0, Vector2? size}) async {
  final effectiveSize = size ?? _defaultSize;
  final comp = await buildBaseLabel(svgPath: "labels/NFPA704/base.svg", size: effectiveSize);
  final textPaint = TextPaint(
    style: TextStyle(
      fontSize: effectiveSize.x * 0.2,
      fontWeight: FontWeight.w800,
      color: Colors.black,
    ),
  );
  final topText = TextComponent(
    text: flammability.toString(),
    anchor: Anchor.center,
    size: Vector2(effectiveSize.x * 0.4, effectiveSize.y * 0.4),
    position: Vector2(effectiveSize.x * 0.5, effectiveSize.y * 0.23),
    textRenderer: textPaint,
  );
  final leftText = TextComponent(
    text: health.toString(),
    anchor: Anchor.center,
    size: Vector2(effectiveSize.x * 0.4, effectiveSize.y * 0.4),
    position: Vector2(effectiveSize.x * 0.25, effectiveSize.y * 0.48),
    textRenderer: textPaint,
  );
  final rightText = TextComponent(
    text: instability.toString(),
    anchor: Anchor.center,
    size: Vector2(effectiveSize.x * 0.4, effectiveSize.y * 0.4),
    position: Vector2(effectiveSize.x * 0.75, effectiveSize.y * 0.48),
    textRenderer: textPaint,
  );
  comp.addAll([
    topText,
    leftText,
    rightText,
  ]);
  if (specialHazard > 0) {
    final svgPath = "labels/NFPA704/sp-$specialHazard.svg";
    final bottomSvg = await Svg.load(svgPath);
    final bottom = SvgComponent(
      size: (size ?? _defaultSize) * 0.3,
      anchor: Anchor.center,
      position: Vector2(effectiveSize.x * 0.55, effectiveSize.y * 0.75),
      svg: bottomSvg,
    );
    comp.add(bottom);
  }
  return comp;
}

Future<PositionComponent> buildNonFlamGas(int type, {int materialNumber = 0, Vector2? size}) async {
  final effectiveSize = size ?? _defaultSize;
  String svgPath = "";
  String cylinderPath = "";
  Color bottomTextColor = Colors.transparent;
  switch (type) {
    case 0:
    case 1:
    case 2:
      svgPath = "labels/NonFlamGas/green-back.svg";
      cylinderPath = "labels/NonFlamGas/white-cyl.svg";
      bottomTextColor = Colors.white;
      break;
    case 3:
    case 4:
      svgPath = "labels/NonFlamGas/white-back.svg";
      cylinderPath = "labels/NonFlamGas/black-cyl.svg";
      bottomTextColor = Colors.black;
      break;
    default:
      throw Exception("Unknown non-flammable gas type: $type");
  }
  final comp = await buildBaseLabel(svgPath: svgPath, size: effectiveSize);
  final cylinderSvg = await Svg.load(cylinderPath);
  final cylinderComp = SvgComponent(
    size: Vector2(effectiveSize.x * 0.4, effectiveSize.y * 0.4),
    anchor: Anchor.center,
    position: Vector2(effectiveSize.x * 0.5, effectiveSize.y * 0.3),
    svg: cylinderSvg,
  );
  final bottomComp = TextComponent(
    text: "2",
    anchor: Anchor.center,
    size: Vector2(effectiveSize.x * 0.4, effectiveSize.y * 0.4),
    position: Vector2(effectiveSize.x * 0.5, effectiveSize.y * 0.85),
    textRenderer: TextPaint(
      style: TextStyle(
        fontSize: effectiveSize.x * 0.1,
        fontWeight: FontWeight.bold,
        color: bottomTextColor,
      ),
    ),
  );
  comp.addAll([cylinderComp, bottomComp]);
  if (type == 0) {
    final textPaint = TextPaint(
      style: TextStyle(
        fontSize: effectiveSize.x * 0.09,
        fontWeight: FontWeight.bold,
        color: Colors.white,
      ),
    );
    comp.addAll([
      TextComponent(
        text: "NON-FLAMMABLE",
        anchor: Anchor.center,
        size: Vector2(effectiveSize.x * 0.4, effectiveSize.y * 0.4),
        position: Vector2(effectiveSize.x * 0.5, effectiveSize.y * 0.5),
        textRenderer: textPaint,
      ),
      TextComponent(
        text: "GAS",
        anchor: Anchor.center,
        size: Vector2(effectiveSize.x * 0.4, effectiveSize.y * 0.4),
        position: Vector2(effectiveSize.x * 0.5, effectiveSize.y * 0.6),
        textRenderer: textPaint,
      ),
    ]);
  } else if (type == 1 || type == 3) {
    final textSize = Vector2(effectiveSize.x * 0.4, effectiveSize.y * 0.19);
    final middleComp = TextComponent(
      text: materialNumber.toString().padRight(4, "0"),
      anchor: Anchor.center,
      size: textSize,
      position: Vector2(effectiveSize.x * 0.5, effectiveSize.y * 0.52),
      children: [
        RectangleComponent.relative(
          Vector2.all(1),
          parentSize: Vector2(textSize.x, textSize.y),
          anchor: Anchor.topLeft,
          position: Vector2(-1, 0),
          paint: Paint()
            ..color = Colors.black
            ..strokeWidth = 2
            ..style = PaintingStyle.stroke,
        )
      ],
      textRenderer: TextPaint(
        style: TextStyle(
          fontSize: effectiveSize.x * 0.15,
          fontWeight: FontWeight.bold,
          color: Colors.black,
          backgroundColor: Colors.white,
        ),
      ),
    );
    comp.add(middleComp);
  }
  return comp;
}

Future<PositionComponent> buildOxGas(int type, {Vector2? size}) async {
  final effectiveSize = size ?? _defaultSize;
  final comp = await buildBaseLabel(svgPath: "labels/OxGas/base.svg", size: effectiveSize);
  if (type == 0) {
    final centerComponent = TextComponent(
      text: "OXYGEN",
      anchor: Anchor.center,
      size: Vector2(effectiveSize.x * 0.4, effectiveSize.y * 0.4),
      position: Vector2(effectiveSize.x * 0.5, effectiveSize.y * 0.52),
      textRenderer: TextPaint(
        style: TextStyle(
          fontSize: effectiveSize.x * 0.15,
          fontWeight: FontWeight.w800,
          color: Colors.black,
        ),
      ),
    );
    comp.add(centerComponent);
  }
  final bottomText = TextComponent(
    text: "2",
    anchor: Anchor.center,
    size: Vector2(effectiveSize.x * 0.4, effectiveSize.y * 0.4),
    position: Vector2(effectiveSize.x * 0.5, effectiveSize.y * 0.85),
    textRenderer: TextPaint(
      style: TextStyle(
        fontSize: effectiveSize.x * 0.1,
        fontWeight: FontWeight.bold,
        color: Colors.black,
      ),
    ),
  );
  comp.add(bottomText);
  return comp;
}

Future<PositionComponent> buildOxOrg(int type, {Vector2? size}) async {
  final effectiveSize = size ?? _defaultSize;
  final comp = await buildBaseLabel(svgPath: "labels/OxOrg/base.svg", size: effectiveSize);
  if (type < 0 || type > 3) {
    throw Exception("Unknown oxidizer organic type: $type");
  }
  final bottomText = TextComponent(
    text: type == 0 || type == 1 ? "5.1" : "5.2",
    anchor: Anchor.center,
    size: Vector2(effectiveSize.x * 0.4, effectiveSize.y * 0.4),
    position: Vector2(effectiveSize.x * 0.5, effectiveSize.y * 0.83),
    textRenderer: TextPaint(
      style: TextStyle(
        fontSize: effectiveSize.x * 0.1,
        fontWeight: FontWeight.bold,
        color: Colors.black,
      ),
    ),
  );
  comp.add(bottomText);
  if (type == 0) {
    comp.add(TextComponent(
      text: "OXIDIZER",
      anchor: Anchor.center,
      size: Vector2(effectiveSize.x * 0.4, effectiveSize.y * 0.4),
      position: Vector2(effectiveSize.x * 0.5, effectiveSize.y * 0.51),
      textRenderer: TextPaint(
        style: TextStyle(
          fontSize: effectiveSize.x * 0.155,
          fontWeight: FontWeight.w800,
          color: Colors.black,
        ),
      ),
    ));
  } else if (type == 2) {
    final textPaint = TextPaint(
      style: TextStyle(
        fontSize: effectiveSize.x * 0.12,
        fontWeight: FontWeight.w800,
        color: Colors.black,
      ),
    );
    comp.addAll([
      TextComponent(
        text: "ORGANIC",
        anchor: Anchor.center,
        size: Vector2(effectiveSize.x * 0.4, effectiveSize.y * 0.4),
        position: Vector2(effectiveSize.x * 0.5, effectiveSize.y * 0.45),
        textRenderer: textPaint,
      ),
      TextComponent(
        text: "PEROXIDE",
        anchor: Anchor.center,
        size: Vector2(effectiveSize.x * 0.4, effectiveSize.y * 0.4),
        position: Vector2(effectiveSize.x * 0.5, effectiveSize.y * 0.58),
        textRenderer: textPaint,
      ),
    ]);
  }
  return comp;
}

Future<PositionComponent> buildRadioactive(int type, {Vector2? size}) async {
  final effectiveSize = size ?? _defaultSize;
  final comp = await buildBaseLabel(svgPath: "labels/Radioactive/base.svg", size: effectiveSize);
  final radSvg = await Svg.load("labels/Radioactive/rad-symbol.svg");
  final radComp = SvgComponent(
    size: Vector2(effectiveSize.x * 0.25, effectiveSize.y * 0.25),
    anchor: Anchor.center,
    position: Vector2(effectiveSize.x * 0.5, effectiveSize.y * 0.25),
    svg: radSvg,
  );
  final bottomComponent = TextComponent(
    text: "7",
    anchor: Anchor.center,
    size: Vector2(effectiveSize.x * 0.4, effectiveSize.y * 0.4),
    position: Vector2(effectiveSize.x * 0.5, effectiveSize.y * 0.88),
    textRenderer: TextPaint(
      style: TextStyle(
        fontSize: effectiveSize.x * 0.12,
        fontWeight: FontWeight.w800,
        color: Colors.black,
      ),
    ),
  );
  comp.addAll([
    radComp,
    bottomComponent,
  ]);
  if (type == 0) {
    final centerText = TextComponent(
      text: "RADIOACTIVE",
      anchor: Anchor.center,
      size: Vector2(effectiveSize.x * 0.4, effectiveSize.y * 0.4),
      position: Vector2(effectiveSize.x * 0.5, effectiveSize.y * 0.49),
      textRenderer: TextPaint(
        style: TextStyle(
          fontSize: effectiveSize.x * 0.12,
          fontWeight: FontWeight.w800,
          color: Colors.black,
        ),
      ),
    );
    comp.add(centerText);
  }
  return comp;
}

Future<PositionComponent> buildSpecialty(int type, {Vector2? size}) async {
  final effectiveSize = size ?? _defaultSize;
  String path = "";
  switch (type) {
    case 0:
      path = "labels/Specialty/biohazard.svg";
      break;
    case 1:
      path = "labels/Specialty/radioactive.svg";
      break;
    case 2:
      path = "labels/Specialty/red-stripe.svg";
      break;
    case 3:
      path = "labels/Specialty/red-stripe-black-border.svg";
      break;
    case 4:
      path = "labels/Specialty/white-box.svg";
      break;
    case 5:
      path = "labels/Specialty/marine.svg";
      break;
  }
  final comp = await buildBaseLabel(svgPath: path, size: effectiveSize);
  if (type == 2) {
    comp.add(TextComponent(
      text: "DANGEROUS",
      anchor: Anchor.center,
      size: Vector2(effectiveSize.x * 0.4, effectiveSize.y * 0.4),
      position: Vector2(effectiveSize.x * 0.5, effectiveSize.y * 0.5),
      textRenderer: TextPaint(
        style: TextStyle(
          fontSize: effectiveSize.x * 0.12,
          fontWeight: FontWeight.w800,
          color: Colors.black,
        ),
      ),
    ));
  }
  if (type == 3) {
    comp.add(TextComponent(
      text: "DANGER",
      anchor: Anchor.center,
      size: Vector2(effectiveSize.x * 0.4, effectiveSize.y * 0.4),
      position: Vector2(effectiveSize.x * 0.5, effectiveSize.y * 0.5),
      textRenderer: TextPaint(
        style: TextStyle(
          fontSize: effectiveSize.x * 0.12,
          fontWeight: FontWeight.w800,
          color: Colors.black,
        ),
      ),
    ));
  }
  if (type == 4) {
    comp.addAll([
      TextComponent(
        text: "INHALATION",
        anchor: Anchor.center,
        size: Vector2(effectiveSize.x * 0.4, effectiveSize.y * 0.4),
        position: Vector2(effectiveSize.x * 0.5, effectiveSize.y * 0.4),
        textRenderer: TextPaint(
          style: TextStyle(
            fontSize: effectiveSize.x * 0.14,
            fontWeight: FontWeight.w800,
            color: Colors.black,
          ),
        ),
      ),
      TextComponent(
        text: "HAZARD",
        anchor: Anchor.center,
        size: Vector2(effectiveSize.x * 0.4, effectiveSize.y * 0.4),
        position: Vector2(effectiveSize.x * 0.5, effectiveSize.y * 0.6),
        textRenderer: TextPaint(
          style: TextStyle(
            fontSize: effectiveSize.x * 0.14,
            fontWeight: FontWeight.w800,
            color: Colors.black,
          ),
        ),
      ),
    ]);
  }
  if (type == 5) {
    comp.add(TextComponent(
      text: "MARINE POLLUTANT",
      anchor: Anchor.center,
      size: Vector2(effectiveSize.x * 0.4, effectiveSize.y * 0.4),
      position: Vector2(effectiveSize.x * 0.5, effectiveSize.y * 0.7),
      textRenderer: TextPaint(
        style: TextStyle(
          fontSize: effectiveSize.x * 0.076,
          fontWeight: FontWeight.w800,
          color: Colors.black,
        ),
      ),
    ));
  }
  return comp;
}

Future<PositionComponent> buildSubstances(int type, {Vector2? size}) async {
  final effectiveSize = size ?? _defaultSize;
  if (type < 0 || type > 1) {
    throw Exception("Unknown substances type: $type");
  }
  final comp = await buildBaseLabel(
      svgPath: type == 0 ? "labels/Substances/black-with-bottom.svg" : "labels/Substances/black-no-bottom.svg", size: effectiveSize);
  final bottomText = TextComponent(
    text: "9",
    anchor: Anchor.center,
    size: Vector2(effectiveSize.x * 0.4, effectiveSize.y * 0.4),
    position: Vector2(effectiveSize.x * 0.5, effectiveSize.y * 0.84),
    textRenderer: TextPaint(
      style: TextStyle(
        fontSize: effectiveSize.x * 0.12,
        fontWeight: FontWeight.w800,
        color: Colors.black,
        decoration: TextDecoration.underline,
      ),
    ),
  );
  comp.add(bottomText);
  return comp;
}

Future<PositionComponent> buildToxCorGas(int type, {int materialNumber = 0, Vector2? size}) async {
  final effectiveSize = size ?? _defaultSize;
  final comp = await buildBaseLabel(svgPath: "labels/ToxCorGas/white-back.svg", size: effectiveSize);
  final skullSvg = await Svg.load("labels/ToxCorGas/white-skull.svg");
  final topComponent = SvgComponent(
    size: Vector2(effectiveSize.x * 0.175, effectiveSize.y * 0.175),
    position: Vector2(effectiveSize.x * 0.41, effectiveSize.y * 0.11),
    svg: skullSvg,
  );
  final bottomComponent = TextComponent(
    text: "2",
    anchor: Anchor.center,
    size: Vector2(effectiveSize.x * 0.4, effectiveSize.y * 0.4),
    position: Vector2(effectiveSize.x * 0.5, effectiveSize.y * 0.84),
    textRenderer: TextPaint(
      style: TextStyle(
        fontSize: effectiveSize.x * 0.12,
        fontWeight: FontWeight.w800,
        color: Colors.black,
      ),
    ),
  );
  if (type == 2 || type == 5) {
    final blackDiamondSvg = await Svg.load("labels/ToxCorGas/black-diamond.svg");
    final blackDiamondComponent = SvgComponent(
      size: Vector2(effectiveSize.x * 0.3, effectiveSize.y * 0.3),
      position: Vector2(effectiveSize.x * 0.35, effectiveSize.y * 0.07),
      svg: blackDiamondSvg,
    );
    comp.add(blackDiamondComponent);
  }
  if (type == 0) {
    comp.addAll([
      TextComponent(
        text: "POISON",
        anchor: Anchor.center,
        size: Vector2(effectiveSize.x * 0.4, effectiveSize.y * 0.4),
        position: Vector2(effectiveSize.x * 0.5, effectiveSize.y * 0.45),
        textRenderer: TextPaint(
          style: TextStyle(
            fontSize: effectiveSize.x * 0.12,
            fontWeight: FontWeight.w800,
            color: Colors.black,
          ),
        ),
      ),
      TextComponent(
        text: "GAS",
        anchor: Anchor.center,
        size: Vector2(effectiveSize.x * 0.4, effectiveSize.y * 0.4),
        position: Vector2(effectiveSize.x * 0.5, effectiveSize.y * 0.55),
        textRenderer: TextPaint(
          style: TextStyle(
            fontSize: effectiveSize.x * 0.12,
            fontWeight: FontWeight.w800,
            color: Colors.black,
          ),
        ),
      )
    ]);
  }
  if (type == 1) {
    comp.addAll([
      TextComponent(
        text: "TOXIC",
        anchor: Anchor.center,
        size: Vector2(effectiveSize.x * 0.4, effectiveSize.y * 0.4),
        position: Vector2(effectiveSize.x * 0.5, effectiveSize.y * 0.45),
        textRenderer: TextPaint(
          style: TextStyle(
            fontSize: effectiveSize.x * 0.12,
            fontWeight: FontWeight.w800,
            color: Colors.black,
          ),
        ),
      ),
      TextComponent(
        text: "GAS",
        anchor: Anchor.center,
        size: Vector2(effectiveSize.x * 0.4, effectiveSize.y * 0.4),
        position: Vector2(effectiveSize.x * 0.5, effectiveSize.y * 0.55),
        textRenderer: TextPaint(
          style: TextStyle(
            fontSize: effectiveSize.x * 0.12,
            fontWeight: FontWeight.w800,
            color: Colors.black,
          ),
        ),
      )
    ]);
  }
  if (type == 6) {
    comp.addAll([
      TextComponent(
        text: "INHALATION",
        anchor: Anchor.center,
        size: Vector2(effectiveSize.x * 0.4, effectiveSize.y * 0.4),
        position: Vector2(effectiveSize.x * 0.5, effectiveSize.y * 0.5),
        textRenderer: TextPaint(
          style: TextStyle(
            fontSize: effectiveSize.x * 0.12,
            fontWeight: FontWeight.w800,
            color: Colors.black,
          ),
        ),
      ),
      TextComponent(
        text: "HAZARD",
        anchor: Anchor.center,
        size: Vector2(effectiveSize.x * 0.4, effectiveSize.y * 0.4),
        position: Vector2(effectiveSize.x * 0.5, effectiveSize.y * 0.6),
        textRenderer: TextPaint(
          style: TextStyle(
            fontSize: effectiveSize.x * 0.11,
            fontWeight: FontWeight.w800,
            color: Colors.black,
          ),
        ),
      )
    ]);
  }
  if (type == 2 || type == 3) {
    final textSize = Vector2(effectiveSize.x * 0.45, effectiveSize.y * 0.19);
    final middleComp = TextComponent(
      text: materialNumber.toString().padRight(4, "0"),
      anchor: Anchor.center,
      size: textSize,
      position: Vector2(effectiveSize.x * 0.5, effectiveSize.y * 0.52),
      children: [
        RectangleComponent.relative(
          Vector2.all(1),
          parentSize: Vector2(textSize.x, textSize.y),
          anchor: Anchor.topLeft,
          position: Vector2(-1, 0),
          paint: Paint()
            ..color = Colors.black
            ..strokeWidth = 2
            ..style = PaintingStyle.stroke,
        )
      ],
      textRenderer: TextPaint(
        style: TextStyle(
          fontSize: effectiveSize.x * 0.15,
          fontWeight: FontWeight.bold,
          color: Colors.black,
          backgroundColor: Colors.white,
        ),
      ),
    );
    comp.add(middleComp);
  }
  comp.addAll([topComponent, bottomComponent]);
  return comp;
}

Future<PositionComponent> buildToxCorSubst(int type, {Vector2? size}) async {
  final effectiveSize = size ?? _defaultSize;
  final comp = await buildBaseLabel(svgPath: "labels/ToxCorGas/white-back.svg", size: effectiveSize);
  final skullSvg = await Svg.load("labels/ToxCorGas/white-skull.svg");
  final topComponent = SvgComponent(
    size: Vector2(effectiveSize.x * 0.175, effectiveSize.y * 0.175),
    position: Vector2(effectiveSize.x * 0.41, effectiveSize.y * 0.11),
    svg: skullSvg,
  );
  final bottomComponent = TextComponent(
    text: "6",
    anchor: Anchor.center,
    size: Vector2(effectiveSize.x * 0.4, effectiveSize.y * 0.4),
    position: Vector2(effectiveSize.x * 0.5, effectiveSize.y * 0.84),
    textRenderer: TextPaint(
      style: TextStyle(
        fontSize: effectiveSize.x * 0.12,
        fontWeight: FontWeight.w800,
        color: Colors.black,
      ),
    ),
  );
  if (type == 3 || type == 4) {
    final blackDiamondSvg = await Svg.load("labels/ToxCorGas/black-diamond.svg");
    final blackDiamondComponent = SvgComponent(
      size: Vector2(effectiveSize.x * 0.3, effectiveSize.y * 0.3),
      position: Vector2(effectiveSize.x * 0.35, effectiveSize.y * 0.07),
      svg: blackDiamondSvg,
    );
    comp.add(blackDiamondComponent);
  }
  if (type == 0) {
    comp.add(TextComponent(
      text: "POISON",
      anchor: Anchor.center,
      size: Vector2(effectiveSize.x * 0.4, effectiveSize.y * 0.4),
      position: Vector2(effectiveSize.x * 0.5, effectiveSize.y * 0.5),
      textRenderer: TextPaint(
        style: TextStyle(
          fontSize: effectiveSize.x * 0.16,
          fontWeight: FontWeight.w800,
          color: Colors.black,
        ),
      ),
    ));
  }
  if (type == 1) {
    comp.add(
      TextComponent(
        text: "TOXIC",
        anchor: Anchor.center,
        size: Vector2(effectiveSize.x * 0.4, effectiveSize.y * 0.4),
        position: Vector2(effectiveSize.x * 0.5, effectiveSize.y * 0.5),
        textRenderer: TextPaint(
          style: TextStyle(
            fontSize: effectiveSize.x * 0.16,
            fontWeight: FontWeight.w800,
            color: Colors.black,
          ),
        ),
      ),
    );
  }
  if (type == 4) {
    comp.addAll([
      TextComponent(
        text: "INHALATION",
        anchor: Anchor.center,
        size: Vector2(effectiveSize.x * 0.4, effectiveSize.y * 0.4),
        position: Vector2(effectiveSize.x * 0.5, effectiveSize.y * 0.5),
        textRenderer: TextPaint(
          style: TextStyle(
            fontSize: effectiveSize.x * 0.12,
            fontWeight: FontWeight.w800,
            color: Colors.black,
          ),
        ),
      ),
      TextComponent(
        text: "HAZARD",
        anchor: Anchor.center,
        size: Vector2(effectiveSize.x * 0.4, effectiveSize.y * 0.4),
        position: Vector2(effectiveSize.x * 0.5, effectiveSize.y * 0.6),
        textRenderer: TextPaint(
          style: TextStyle(
            fontSize: effectiveSize.x * 0.11,
            fontWeight: FontWeight.w800,
            color: Colors.black,
          ),
        ),
      )
    ]);
  }
  if (type == 5) {
    comp.addAll([
      TextComponent(
        text: "PG",
        anchor: Anchor.center,
        size: Vector2(effectiveSize.x * 0.4, effectiveSize.y * 0.4),
        position: Vector2(effectiveSize.x * 0.4, effectiveSize.y * 0.5),
        textRenderer: TextPaint(
          style: TextStyle(
            fontSize: effectiveSize.x * 0.16,
            fontWeight: FontWeight.w800,
            color: Colors.black,
          ),
        ),
      ),
      TextComponent(
        text: "III",
        anchor: Anchor.center,
        size: Vector2(effectiveSize.x * 0.4, effectiveSize.y * 0.4),
        position: Vector2(effectiveSize.x * 0.65, effectiveSize.y * 0.5),
        textRenderer: TextPaint(
          style: TextStyle(
            fontSize: effectiveSize.x * 0.16,
            fontWeight: FontWeight.normal,
            fontStyle: FontStyle.normal,
            color: Colors.black,
          ),
        ),
      ),
    ]);
  }
  comp.addAll([topComponent, bottomComponent]);
  return comp;
}
