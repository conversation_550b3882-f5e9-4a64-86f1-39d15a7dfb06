import 'dart:ui';

import 'package:flame/image_composition.dart';
import 'package:flutter/material.dart';
import 'package:simsushare_player/models/SimObjects.dart';
import 'package:simsushare_player/utils/web_parser.dart';

SimPerson parsePerson(String type, String text, Map<String, String> props) {
  final data = text.split(",");
  return SimPerson(
    type: type,
    posture: int.tryParse(data[4]) ?? 0,
    id: props["id"] ?? "",
    name: props["name"] ?? props["id"] ?? "",
    x: double.tryParse(props["x"] ?? "") ?? 150,
    y: double.tryParse(props["y"] ?? "") ?? 150,
    widthScale: double.tryParse(props["scaleX"] ?? "") ?? 1.0,
    heightScale: double.tryParse(props["scaleY"] ?? "") ?? 1.0,
    rotation: double.tryParse(props["rotation"] ?? "") ?? 0.0,
    hideOnStart: props["hideOnStart"] == "true",
    syncVariable: data[9] == "0" ? null : data[9],
  )
    ..filterColor = props["color"] != null
        ? getColorFromValue(int.parse(props["color"] as String), withAlpha: true)
        : (double.tryParse(data[3]) != null ? HSLColor.fromAHSL(0.7, 0, 0, (double.parse(data[3]) + 256.0) / 512).toColor() : Colors.transparent)
    // : Colors.black
    //     .withAlpha(0)
    //     .brighten(clampDouble((double.tryParse(data[3]) ?? 0.0) / 255, 0, 1))
    //     .darken(clampDouble((double.tryParse(data[3]) ?? 0.0) / 255 * -1, 0, 1))
    ..widthScale = double.tryParse(props["scaleX"] ?? "") ?? 1.0
    ..heightScale = double.tryParse(props["scaleY"] ?? "") ?? 1.0
    ..fadeInWhen = double.tryParse(data[5]) ?? 0.0
    ..fadeInDuration = double.tryParse(data[6]) ?? 0.0
    ..mirrorY = (data.length > 12 ? data[12] : "") == "true"
    ..mirrorX = (data.length > 13 ? data[13] : "") == "true"
    // ..widthScale = double.tryParse((data.length > 14) ? data[14] : "") ?? 1.0
    ..blur = double.tryParse((data.length > 14) ? data[14] : "") ?? 0.0
    ..fadeOut = (data.length > 15 ? data[15] : "") == "true"
    ..fadeOutWhen = double.tryParse((data.length > 16) ? data[16] : "") ?? 0.0
    ..fadeOutDuration = double.tryParse((data.length > 17) ? data[17] : "") ?? 0.0
    ..clickToToggle = (data.length > 7 ? data[7] : "") == "true"
    ..priority = int.tryParse(props["priority"] ?? "0") ?? 0
    ..movable = props["movable"] == "true";
}

String getPersonPosture(String type, Map<String, String> props) {
  final data = type.split(",");
  return data[4];
}

String getPersonText(SimPerson person) {
  // in,0,0,212,1,8,2,true,80,FFSync1,0,false,true,1.448,20,true,18,3
  return "in,0,0,${HSLColor.fromColor(person.filterColor).lightness * 512 - 256},${person.posture},${person.fadeInWhen},${person.fadeInDuration},${person.clickToToggle.toString()},0,${person.syncVariable ?? "0"},0,${person.mirrorY},${person.mirrorX},${person.heightScale},${person.blur},${person.fadeOut || person.fadeOutWhen > 0},${person.fadeOutWhen},${person.fadeOutDuration}";
}

Map<String, String> getPersonProps(SimLabel label) {
  return {
    "id": label.id,
    "name": label.name,
    "x": label.x.toString(),
    "y": label.y.toString(),
    "scaleX": label.width.toString(),
    "scaleY": label.height.toString(),
    "color": label.filterColor.value.toString(),
    "priority": label.priority.toString(),
  };
}
