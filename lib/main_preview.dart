import 'dart:io';
import 'dart:math';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:multi_window/multi_window.dart';
import 'package:desktop_window/desktop_window.dart';
import 'package:simsushare_player/controllers/ClipboardController.dart';
import 'package:simsushare_player/controllers/SimController.dart';
import 'package:simsushare_player/controllers/UserController.dart';
import 'package:simsushare_player/pages/Preview.dart';

import 'package:simsushare_player/utils/constants.dart';

import 'package:scaled_app/scaled_app.dart';
import 'package:window_manager/window_manager.dart';

void main(List<String> args) async {
  if (!kIsWeb) {
    if (Platform.isAndroid || Platform.isIOS) {
      WidgetsFlutterBinding.ensureInitialized();
      SystemChrome.setPreferredOrientations([DeviceOrientation.portraitUp]);
      SystemChrome.setEnabledSystemUIMode(SystemUiMode.manual, overlays: []);
    } else if (Platform.isWindows || Platform.isLinux || Platform.isMacOS) {
      ScaledWidgetsFlutterBinding.ensureInitialized(
        scaleFactor: (deviceSize) => min(deviceSize.width / 1200, deviceSize.height / 800),
      );
      await windowManager.ensureInitialized();

      await Future.wait([
        // DesktopWindow.setMinWindowSize(const Size(1200, 800)),
        // DesktopWindow.setMaxWindowSize(Size.infinite),
        DesktopWindow.setWindowSize(const Size(1200, 800)),
      ]);
      WindowManager.instance.setAspectRatio(12 / 8);
      MultiWindow.init(args);
    }
  }
  await GetStorage.init();
  Get.put(SimController());
  Get.put(UserController());
  Get.put(ClipboardController());
  runApp(
    GetMaterialApp(
      title: 'SimsUShare Player',
      theme: ThemeData(
        scaffoldBackgroundColor: Colors.black,
        inputDecorationTheme: const InputDecorationTheme(
          labelStyle: TextStyle(color: white60),
          hintStyle: TextStyle(color: white60),
          enabledBorder: inputOutlinedBorder,
          focusedBorder: inputOutlinedBorder,
          border: InputBorder.none,
        ),
        canvasColor: mainBackgrounds,
        navigationRailTheme: const NavigationRailThemeData(
          backgroundColor: mainBackgrounds,
          selectedLabelTextStyle: selectedDrawerItemTextStyle,
          unselectedLabelTextStyle: unselectedDrawerItemTextStyle,
          selectedIconTheme: IconThemeData(color: yellow),
          unselectedIconTheme: IconThemeData(color: white60),
        ),
        useMaterial3: false,
        tabBarTheme: const TabBarTheme(
          labelColor: yellow,
          unselectedLabelColor: white60,
          indicatorColor: yellow,
        ),
      ),
      initialRoute: "/player",
      getPages: [
        GetPage(name: "/player", page: () => Preview()),
      ],
    ),
  );
}
