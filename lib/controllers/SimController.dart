import 'dart:async';

import 'package:args/args.dart';
import 'package:easy_debounce/easy_debounce.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:simsushare_player/models/Mask.dart';
import 'package:simsushare_player/models/SimObjects.dart';
import 'package:simsushare_player/models/Simulation.dart';

enum SimObjectType {
  sprite,
  image,
  text,
  shape,
  locationJumper,
  audio,
  label,
  container,
  person,
  timer,
  mask,
}

class SimController extends GetxController {
  /// used in web version to store scenarios that are used in memory
  final downloadedSims = <String, Scenario>{}.obs; // ScenarioUrl, ScenarioData

  Rxn<Scenario> currentSim = Rxn<Scenario>();
  Rx<int> currentLocation = Rx<int>(-1);
  Rx<int> currentState = Rx<int>(0);
  Rx<int> selectedSimObjectIndex = Rx<int>(-1);
  final newMask = Rxn<Mask>(null);
  final selectedType = Rxn<SimObjectType>(null);
  final isPlaying = false.obs;
  final showSimTime = false.obs;
  final showLocationTime = false.obs;
  final hideNavigator = true.obs;
  // Rx<SimObjectType> = Rx<SimObjectType>();
  ValueNotifier backgroundNotifier = ValueNotifier(0);
  // Stream<dynamic> signalStream = Stream;
  StreamController<dynamic> signalStream = StreamController<dynamic>.broadcast();
  final saved = true.obs;
  final autosave = true.obs;
  final undoRedoTriggered = false.obs;
  final undoStack = <Scenario>[].obs;
  final redoStack = <Scenario>[].obs;

  @override
  void onInit() {
    final storage = GetStorage();
    print("Initializing Sim controller with current sim $currentSim");

    ever(currentSim, (value) {
      // print("Current Sim updated $value");
      // storage.write("currentSim", value);
      if (currentSim.value != null && !currentSim.value!.inPlayMode) {
        if (autosave.value && currentSim.value != null && !saved.value) {
          signalStream.add("autosave");
          saved.value = true;
          return;
        }
      }
      saved.value = false;
      saved.refresh();
    });
    ever(currentLocation, (value) {
      print("Current Location updated $value");
      // storage.write("currentLocation", value);
    });
    ever(currentState, (value) {
      print("Current State updated $value");
      // storage.write("currentState", value);
    });
    ever(saved, (value) => print("***Saved*** updated $value"));
    ever(autosave, (value) {
      final prefs = GetStorage();
      prefs.write("autosave", value);
    });
    super.onInit();
    final parser = ArgParser();
    parser.addFlag("reloadable");
    final flags = parser.parse(['--reloadable']);
    print(flags.arguments);
    print("${flags['reloadable'].runtimeType} ${flags['reloadable']}");
    if (flags['reloadable']) {
      print("Reloadable");
      if (storage.read("currentSim") != null) {
        currentSim.value = storage.read("currentSim");
      }
      if (storage.read("currentLocation") != null) {
        currentLocation.value = storage.read("currentLocation");
      }
      if (storage.read("currentState") != null) {
        currentState.value = storage.read("currentState");
      }
    }
    // autosave.value = storage.read("autosave") ?? false;
    autosave.value = storage.read("autosave") ?? true;
  }

  play() {
    isPlaying.value = true;
    signalStream.add("play");
  }

  pause() {
    isPlaying.value = false;
    signalStream.add("pause");
  }

  restart() {
    currentLocation.value = 0;
    currentState.value = 0;
    isPlaying.value = true;
    signalStream.add("restart");
  }

  reset() {
    currentSim.value = null;
    currentLocation.value = -1;
    currentState.value = 0;
    selectedSimObjectIndex.value = -1;
    newMask.value = null;
    selectedType.value = null;
    isPlaying.value = false;
    undoStack.clear();
    redoStack.clear();
  }

  updateCurrentSim(Scenario s) {
    currentSim.value = s;
  }

  void addToUndoStack(Scenario? s) {
    EasyDebounce.debounce("undo/redo/addToUndo", const Duration(milliseconds: 250), () {
      if (currentSim.value == null) {
        undoStack.clear();
        redoStack.clear();
        return;
      }
      // if (undoStack.isNotEmpty && currentSim.value == undoStack.last) return;
      if (s == null) {
        undoStack.add(currentSim.value!);
        return;
      }
      if (undoStack.isEmpty) {
        undoStack.add(currentSim.value!);
        return;
      }
      if (undoStack.last.equalTo(s)) {
        print("No change detected to add to undo stack");
        return;
      }
      print("Adding to undo stack");
      undoStack.add(s);

      Future.delayed(const Duration(milliseconds: 50), () {
        int index = 0;
        undoStack.value.forEach((sim) {
          print("Undo stack@$index: ${sim.toString()}\n\n\n");
          index++;
        });
      });
    });
  }

  void undo() {
    EasyDebounce.debounce("undo/redo/addToUndo", const Duration(milliseconds: 250), () {
      print("Undo triggered");
      if (undoStack.isEmpty) {
        return;
      }
      // undoRedoTriggered.value = true;
      redoStack.add(currentSim.value!.copy(clone: true));
      // currentSim.value = undoStack.removeLast();
      undoStack.removeLast();
      print("Setting undo stack last item ${undoStack.last}");
      currentSim.value = undoStack.removeLast();
      currentSim.refresh();
      signalStream.add("stack-update");
      // Future.delayed(const Duration(milliseconds: 50), () => undoRedoTriggered.value = false);
    });
  }

  void redo() {
    EasyDebounce.debounce("undo/redo/addToUndo", const Duration(milliseconds: 250), () {
      print("Redo triggered");
      if (redoStack.isEmpty) {
        return;
      }
      // undoRedoTriggered.value = true;
      undoStack.add(currentSim.value!);
      currentSim.value = redoStack.removeLast();
      currentSim.refresh();
      Future.delayed(const Duration(milliseconds: 150), () {
        signalStream.add("stack-update");
      });
      // Future.delayed(const Duration(milliseconds: 50), () => undoRedoTriggered.value = false);
    });
  }

  SimObject? getCurrentSelectedObject() {
    if (selectedType.value == null || selectedSimObjectIndex.value == -1) {
      return null;
    }
    switch (selectedType.value) {
      case SimObjectType.sprite:
        return currentSim.value!.locations[currentLocation.value].sprites.isEmpty
            ? null
            : currentSim.value!.locations[currentLocation.value].sprites[selectedSimObjectIndex.value];
      case SimObjectType.image:
        return currentSim.value!.locations[currentLocation.value].image.isEmpty
            ? null
            : currentSim.value!.locations[currentLocation.value].images[selectedSimObjectIndex.value];
      case SimObjectType.text:
        return currentSim.value!.locations[currentLocation.value].texts.isEmpty
            ? null
            : currentSim.value!.locations[currentLocation.value].texts[selectedSimObjectIndex.value];
      case SimObjectType.shape:
        return currentSim.value!.locations[currentLocation.value].shapes.isEmpty
            ? null
            : currentSim.value!.locations[currentLocation.value].shapes[selectedSimObjectIndex.value];
      case SimObjectType.locationJumper:
        return currentSim.value!.locations[currentLocation.value].jumpers.isEmpty
            ? null
            : currentSim.value!.locations[currentLocation.value].jumpers[selectedSimObjectIndex.value];
      case SimObjectType.audio:
        return currentSim.value!.locations[currentLocation.value].sounds.isEmpty
            ? null
            : currentSim.value!.locations[currentLocation.value].sounds[selectedSimObjectIndex.value];
      case SimObjectType.label:
        return currentSim.value!.locations[currentLocation.value].labels.isEmpty
            ? null
            : currentSim.value!.locations[currentLocation.value].labels[selectedSimObjectIndex.value];
      case SimObjectType.container:
        return currentSim.value!.locations[currentLocation.value].containers.isEmpty
            ? null
            : currentSim.value!.locations[currentLocation.value].containers[selectedSimObjectIndex.value];
      case SimObjectType.person:
        return currentSim.value!.locations[currentLocation.value].people.isEmpty
            ? null
            : currentSim.value!.locations[currentLocation.value].people[selectedSimObjectIndex.value];
      case SimObjectType.timer:
        return currentSim.value!.locations[currentLocation.value].timers.isEmpty
            ? null
            : currentSim.value!.locations[currentLocation.value].timers[selectedSimObjectIndex.value];
      default:
        return null;
    }
  }

  Mask getCurrentSelectedMask() {
    return currentSim.value!.masks[selectedSimObjectIndex.value];
  }

  void addSimObject(SimObject simObj) {
    switch (simObj.runtimeType) {
      case SimSprite:
        currentSim.value!.locations[currentLocation.value].sprites.add(simObj as SimSprite);
        return;
      case SimImage:
        currentSim.value!.locations[currentLocation.value].images.add(simObj as SimImage);
        return;
      case SimText:
        currentSim.value!.locations[currentLocation.value].texts.add(simObj as SimText);
        return;
      case SimShape:
        currentSim.value!.locations[currentLocation.value].shapes.add(simObj as SimShape);
        return;
      case SimLocationJumper:
        currentSim.value!.locations[currentLocation.value].jumpers.add(simObj as SimLocationJumper);
        return;
      case SimSound:
        currentSim.value!.locations[currentLocation.value].sounds.add(simObj as SimSound);
        return;
      case SimLabel:
        currentSim.value!.locations[currentLocation.value].labels.add(simObj as SimLabel);
        return;
      case SimContainer:
        currentSim.value!.locations[currentLocation.value].containers.add(simObj as SimContainer);
        return;
      case SimPerson:
        currentSim.value!.locations[currentLocation.value].people.add(simObj as SimPerson);
        return;
      case SimTimer:
        currentSim.value!.locations[currentLocation.value].timers.add(simObj as SimTimer);
        return;
      default:
        print("No object type selected");
        return;
    }
  }

  removeSimObject(SimObject simObj) {
    switch (simObj.runtimeType) {
      case SimSprite:
        currentSim.value!.locations[currentLocation.value].sprites.remove(simObj);
        return;
      case SimImage:
        currentSim.value!.locations[currentLocation.value].images.remove(simObj);
        return;
      case SimText:
        currentSim.value!.locations[currentLocation.value].texts.remove(simObj);
        return;
      case SimShape:
        currentSim.value!.locations[currentLocation.value].shapes.remove(simObj);
        return;
      case SimLocationJumper:
        currentSim.value!.locations[currentLocation.value].jumpers.remove(simObj);
        return;
      case SimSound:
        currentSim.value!.locations[currentLocation.value].sounds.remove(simObj);
        return;
      case SimTimer:
        currentSim.value!.locations[currentLocation.value].timers.remove(simObj);
        return;
      default:
        print("No object type selected");
        return;
    }
  }

  void jumpToLocation(String locationId) {
    final locIndex = currentSim.value!.locations.indexWhere((element) => element.id == locationId);
    if (locIndex != -1) {
      currentLocation.value = locIndex;
      final stateIndex = currentSim.value!.states.indexWhere((state) => state.id == currentSim.value!.locations[locIndex].state);
      currentState.value = stateIndex;
    }
  }

  List<SimObject> getAllLocationObjects(int index) {
    final loc = currentSim.value!.locations[index];
    final objects = <SimObject>[];
    objects.addAll(loc.sprites);
    objects.addAll(loc.images);
    objects.addAll(loc.texts);
    objects.addAll(loc.shapes);
    objects.addAll(loc.jumpers);
    objects.addAll(loc.sounds);
    objects.addAll(loc.labels);
    objects.addAll(loc.containers);
    objects.addAll(loc.people);
    objects.addAll(loc.timers);
    return objects;
  }

  int getLargestObjectTypeInLocation(int locationIndex, String objectType, {String? overridePrefix}) {
    final loc = currentSim.value!.locations[locationIndex];
    switch (objectType) {
      case "sprite":
        return loc.sprites.fold<int>(
          0,
          (previousValue, element) {
            // final suffix = element.id.split(overridePrefix ?? "Sprite_").last;
            final suffix = element.name.split(overridePrefix ?? "Sprite_").last;
            final suffixInt = int.tryParse(suffix.startsWith("_") ? suffix.substring(1) : suffix) ?? 0;
            return suffixInt > previousValue ? suffixInt : previousValue;
          },
        );
      case "image":
        return loc.images.fold<int>(
          0,
          (previousValue, element) {
            final suffix = element.id.split(overridePrefix ?? "Image_").last;
            final suffixInt = int.tryParse(suffix) ?? 0;
            return suffixInt > previousValue ? suffixInt : previousValue;
          },
        );
      case "text":
        return loc.texts.fold<int>(
          0,
          (previousValue, element) {
            final suffix = element.id.split(overridePrefix ?? "Text_").last;
            final suffixInt = int.tryParse(suffix) ?? 0;
            return suffixInt > previousValue ? suffixInt : previousValue;
          },
        );
      case "shape":
        return loc.shapes.fold<int>(
          0,
          (previousValue, element) {
            final suffix = element.id.split(overridePrefix ?? "Shape_").last;
            final suffixInt = int.tryParse(suffix) ?? 0;
            return suffixInt > previousValue ? suffixInt : previousValue;
          },
        );
      case "jumper":
        return loc.jumpers.fold<int>(
          0,
          (previousValue, element) {
            final suffix = element.id.split(overridePrefix ?? "Jumper_").last;
            final suffixInt = int.tryParse(suffix) ?? 0;
            return suffixInt > previousValue ? suffixInt : previousValue;
          },
        );
      case "sound":
        return loc.sounds.fold<int>(
          0,
          (previousValue, element) {
            final suffix = element.id.split(overridePrefix ?? "Sound_").last;
            final suffixInt = int.tryParse(suffix) ?? 0;
            return suffixInt > previousValue ? suffixInt : previousValue;
          },
        );
      case "label":
        return loc.labels.fold<int>(
          0,
          (previousValue, element) {
            final suffix = element.id.split(overridePrefix ?? "Label_").last;
            final suffixInt = int.tryParse(suffix) ?? 0;
            return suffixInt > previousValue ? suffixInt : previousValue;
          },
        );
      case "container":
        return loc.containers.fold<int>(
          0,
          (previousValue, element) {
            final suffix = element.id.split(overridePrefix ?? "Container_").last;
            final suffixInt = int.tryParse(suffix) ?? 0;
            return suffixInt > previousValue ? suffixInt : previousValue;
          },
        );
      case "person":
        return loc.people.fold<int>(
          0,
          (previousValue, element) {
            final suffix = element.id.split(overridePrefix ?? "Person_").last;
            final suffixInt = int.tryParse(suffix) ?? 0;
            return suffixInt > previousValue ? suffixInt : previousValue;
          },
        );
      case "timer":
        return loc.timers.fold<int>(
          0,
          (previousValue, element) {
            final suffix = element.id.split(overridePrefix ?? "Timer_").last;
            final suffixInt = int.tryParse(suffix) ?? 0;
            return suffixInt > previousValue ? suffixInt : previousValue;
          },
        );
      case "mask":
        return currentSim.value!.masks.where((element) => element.locationId == loc.id).fold<int>(
          0,
          (previousValue, element) {
            final suffix = element.name.split(overridePrefix ?? "Mask_").last;
            final suffixInt = int.tryParse(suffix) ?? 0;
            return suffixInt > previousValue ? suffixInt : previousValue;
          },
        );
      default:
        return 0;
    }
  }
}

String? getSimObjectType(SimObject obj) {
  if (obj is SimSprite) {
    return "sprite";
  } else if (obj is SimImage) {
    return "image";
  } else if (obj is SimText) {
    return "text";
  } else if (obj is SimShape) {
    return "shape";
  } else if (obj is SimLocationJumper) {
    return "jumper";
  } else if (obj is SimSound) {
    return "sound";
  } else if (obj is SimLabel) {
    return "label";
  } else if (obj is SimContainer) {
    return "container";
  } else if (obj is SimPerson) {
    return "person";
  } else if (obj is SimTimer) {
    return "timer";
  } else if (obj is Mask) {
    return "mask";
  } else {
    return null;
  }
}
