import 'dart:io';

import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:jwt_decoder/jwt_decoder.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:simsushare_player/models/Simulation.dart';
import 'package:simsushare_player/pages/home_views/my_simulations_view/dialogs/registration_dialog.dart';
import 'package:simsushare_player/utils/constants.dart';
import 'package:device_info_plus/device_info_plus.dart';

const registrationRequired = int.fromEnvironment("REGISTRATION_REQUIRED", defaultValue: 0);

class UserController extends GetxController {
  var token = Rxn<String>();
  var user = Rxn<dynamic>();
  final selectedSims = RxMap<String, Scenario>();
  /* 
    keeps track of login tokens for repositories you're connecting to.
    Unlike `token`, this is a map of tokens for each repository while `token` is the token for the base URL (main server)
  */
  final repoToken = RxMap<String, String>();

  final registrationToken = Rxn<String>();
  final registered = RxBool(false);
  final isRegistrationRequired = RxBool(registrationRequired != 0);

  final totalUploads = 0.obs;
  final completedUploads = 0.obs;

  @override
  void onInit() async {
    super.onInit();
    print("User Controller On Init");
    /* ever(token, (value) {
      if (value != null && isRegistrationRequired.value == false) {
        isRegistrationRequired.value = true;
        return;
      }
    }); */
    final prefs = await SharedPreferences.getInstance();
    final storedBaseURL = prefs.getString("baseUrl");
    if (storedBaseURL != null) {
      await reassignBaseUrl(storedBaseURL);
    }
    final storedToken = prefs.getString("token");
    registrationToken.value = prefs.getString("registrationToken");
    final registrationStatus = await checkRegistrationSerial(checkOnly: true);
    print("Registration Status: $registrationStatus");
    if (registrationStatus) {
      registered.value = true;
    } else {
      if (isRegistrationRequired.value) {
        Future.delayed(const Duration(milliseconds: 1500), () => Get.dialog(RegistrationDialog(), barrierDismissible: false));
      }
    }
    if (storedToken != null) {
      print("Stored Token: $storedToken");
      if (JwtDecoder.isExpired(storedToken)) {
        print("Token expired");
        await prefs.remove("token");
        return;
      }
      setToken(storedToken);
      // token.value = storedToken;
      // checkToken();
      // dio.options.headers["Authorization"] = token.value;
      try {
        final response = await dio.get("/users/curuser");
        if (response.statusCode != 200) {
          print("Get current user failed: ${response.statusCode} ${response.data}");
          return;
        }
        print("Current User: ${response.data}");
        user.value = response.data["user"];
      } catch (err) {
        print(err);
        return;
      }
    }
  }

  // Login to a repository and get the token. The function throws an error if the login fails
  Future<String> loginRepo({required String repoUrl, required String username, required String password}) async {
    print("BASE URL: ${(repoUrl.startsWith("http") ? "" : "https://") + repoUrl}");
    final response = await Dio(
      BaseOptions(
        baseUrl: (repoUrl.startsWith("http") ? "" : "https://") + repoUrl + "/api",
        receiveTimeout: const Duration(seconds: 2),
        validateStatus: (status) => status == 200,
      ),
    ).post("/login", data: {
      "user": username,
      "password": password,
    });
    return response.data["token"];
  }

  Future<bool> verifyRepoToken(String repoUrl) async {
    if (!repoToken.containsKey(repoUrl)) {
      return false;
    }
    final response = await Dio(
      BaseOptions(
        baseUrl: (repoUrl.startsWith("http") ? "" : "https://") + repoUrl + "/api",
        receiveTimeout: const Duration(seconds: 2),
        validateStatus: (status) => status == 200,
      ),
    ).get("/valid");
    return response.statusCode == 200;
  }

  void setToken(String newToken) {
    token.value = newToken;
    repoToken[baseurl.replaceAll("https://", "").replaceAll("http://", "")] = token.value!;
    dio.options.headers["Authorization"] = token.value;
    checkToken();
  }

  void logout() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove("token");
    await prefs.remove("baseUrl");
    await prefs.remove("companyId");
    repoToken.clear();
    token.value = null;
    user.value = null;
  }

  void checkToken() {
    if (token.value == null) return;
    if (JwtDecoder.isExpired(token.value!)) {
      logout();
      return;
    }
    // put a timer on the token expiry date to recheck
    final exp = JwtDecoder.getExpirationDate(token.value!);
    final diff = exp.difference(DateTime.now());
    print("Expires in $diff");
    Future.delayed(diff, () {
      checkToken();
    });
  }

  Map<String, dynamic> decodeServerAuthToken(String token) {
    final decoded = JwtDecoder.decode(token);
    return {
      "userId": decoded["_id"],
      "level": decoded["level"],
      "companyId": decoded["company"]["_id"],
      "companyName": decoded["company"]["name"],
    };
  }

  Future<String> getDeviceId() async {
    String deviceId = "";
    final deviceInfo = DeviceInfoPlugin();

    if (Platform.isAndroid) {
      final androidInfo = await deviceInfo.androidInfo;
      deviceId = androidInfo.id;
    } else if (Platform.isIOS) {
      final iosInfo = await deviceInfo.iosInfo;
      deviceId = iosInfo.identifierForVendor!;
    } else if (Platform.isMacOS) {
      final macInfo = await deviceInfo.macOsInfo;
      deviceId = macInfo.model;
    } else if (Platform.isWindows) {
      final windowsInfo = await deviceInfo.windowsInfo;
      deviceId = windowsInfo.computerName;
    } else if (Platform.isLinux) {
      final linuxInfo = await deviceInfo.linuxInfo;
      deviceId = linuxInfo.machineId ?? linuxInfo.id;
    }

    return deviceId;
  }

  Future<bool> checkRegistrationSerial({bool checkOnly = false}) async {
    if (registrationToken.value == null) return false;
    String deviceId = await getDeviceId();

    if (deviceId.isEmpty) {
      print("Device ID is empty");
      return false;
    }

    // TODO: URL needs to be checked
    var response = await dio.get("https://simsushare.com/susplatlic/register.php", queryParameters: {
      "code": registrationToken.value,
      "mac": deviceId,
      "checksub": checkOnly ? 1 : 0, // NOTE: can be used to return the expiry date of the subscription
    });
    if (response.statusCode != 200 || !response.data.toString().startsWith("EXPIRES:")) {
      print("Registration check failed: ${response.statusCode} ${response.data}");
      return false;
      // TODO: need to handle when license is perpetual. It always returns OK instead of EXPIRES
    }
    final expiresAt = response.data.toString().split(":")[1];
    final expirySplit = expiresAt.split("/");
    final day = int.parse(expirySplit[0]);
    final month = int.parse(expirySplit[1]);
    final year = int.parse("20" + expirySplit[2]); // year comes in YY format
    final expiryDate = DateTime(year, month, day);
    if (expiryDate.isBefore(DateTime.now())) {
      print("Subscription expired at $expiryDate");
      registered.value = false;
      return false;
    }
    print("Registration Check: ${response.data}");
    final prefs = await SharedPreferences.getInstance();
    prefs.setString("registrationToken", registrationToken.value!);
    registered.value = true;
    // isRegistrationRequired.value = true;
    return true;
    /* if (response.data as String == "YES") {
      prefs.setString("registrationToken", registrationToken.value!);
      registered.value = true;
      canEdit.value = true;
      return true;
    } else {
      // Will not remove the registration token. Might be an issue with the server or build.
      // Prefer to keep it stored until user updates the token himself
      registered.value = false;
      // Edit can be enabled with logging in. Will not disable it here.
      return false;
    } */
  }

  Future<void> deactivateDevice() async {
    final prefs = await SharedPreferences.getInstance();
    String deviceId = await getDeviceId();
    final response = await dio.get("https://simsushare.com/susplatlic/release.php", queryParameters: {
      "code": registrationToken.value,
      "mac": deviceId,
    });
    if (response.statusCode != 200) {
      print("Failed to deactivate device (Non-200): ${response.statusCode} ${response.data}");
      throw Exception("Failed to deactivate device: ${response.data}");
    }
    if (response.data.toString() != "RELEASED") {
      print("Failed to deactivate device: ${response.statusCode} ${response.data}");
      throw Exception("Failed to deactivate device: ${response.data}");
    }
    print("Deleting registration token:" + prefs.getString("registrationToken").toString());
    await prefs.remove("registrationToken");
    registered.value = true;
    // isRegistrationRequired.value = true;
    if (isRegistrationRequired.value) {
      Future.delayed(const Duration(milliseconds: 1000), () => Get.dialog(RegistrationDialog(), barrierDismissible: false));
    }
  }
}
