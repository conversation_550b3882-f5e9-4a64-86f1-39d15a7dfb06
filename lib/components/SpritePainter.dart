import 'dart:math';
import 'dart:ui' as UI;
import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:simsushare_player/models/Mask.dart';
import 'package:simsushare_player/models/SimObjects.dart';

class SpritePainter extends CustomPainter {
  final SimSprite sprite;
  final List<Mask> masks;
  // final UI.Image img;
  late double x, y, left, top, width, height;
  double scale = 1.0;
  double interval = 0;

  final DateTime _time = DateTime.now();

  final ValueNotifier<DateTime> notifier;

  int frameIndex = 0;
  bool didPrint = false;
  List<String> printout = List<String>.empty(growable: true);

  SpritePainter({required this.sprite, required this.notifier, required this.masks, this.interval = 100, this.scale = 1.0})
      : super(repaint: notifier) {
    print("${sprite.img.width} x ${sprite.img.height}");
    x = sprite.x;
    y = sprite.y;
    left = sprite.frames[0].x;
    top = sprite.frames[0].y;
    var largestWidth = sprite.frames[0].width;
    // TODO: f.rotated condition might not be useful. double check if to leave it or remove it
    for (var f in sprite.frames) {
      /* if (f.rotated) {
        if (f.height > largestWidth) {
          largestWidth = f.width;
        }
      } else {
        if (f.width > largestWidth) {
          largestWidth = f.width;
        }
      } */
      if (f.width > largestWidth) {
        largestWidth = f.width;
      }
    }
    var largestHeight = sprite.frames[0].width;
    for (var f in sprite.frames) {
      /* if (f.rotated) {
        if (f.width > largestHeight) {
          largestHeight = f.height;
        }
      } else {
        if (f.height > largestHeight) {
          largestHeight = f.height;
        }
      } */
      if (f.height > largestHeight) {
        largestHeight = f.height;
      }
    }
    width = largestWidth * sprite.widthScale;
    height = largestHeight;
  }

  @override
  void paint(Canvas canvas, Size size) {
    canvas.save();
    // canvas.clipRect(Rect.fromLTWH(left, top, width, height));
    // Rect.fromLTWH(0, 0, img.width.toDouble(), img.height.toDouble()),
    List<Mask> masksApplied = masks.where((mask) => sprite.maskIds.contains(mask.id)).toList();

    if (masksApplied.isNotEmpty) {
      canvas.saveLayer(Rect.largest, UI.Paint());
    }

    final showWithinMasks = masksApplied.where((mask) => mask.type == MaskType.showWithin);
    if (showWithinMasks.isNotEmpty) {
      final path = UI.Path();
      showWithinMasks.forEachIndexed((index, mask) {
        if (index != 0) {
          path.moveTo(mask.coordinates.first.x, mask.coordinates.first.y);
        }
        path.addPolygon(mask.coordinates.map((coor) => Offset(coor.x - x, coor.y - y)).toList(), true);
      });
      canvas.clipPath(path);
    }

    if (!didPrint) {
      printout.add(
          "{rotated: ${sprite.frames[frameIndex].rotated}, rect: ${sprite.frames[frameIndex].rotated ? Rect.fromLTRB(left + height, top, left, top + width) : Rect.fromLTWH(left, top, width, height)}}");
    }

    if (sprite.frames[frameIndex].rotated) {
      // canvas.translate(width / 2, height / 2);
      canvas.rotate(pi * 1.5);
      // canvas.translate(-width / 2, -height / 2);
      // canvas.scale(1, 1);
      canvas.translate(-height, 0);
    }
    canvas.drawImageRect(
      sprite.img,
      // Rect.fromLTWH(left, top, sprite.frames[frameIndex].rotated ? height : width, sprite.frames[frameIndex].rotated ? width : height),
      // sprite.frames[frameIndex].rotated ? Rect.fromLTWH(left + width, top, -width, -height) : Rect.fromLTWH(left, top, width, height),
      // Rect.fromLTWH(left, top, width, height),
      // Rect.fromLTWH(left, top, sprite.frames[frameIndex].width, sprite.frames[frameIndex].height),
      Rect.fromLTWH(
        left,
        top,
        sprite.frames[frameIndex].rotated ? sprite.frames[frameIndex].height : sprite.frames[frameIndex].width,
        sprite.frames[frameIndex].rotated ? sprite.frames[frameIndex].width : sprite.frames[frameIndex].height,
      ),
      // Rect.fromLTWH(left, top, sprite.width, sprite.height),
      Rect.fromLTWH(
        0,
        0,
        sprite.frames[frameIndex].rotated ? sprite.frames[frameIndex].height : sprite.frames[frameIndex].width,
        sprite.frames[frameIndex].rotated ? sprite.frames[frameIndex].width : sprite.frames[frameIndex].height,
      ),
      UI.Paint(),
    );
    /* canvas.drawImageRect(sprite.img, Rect.fromLTWH(left, top, width, height),
        Rect.fromLTWH(0, 0, sprite.width * sprite.scale * sprite.widthScale, sprite.height * sprite.scale * sprite.heightScale), UI.Paint()); */

    /* canvas.drawAtlas(
      sprite.img,
      // [UI.RSTransform.fromComponents(rotation: 0, scale: sprite.scale, anchorX: 0, anchorY: 0, translateX: 0, translateY: 0)],
      [UI.RSTransform.fromComponents(rotation: 0, scale: 1, anchorX: 0, anchorY: 0, translateX: 0, translateY: 0)],
      // [Rect.fromLTWH(left, top, width * sprite.widthScale, height)],
      [Rect.fromLTWH(left, top, width, height)],
      [],
      // UI.BlendMode.src,
      UI.BlendMode.src,
      // masksApplied.isEmpty ? UI.BlendMode.src : UI.BlendMode.ds,
      null,
      Paint(),
    ); */

    if (masksApplied.isNotEmpty) {
      final clearPaint = UI.Paint();
      // clearPaint.color = Colors.white;
      clearPaint.blendMode = UI.BlendMode.clear;
      clearPaint.color = Colors.transparent;

      masksApplied.where((mask) => mask.type == MaskType.showOutside).forEach((mask) {
        final path = Path();
        path.addPolygon(mask.coordinates.map((coor) => Offset(coor.x - x, coor.y - y)).toList(), true);
        canvas.drawPath(path, clearPaint);
      });
      canvas.restore();
    }

    frameIndex += 1;

    if (sprite.frames.length == frameIndex) {
      frameIndex = 0;
      if (!didPrint) {
        didPrint = true;
        print(printout);
      }
    }

    left = sprite.frames[frameIndex].x;
    top = sprite.frames[frameIndex].y;
    width = sprite.frames[frameIndex].width /*  * sprite.widthScale */;
    height = sprite.frames[frameIndex].height;
    canvas.restore();
  }

  @override
  bool shouldRepaint(SpritePainter oldDelegate) => DateTime.now().difference(oldDelegate._time).inMilliseconds >= interval;

  @override
  bool shouldRebuildSemantics(SpritePainter oldDelegate) => true;
}
