import 'package:flutter/material.dart';
import 'package:collection/collection.dart';
import 'package:get/get.dart';
import 'package:simsushare_player/components/MainDialog.dart';
import 'package:simsushare_player/controllers/SimController.dart';

class MaskSpriteList extends StatelessWidget {
  final String maskId;
  const MaskSpriteList({Key? key, required this.maskId}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final SimController _simController = Get.find();

    return MainDialog(
        child: Obx(
      () => Column(
        children: [
          ..._simController.currentSim.value!.locations[_simController.currentLocation.value].sprites.mapIndexed(
            (index, sprite) => ListTile(
              leading: Text(sprite.name),
              trailing: Switch(
                  value: sprite.maskIds.firstWhereOrNull((mid) => mid == maskId) != null,
                  onChanged: (enabled) {
                    if (enabled) {
                      _simController.currentSim.value!.locations[_simController.currentLocation.value].sprites[index].maskIds.add(maskId);
                    } else {
                      final maskIndex = sprite.maskIds.indexOf(maskId);
                      _simController.currentSim.value!.locations[_simController.currentLocation.value].sprites[index].maskIds.removeAt(maskIndex);
                    }
                    _simController.currentSim.refresh();
                  }),
            ),
          )
        ],
      ),
    ));
  }
}
