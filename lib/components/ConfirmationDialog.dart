import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:simsushare_player/components/ContentDialog.dart';
import 'package:simsushare_player/components/MainButtons.dart';
import 'package:simsushare_player/utils/constants.dart';

class ConfirmationDialog extends StatelessWidget {
  final String message;
  final Function() onConfirmed;
  final String? confirmLabel;
  final String? cancelLabel;

  const ConfirmationDialog({
    Key? key,
    required this.message,
    required this.onConfirmed,
    this.confirmLabel,
    this.cancelLabel,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ContentDialog(
      title: "Confirm",
      width: 700,
      height: 370,
      content: Container(
        padding: const EdgeInsets.symmetric(vertical: 10),
        alignment: Alignment.center,
        child: Text(
          message,
          style: const TextStyle(
            color: white60,
            fontSize: 16,
          ),
          maxLines: 3,
          textAlign: TextAlign.center,
        ),
      ),
      actions: [
        TransparentButton(
          label: cancelLabel ?? "Cancel",
          // breakpoint: breakpoint,
          onPressed: () => Get.back(result: false),
        ),
        actionButtonsGap,
        OrangeButton(
          label: confirmLabel ?? "Confirm",
          onPressed: () async {
            final result = await onConfirmed();
            Get.back(result: result);
          },
        ),
      ],
    );
  }
}
