// ignore_for_file: invalid_use_of_protected_member

import 'package:flutter/material.dart';
import 'package:collection/collection.dart';
import 'package:get/get.dart';
import 'package:simsushare_player/controllers/SimController.dart';
import 'package:simsushare_player/models/SimObjects.dart';

class CopyMultipleSpritesDialog extends StatelessWidget {
  const CopyMultipleSpritesDialog({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final SimController _simController = Get.find();

    var selectedSprites = (_simController.currentSim.value!.locations[_simController.currentLocation.value].sprites.map((e) => false).toList()).obs;

    return SimpleDialog(title: const Text("Copy Multiple Sprites"), children: [
      ..._simController.currentSim.value!.locations[_simController.currentLocation.value].sprites
          .mapIndexed(
            (index, element) => Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                Text(index.toString()),
                Obx(() => Checkbox(
                    value: selectedSprites.value[index],
                    onChanged: (checked) {
                      selectedSprites.value[index] = checked ?? false;
                      selectedSprites.refresh();
                    }))
              ],
            ),
          )
          .toList(),
      const SizedBox(
        height: 20,
      ),
      Row(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
            },
            child: const Text("Close"),
          ),
          TextButton(
            onPressed: () {
              // if none is selected
              if (!selectedSprites.firstWhere((element) => element)) return;
              selectedSprites.forEachIndexed((index, copied) {
                if (!copied) return;
                final spriteCopy =
                    _simController.currentSim.value!.locations[_simController.currentLocation.value].sprites[index].copy() as SimSprite;
                spriteCopy.x += 15;
                spriteCopy.y -= 15;
                _simController.currentSim.value!.locations[_simController.currentLocation.value].sprites.add(spriteCopy);
                _simController.currentSim.refresh();
                Get.back(canPop: false, closeOverlays: true);
              });
            },
            child: const Text("Copy"),
          )
        ],
      ),
    ]);
  }
}
