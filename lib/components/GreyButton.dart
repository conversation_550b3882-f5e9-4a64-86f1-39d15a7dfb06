import 'package:flutter/material.dart';
import 'package:simsushare_player/utils/constants.dart';

class GreyButtonLarge extends StatelessWidget {
  final Function() onPressed;
  final Widget child;
  const GreyButtonLarge({Key? key, required this.child, required this.onPressed}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ElevatedButton(
      onPressed: onPressed,
      child: child,
      style: ElevatedButton.styleFrom(
        backgroundColor: lightBackgrounds,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(50), side: const BorderSide(color: bordersColor, width: 1)),
        padding: const EdgeInsets.symmetric(vertical: 14.5, horizontal: 22),
      ),
    );
  }
}

class GreyButtonSmall extends StatelessWidget {
  final Function() onPressed;
  final Widget child;
  const GreyButtonSmall({Key? key, required this.child, required this.onPressed}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ElevatedButton(
      onPressed: onPressed,
      child: child,
      style: ElevatedButton.styleFrom(
        backgroundColor: lightBackgrounds,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(50),
          side: const BorderSide(color: bordersColor, width: 1),
        ),
        padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 4),
      ),
    );
  }
}
