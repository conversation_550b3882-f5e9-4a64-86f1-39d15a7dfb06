import 'package:flutter/material.dart';
import 'package:collection/collection.dart';
import 'package:get/get.dart';
import 'package:simsushare_player/controllers/SimController.dart';
import 'package:simsushare_player/models/Mask.dart';

class MasksDialog extends StatelessWidget {
  const MasksDialog({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // NOTE: WIP
    final SimController _simController = Get.find();

    return Dialog(
      child: Obx(
        // () => ReorderableListView(
        () => ListView(
          /* onReorder: (oldIndex, newIndex) {
              final targetMask = _simController.currentSim.value!.masks[oldIndex];
              _simController.currentSim.value!.masks.removeAt(oldIndex);
              _simController.currentSim.value!.masks.insert(newIndex, targetMask);
              _simController.currentSim.refresh();
            }, */
          children: [
            ..._simController.currentSim.value!.masks.mapIndexed(
              (index, mask) => ListTile(
                key: ValueKey(mask.id),
                title: Row(
                  children: [
                    Text("${mask.name} (${mask.type == MaskType.showWithin ? "Portal" : "Clipper"})"),
                    const SizedBox(width: 10),
                    Container(
                      width: 30,
                      height: 30,
                      decoration: BoxDecoration(
                        color: Color(int.parse(mask.color, radix: 16)),
                        border: Border.all(width: 1, color: Colors.transparent),
                        borderRadius: BorderRadius.circular(2),
                      ),
                      child: const SizedBox(),
                    )
                  ],
                ),
                trailing: ElevatedButton(
                  onPressed: () {
                    if (mask.type == MaskType.showWithin) {
                      _simController.currentSim.value!.masks[index].type = MaskType.showOutside;
                    } else {
                      _simController.currentSim.value!.masks[index].type = MaskType.showWithin;
                    }
                    _simController.currentSim.refresh();
                  },
                  child: const Text("Reverse"),
                ),
              ),
            )
          ],
        ),
      ),
    );
  }
}
