import 'package:flutter/material.dart';
import 'package:simsushare_player/models/Mask.dart';

class MaskClipper extends CustomClipper<Path> {
  final List<Coordinate> coords;

  MaskClipper(this.coords);

  @override
  Path getClip(Size size) {
    print("Clipping mask path $coords");
    Path path = Path();
    // path.addPolygon([Offset(0, size.height), Offset(size.width / 2, 0), Offset(size.width, size.height)], true);
    path.addPolygon(coords.map((c) => Offset(c.x, c.y)).toList(), true);
    // path.
    return path;
  }

  @override
  bool shouldReclip(covariant CustomClipper<Path> oldClipper) {
    return false;
  }
}
