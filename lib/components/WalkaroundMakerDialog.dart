import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:simsushare_player/components/ContentDialog.dart';
import 'package:simsushare_player/components/MainButtons.dart';
import 'package:simsushare_player/controllers/SimController.dart';
import 'package:simsushare_player/models/Simulation.dart';

const _locationTextStyleLarge = TextStyle(
  color: Colors.white,
  fontSize: 20,
);

const _locationTextStyleSmall = TextStyle(
  color: Colors.white,
  fontSize: 12,
);

class WalkaroundMakerDialog extends StatelessWidget {
  WalkaroundMakerDialog({
    Key? key,
  }) : super(key: key);

  final selectedLocations = <SimulationLocation>[].obs;

  @override
  Widget build(BuildContext context) {
    final _simController = Get.find<SimController>();
    final sim = _simController.currentSim.value!;
    return ContentDialog(
      title: "Walkaround Maker",
      content: Obx(
        () => Row(
          children: [
            Expanded(
              child: ListView(
                children: sim.locations
                    .where((loc) => !selectedLocations.contains(loc))
                    .map(
                      (loc) => ListTile(
                        leading: Text(
                          "${loc.name} (${sim.states.firstWhere((state) => state.id == loc.state).name})",
                          style: _locationTextStyleLarge,
                        ),
                        onTap: () {
                          selectedLocations.add(loc);
                        },
                      ),
                    )
                    .toList(),
              ),
            ),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 5),
              child: const VerticalDivider(
                color: Colors.white,
                thickness: 2,
              ),
            ),
            Expanded(
              child: Column(
                children: selectedLocations
                    .map(
                      (loc) => ListTile(
                        leading:
                            Text("${loc.name} (${sim.states.firstWhere((state) => state.id == loc.state).name})", style: _locationTextStyleLarge),
                        onTap: () {
                          selectedLocations.remove(loc);
                        },
                      ),
                    )
                    .toList(),
              ),
            )
          ],
        ),
      ),
      actions: [
        TransparentButton(
          onPressed: () {
            Get.back();
          },
          label: "Cancel",
        ),
        const SizedBox(width: 10),
        OrangeButton(
          onPressed: () {
            // Make walkaround
            if (selectedLocations.length < 2) {
              Get.snackbar("Error", "You must select at least 2 locations to make a walkaround");
              return;
            }
            for (var i = 0; i < selectedLocations.length; i++) {
              final loc = selectedLocations[i];
              var east = sim.navigations.firstWhereOrNull((nav) => nav.from == loc.id && nav.direction == "E");
              var west = sim.navigations.firstWhereOrNull((nav) => nav.from == loc.id && nav.direction == "W");
              final toWest = i == selectedLocations.length - 1 ? selectedLocations[0].id : selectedLocations[i + 1].id;
              final toEast = i == 0 ? selectedLocations[selectedLocations.length - 1].id : selectedLocations[i - 1].id;
              if (east == null) {
                east = SimulationNavigation(
                  from: loc.id,
                  to: toEast,
                  direction: "E",
                );
                sim.navigations.add(east);
              } else {
                east.to = toEast;
              }
              if (west == null) {
                west = SimulationNavigation(
                  from: loc.id,
                  to: toWest,
                  direction: "W",
                );
                sim.navigations.add(west);
              } else {
                west.to = toWest;
              }
            }
            _simController.currentSim.refresh();
            Get.back();
          },
          label: "Create",
        ),
      ],
    );
  }
}
