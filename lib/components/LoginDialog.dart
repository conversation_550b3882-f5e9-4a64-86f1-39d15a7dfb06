import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:simsushare_player/components/ContentDialog.dart';
import 'package:simsushare_player/components/MainButtons.dart';
import 'package:simsushare_player/controllers/UserController.dart';
import 'package:simsushare_player/utils/constants.dart';

class LoginDialog extends StatelessWidget {
  LoginDialog({
    Key? key,
  }) : super(key: key);

  final repoUrl = baseurl.split("/api")[0].obs;
  final username = "".obs;
  final password = "".obs;
  final error = "".obs;
  final loading = false.obs; // NOTE: doesn't work

  @override
  Widget build(BuildContext context) {
    UserController _userController = Get.find();
    return ContentDialog(
        title: "Login",
        width: 550,
        height: 470,
        content: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Obx(
              () {
                return error.isEmpty
                    ? const SizedBox()
                    : Container(
                        margin: const EdgeInsets.only(bottom: 20),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text(
                              error.value,
                              style: TextStyle(color: Colors.red[500]),
                            ),
                          ],
                        ),
                      );
              },
            ),
            TextField(
              decoration: const InputDecoration(label: Text("Repository URL")),
              onChanged: (value) => repoUrl.value = value,
              style: textFieldTextStyle,
              controller: TextEditingController(text: repoUrl.value),
            ),
            const SizedBox(height: 10),
            TextField(
              decoration: const InputDecoration(label: Text("Username/Email")),
              onChanged: (value) => username.value = value,
              style: textFieldTextStyle,
            ),
            const SizedBox(height: 10),
            TextField(
              decoration: const InputDecoration(label: Text("Password")),
              obscureText: true,
              onChanged: (value) => password.value = value,
              style: textFieldTextStyle,
            ),
          ],
        ),
        actions: [
          TransparentButton(
            onPressed: () {
              Get.back(canPop: false);
            },
            label: "Cancel",
          ),
          Obx(
            () => OrangeButton(
              disabled: loading.value,
              onPressed: () async {
                try {
                  loading.value = true;
                  // print(baseurl + "/login");
                  print("Repo:" + repoUrl.value);
                  try {
                    await Dio(BaseOptions(
                      receiveTimeout: const Duration(seconds: 1),
                      validateStatus: (status) => status == 200,
                    )).get(repoUrl.value);
                  } catch (err) {
                    print(err);
                    error.value = "Invalid Repository URL";
                    return;
                  } finally {
                    loading.value = false;
                  }
                  // reassignDio(repoUrl.value + "/api");
                  await reassignBaseUrl(repoUrl.value);
                  final response = await dio.post("/login", data: {
                    "user": username.value,
                    "password": password.value,
                  });
                  print(response.headers);
                  print({
                    "user": username.value,
                    "password": password.value,
                  });
                  print("Response: ${response.data}");

                  final responseBody = response.data;
                  if (response.statusCode != 200) {
                    error.value = responseBody["error"];
                    return print("${response.statusCode}, $responseBody");
                  }
                  final prefs = await SharedPreferences.getInstance();
                  await prefs.setString("token", responseBody["token"]);
                  _userController.user.value = responseBody["user"];
                  _userController.setToken(responseBody["token"]);
                  await prefs.setString("companyId", responseBody["user"]["company"]["_id"]);
                  // _userController.token.value = responseBody["token"];
                  // dio.options.headers["Authorization"] = responseBody["token"];
                  return Get.back(result: responseBody["user"], canPop: false);
                } catch (err) {
                  print(err);
                }
              },
              label: "Log In",
            ),
          )
        ]);
  }
}
