import 'dart:io';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:simsushare_player/components/ContentDialog.dart';
import 'package:simsushare_player/components/MainButtons.dart';
import 'package:simsushare_player/components/PickLocationImage.dart';

class SelectSingleImageDialog extends StatelessWidget {
  final String title;
  final String errorMessage;
  final String confirmLabel;
  SelectSingleImageDialog({
    Key? key,
    required this.title,
    this.confirmLabel = "Select",
    this.errorMessage = "No image selected",
  }) : super(key: key);

  final selectedImage = "".obs;

  @override
  Widget build(BuildContext context) {
    return ContentDialog(
        title: title,
        content: Column(
          children: [
            const Text(
              "Upload Image",
              style: TextStyle(color: Colors.white),
            ),
            const SizedBox(height: 11),
            PickLocationImage(
              multi: false,
              onImageSelected: (image) {
                if (image.isEmpty) return;
                selectedImage.value = image[0]!;
              },
            ),
            const SizedBox(height: 10),
            Obx(() {
              if (selectedImage.value.isEmpty) return const SizedBox();
              return Center(
                child: Image.file(
                  File(selectedImage.value),
                  fit: BoxFit.contain,
                  height: 640 / 2,
                  width: 940 / 2,
                ),
              );
            })
          ],
        ),
        actions: [
          TransparentButton(
            onPressed: () {
              Get.back();
            },
            label: "Cancel",
          ),
          OrangeButton(
            onPressed: () {
              if (selectedImage.value.isEmpty) {
                Get.snackbar("Error", errorMessage);
                return;
              }
              Get.back(result: selectedImage.value);
            },
            label: confirmLabel,
          ),
        ]);
  }
}
