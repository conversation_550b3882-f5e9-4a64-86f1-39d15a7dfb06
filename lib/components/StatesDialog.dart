import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:collection/collection.dart';
import 'package:simsushare_player/controllers/SimController.dart';
import 'package:simsushare_player/models/Simulation.dart';

class StatesDialog extends StatelessWidget {
  const StatesDialog({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final SimController _simController = Get.find();

    final originalList = [..._simController.currentSim.value!.states];
    print("Original list: $originalList (${originalList.length})");

    final newStateName = "".obs;
    final errorMsg = "".obs;

    return Dialog(
      child: Obx(
        () => Column(
          children: [
            Visibility(
              child: Text(errorMsg.value),
              visible: errorMsg.isNotEmpty,
            ),
            const Text("States"),
            TextField(
              onChanged: (value) {
                newStateName.value = value;
              },
            ),
            ..._simController.currentSim.value!.states.mapIndexed(
              (index, state) => TextField(
                controller: TextEditingController(text: state.name),
                onChanged: (value) {
                  _simController.currentSim.value!.states[index].name = value;
                },
              ),
            ),
            TextButton(
              onPressed: () {
                errorMsg.value = "";
                _simController.currentSim.value!.states.add(SimulationState(id: "state0", name: ""));
              },
              child: const Text("Add State +"),
            ),
            Row(
              children: [
                TextButton(
                  onPressed: () {
                    _simController.currentSim.value!.states = originalList;
                    _simController.currentSim.refresh();
                    Get.back();
                  },
                  child: const Text("Cancel"),
                ),
                TextButton(
                  onPressed: () {
                    final stateNames = <String, bool>{};
                    final allUnique = _simController.currentSim.value!.states.every((state) {
                      if (stateNames[state] == true) {
                        return false;
                      }
                      stateNames[state.id] = true;
                      return true;
                    });
                    if (!allUnique) {
                      errorMsg.value = "2 states cannot have the same name";
                      return;
                    }
                    _simController.currentSim.value!.states.forEachIndexed((index, state) {
                      final oldName = _simController.currentSim.value!.states[index];
                      _simController.currentSim.value!.locations.forEachIndexed((eIndex, location) {
                        if (location.state == oldName.id) {
                          _simController.currentSim.value!.locations[eIndex].state = state.id;
                        }
                      });
                    });
                    _simController.currentSim.refresh();
                    Get.back();
                  },
                  child: const Text("Done"),
                )
              ],
            ),
          ],
        ),
      ),
    );
  }
}
