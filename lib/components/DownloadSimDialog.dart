// ignore_for_file: must_be_immutable

import 'dart:io';

import 'package:archive/archive.dart';
import 'package:collection/collection.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:path_provider/path_provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:simsushare_player/components/ContentDialog.dart';
import 'package:simsushare_player/components/MainButtons.dart';
import 'package:simsushare_player/components/RepositoriesDialog.dart';
import 'package:simsushare_player/controllers/SimController.dart';
import 'package:simsushare_player/controllers/UserController.dart';
import 'package:simsushare_player/models/CloudScenario.dart';
import 'package:simsushare_player/utils/constants.dart';

class DownloadSimDialog extends StatelessWidget {
  DownloadSimDialog({
    Key? key,
  }) : super(key: key);

  Rx<List<CloudScenario>> scenarios = Rx<List<CloudScenario>>([]);
  Rx<List<CloudScenario>> selectedScenarios = Rx<List<CloudScenario>>([]);
  RxList<String> repos = RxList<String>([]);
  final currentRepo = "".obs;
  final loginUsername = "".obs;
  final loginPassword = "".obs;

  _initialize() async {
    UserController _userController = Get.find();
    print("USER ID: ${_userController.user.value["id"]}");
    if (_userController.user.value == null) {
      print("User value is null in user controller");
      return;
    }
    /* try { */
    // print(_userController.user.value);
    final response = await dio.get("/scenarios/" + _userController.user.value["company"]["_id"]);
    // print("${response.statusCode} ${response.data}");
    if (response.statusCode != 200) {
      return print("Failed to fetch scenarios: ${response.statusCode} ${response.data}");
    }
    final scenariosData = response.data["scenarios"];
    print(scenariosData[0]);
    print(scenariosData[0].runtimeType);
    print(scenariosData[0].toString());
    scenarios.value = List<CloudScenario>.from(scenariosData.map((s) => CloudScenario.fromJson(s)), growable: true);
    // print("Scenarios: ${scenarios.value}");
    /* } catch (err) {
      print(err);
    } */
    final prefs = await SharedPreferences.getInstance();
    final storedRepos = prefs.getStringList("repos_${_userController.user.value["id"]}");
    if (storedRepos != null) {
      repos.value = storedRepos;
    }
    repos.add(baseurl.replaceAll("https://", "").replaceAll("http://", "").replaceAll("/api", ""));
    repos.value = repos.toSet().toList(); // remove duplicates
    currentRepo.value = repos.first;
    repos.refresh();
  }

  _getRepoScenarios(String repo, {required String token}) async {
    print("Fetching repo scenarios for $repo with token: $token");
    UserController _userController = Get.find();
    final decoded = _userController.decodeServerAuthToken(token);
    final response = await Dio(
      BaseOptions(
        headers: {
          "Authorization": token,
        },
        validateStatus: (status) => true,
      ),
    ).get("https://$repo/api/scenarios/" + decoded["companyId"]);
    if (response.statusCode != 200) {
      Get.showSnackbar(GetSnackBar(
        title: "Failed to fetch scenarios",
        message: "${response.statusCode} ${response.data}",
        duration: const Duration(seconds: 3),
      ));
      _userController.repoToken.remove(repo);
      return print("Failed to fetch scenarios: ${response.statusCode} ${response.data}");
    }
    final scenariosData = response.data["scenarios"];
    scenarios.value = List<CloudScenario>.from(scenariosData.map((s) => CloudScenario.fromJson(s)), growable: true);
  }

  @override
  Widget build(BuildContext context) {
    UserController _userController = Get.find();
    _initialize();
    return ContentDialog(
      title: "Download Simulation",
      content: Obx(
        () => Column(
          children: [
            Row(
              children: [
                Expanded(
                  child: Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: DropdownButton<String>(
                      isExpanded: true,
                      // value: repos.isNotEmpty ? repos.first : null,
                      value: currentRepo.value.isNotEmpty ? currentRepo.value : null,
                      items: [
                        ...repos
                            .map((repo) => DropdownMenuItem<String>(
                                  value: repo,
                                  child: Text(
                                    repo,
                                    style: const TextStyle(color: Colors.white),
                                  ),
                                ))
                            .toList(),
                        const DropdownMenuItem<String>(
                          value: "+",
                          child: Row(
                            children: [
                              Icon(Icons.add, color: Colors.yellow),
                              SizedBox(width: 8),
                              Text(
                                "Add new repository",
                                style: TextStyle(color: Colors.yellow),
                              ),
                            ],
                          ),
                        )
                      ],
                      onChanged: (value) async {
                        if (value == null) return;
                        if (value == "+") {
                          await Get.dialog(RepositoriesDialogLarge());
                          return _initialize();
                        }
                        // // find true key
                        // final realValue = _userController.repoToken.keys
                        //     .firstWhereOrNull((key) => key.replaceAll("https://", "").replaceAll("http://", "").replaceAll("/api", "") == value);
                        currentRepo.value = value;
                        if (!_userController.repoToken.keys
                            .map((key) => key.replaceAll("https://", "").replaceAll("http://", "").replaceAll("/api", ""))
                            .contains(value)) {
                          // Handle the case where the token does not exist
                          // You can show a dialog or prompt the user to enter the token
                          loginUsername.value = "";
                          loginPassword.value = "";
                          scenarios.value.clear();
                          return;
                        }
                        _getRepoScenarios(value, token: _userController.repoToken[value]!);
                      },
                      dropdownColor: Colors.black,
                      style: const TextStyle(color: Colors.white),
                    ),
                  ),
                ),
              ],
            ),
            Expanded(
              child: !_userController.repoToken.keys
                      .map((key) => key.replaceAll("https://", "").replaceAll("http://", "").replaceAll("/api", ""))
                      .contains(currentRepo.value)
                  ? Column(
                      children: [
                        Padding(
                          padding: const EdgeInsets.all(8.0),
                          child: TextField(
                            decoration: const InputDecoration(
                              labelText: 'Username',
                              labelStyle: TextStyle(color: Colors.white),
                            ),
                            style: const TextStyle(color: Colors.white),
                            onChanged: (value) {
                              loginUsername.value = value;
                            },
                          ),
                        ),
                        Padding(
                          padding: const EdgeInsets.all(8.0),
                          child: TextField(
                            decoration: const InputDecoration(
                              labelText: 'Password',
                              labelStyle: TextStyle(color: Colors.white),
                            ),
                            onChanged: (value) {
                              loginPassword.value = value;
                            },
                            obscureText: true,
                            style: const TextStyle(color: Colors.white),
                          ),
                        ),
                        Padding(
                          padding: const EdgeInsets.all(8.0),
                          child: SizedBox(
                            width: 120,
                            child: OrangeButton(
                              onPressed: () async {
                                final token = await _userController.loginRepo(
                                  repoUrl: currentRepo.value,
                                  username: loginUsername.value,
                                  password: loginPassword.value,
                                );
                                _userController.repoToken[currentRepo.value] = token;
                                loginUsername.value = "";
                                loginPassword.value = "";
                                _getRepoScenarios(currentRepo.value, token: token);
                              },
                              label: 'Log In',
                            ),
                          ),
                        ),
                      ],
                    )
                  : ListView(
                      children: [
                        ...scenarios.value
                            .map(
                              (scenario) => ListTile(
                                hoverColor: white60,
                                selectedTileColor: brick,
                                title: Text(scenario.title, style: const TextStyle(color: Colors.white)),
                                selected: selectedScenarios.value.contains(scenario),
                                onTap: () async {
                                  if (selectedScenarios.value.contains(scenario)) {
                                    selectedScenarios.value.remove(scenario);
                                  } else {
                                    selectedScenarios.value.add(scenario);
                                  }
                                  selectedScenarios.refresh();
                                },
                              ),
                            )
                            .toList(),
                      ],
                    ),
            ),
          ],
        ),
      ),
      actions: [
        TransparentButton(
          label: "Cancel",
          onPressed: () {
            Get.back(result: false);
          },
        ),
        OrangeButton(
          label: "Download",
          onPressed: () async {
            if (selectedScenarios.value.isEmpty) return;
            Get.back(result: true);
            Get.showSnackbar(const GetSnackBar(
              title: "Download Started",
              message: "Sim download started in background and we will notify you as soon as it downloaded",
              duration: Duration(seconds: 3),
            ));
            int successCount = 0;
            try {
              await Future.wait(selectedScenarios.value.map((scenario) async {
                print("Tapped: $scenario");
                var response = await dio.get("/scenarios/${_userController.user.value["company"]["_id"]}/${scenario.id}/getlink");
                if (response.statusCode != 200) {
                  return print("${response.statusCode} ${response.data}");
                }
                final downloadLink = response.data;
                print(downloadLink);
                response = await dio.get(
                  downloadLink,
                  options: Options(
                    responseType: ResponseType.bytes,
                    contentType: "application/json",
                    headers: {"pragma": "no-cache", "Authorization": ""},
                  ),
                );
                print(response.data.runtimeType);
                print("${response.requestOptions.contentType} ${response.requestOptions.headers}");
                final archiveFile = File((await getApplicationDocumentsDirectory()).createTempSync("tmp_").path + "/archive.zip");
                await archiveFile.writeAsBytes(response.data);
                print(archiveFile.path);
                // final archive = ZipDecoder().decodeBytes(response.data);
                final archive = ZipDecoder().decodeBytes(await archiveFile.readAsBytes());
                final dataDir = await getApplicationDocumentsDirectory();
                final scenarioPath = dataDir.path + "/simulations/" + scenario.title;
                var finalPath = scenarioPath;
                final conflictDirectory = await Directory(scenarioPath).exists();
                if (conflictDirectory) {
                  final dirListing = await Directory(dataDir.path + "/simulations/").list().toList();
                  int largestConflictingValue = 0;
                  dirListing.where((entry) => entry.path.startsWith(scenarioPath)).forEach((entry) {
                    final suffix = entry.path.split(scenarioPath)[1];
                    if (suffix.isEmpty) return;
                    final value = int.tryParse(suffix.trim().replaceAll("(", "").replaceAll(")", "")) ?? 0;
                    if (value > largestConflictingValue) {
                      largestConflictingValue = value;
                    }
                  });
                  finalPath += " ($largestConflictingValue)";
                }
                await Directory(finalPath).create();
                await Future.wait(archive.files.map((f) async {
                  print("Name: ${f.name}, isFile: ${f.isFile}");
                  if (f.isFile) {
                    final writeFile = File(finalPath + "/" + f.name);
                    await writeFile.create(recursive: true);
                    return writeFile.writeAsBytes(f.content);
                  } else {
                    await Directory(finalPath + "/" + f.name).create();
                  }
                }));
                successCount++;
              }));
              if (successCount > 0) {
                Get.showSnackbar(GetSnackBar(
                  title: "Downlaoding complete",
                  message: "$successCount simulation(s) has completed download",
                  duration: const Duration(seconds: 3),
                ));
                final _simController = Get.find<SimController>();
                _simController.signalStream.add("downloaded");
                return;
              } else {
                Get.showSnackbar(const GetSnackBar(
                  title: "Download failed",
                  message: "Failed to download all simulations",
                  duration: Duration(seconds: 3),
                ));
              }
            } catch (err) {
              print(err);
              Get.showSnackbar(const GetSnackBar(
                title: "Download failed",
                message: "Failed to download one or more simulation",
                duration: Duration(seconds: 3),
              ));
            }
          },
        )
      ],
    );
  }
}
