// ignore_for_file: must_be_immutable

import 'dart:convert';
import 'package:collection/collection.dart';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:simsushare_player/controllers/SimController.dart';
import 'package:simsushare_player/models/SimObjects.dart';

class ChooseSpriteWidget extends StatelessWidget {
  final List<String> spriteNames;
  List<String> spriteAssetNames;
  ChooseSpriteWidget({Key? key, required this.spriteNames, required this.spriteAssetNames}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final SimController _simController = Get.find();
    return Dialog(
      child: GridView.count(
          crossAxisCount: 4,
          mainAxisSpacing: 8,
          crossAxisSpacing: 8,
          children: spriteNames
              .mapIndexed(
                (index, sn) => GestureDetector(
                  child: Container(
                    color: Colors.blue.shade700,
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.spaceAround,
                      children: [
                        Text(
                          sn,
                          style: const TextStyle(color: Colors.white),
                        ),
                      ],
                    ),
                  ),
                  onTap: () async {
                    print("Adding sprite");
                    final img = await decodeImageFromList((await rootBundle.load(spriteAssetNames[index])).buffer.asUint8List());
                    final meta = jsonDecode(await rootBundle.loadString(spriteAssetNames[index].replaceFirst(".png", ".json")));
                    // print("META: ${(meta["frames"] as Map<String, dynamic>).keys.toList()}");
                    final frames = (meta["frames"] as Map<String, dynamic>)
                        .values
                        .map((e) => SpriteFrame(
                            x: (e["frame"]["x"] as int).toDouble(),
                            y: (e["frame"]["y"] as int).toDouble(),
                            width: (e["frame"]["w"] as int).toDouble(),
                            height: (e["frame"]["h"] as int).toDouble(),
                            rotated: e["rotated"] as bool))
                        .toList();
                    final cs = _simController.currentSim.value!;
                    cs.locations[0].sprites.add(
                      SimSprite(
                        img: img, frames: frames, x: Get.width * 0.5, y: Get.height * 0.5, assetName: spriteAssetNames[index],
                        // width: frames[0].width,
                        // height: frames[0].height,
                      ),
                    );
                    // _simController.updateCurrentSim(cs);
                    _simController.currentSim.value = cs;
                    _simController.currentSim.refresh();
                    Future.delayed(const Duration(seconds: 1), () async {
                      print("${_simController.currentSim.value!.locations[0].sprites.length} sprites in current scenario");
                    });
                    Navigator.pop(context);
                    // print(frames);

                    /* _currentSprite.value = Sprite(
                                      img: img,
                                      frames: frames,
                                      x: Get.width * 0.5,
                                      y: Get.height * 0.5,
                                      width: 500,
                                      height: 500); */
                  },
                ),
              )
              .toList()),
    );
  }
}
