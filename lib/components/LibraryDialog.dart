import 'dart:io';
import 'package:path/path.dart' as path;
import 'package:collection/collection.dart';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:path_provider/path_provider.dart';
import 'package:simsushare_player/components/ContentDialog.dart';
import 'package:simsushare_player/components/MainButtons.dart';
import 'package:simsushare_player/utils/constants.dart';

class LibraryDialog extends StatelessWidget {
  final List<String> extensionFilter;
  final bool allowMultiple;

  LibraryDialog({
    Key? key,
    this.extensionFilter = const [],
    this.allowMultiple = true,
  }) : super(key: key);

  final libraryFolders = RxList<Directory>.empty(growable: true);
  final libraryFolderFiles = RxList<List<FileSystemEntity>>.empty(growable: true);
  final openLibraryFolders = RxSet<int>();
  final recordings = RxList<FileSystemEntity>.empty(growable: true);

  final selectedFiles = RxList<FileSystemEntity>.empty(growable: true);

  _getFiles() async {
    // NOTE: Path provider doesn't support the web
    if (kIsWeb) return;
    final docDir = await getApplicationDocumentsDirectory();
    print("DIR: ${docDir.path}");
    final dir = Directory(docDir.path + "/library");
    if (!(await dir.exists())) {
      await dir.create();
    }
    // Directory(dir.path )
    final folders = (await dir.list().toList()).where((entry) => !entry.path.contains(".DS_Store"));
    print("FILES: $folders");
    libraryFolders.addAll(folders.map((f) => Directory(f.path)));
    final folderFilesList = await Future.wait(
      folders.map((f) async => Directory(f.path).list().toList()),
    );
    libraryFolderFiles.addAll(folderFilesList);
    libraryFolders.refresh();

    final recordingsDir = Directory(docDir.path + "/recordings");
    if (!(await recordingsDir.exists())) {
      await recordingsDir.create();
    }
    final recordingFiles = await recordingsDir.list().toList();
    recordings.addAll(recordingFiles);
  }

  @override
  Widget build(BuildContext context) {
    _getFiles();

    return ContentDialog(
      title: "Select From Library",
      content: Container(
        padding: const EdgeInsets.symmetric(horizontal: 24),
        child: Obx(
          () => Column(
            children: [
              Expanded(
                child: SingleChildScrollView(
                  child: Column(children: [
                    (extensionFilter.isNotEmpty && recordings.where((r) => extensionFilter.contains(path.extension(r.path))).isEmpty)
                        ? const SizedBox()
                        : Container(
                            padding: const EdgeInsets.symmetric(vertical: 20, horizontal: 16),
                            margin: const EdgeInsets.only(bottom: 17),
                            decoration: BoxDecoration(color: lightBackgrounds, borderRadius: BorderRadius.circular(6)),
                            child: Column(
                              children: [
                                const Row(
                                  children: [
                                    Icon(Icons.folder, color: Colors.white),
                                    SizedBox(width: 14),
                                    Text("Recordings", style: TextStyle(color: Colors.white)),
                                  ],
                                ),
                                const Divider(color: white60),
                                Column(
                                  children: recordings.isEmpty
                                      ? []
                                      : recordings
                                          .map(
                                            (file) => Container(
                                              width: 180,
                                              height: 180,
                                              decoration: BoxDecoration(
                                                borderRadius: BorderRadius.circular(4),
                                              ),
                                              child: GestureDetector(
                                                onTap: () {
                                                  Get.back(canPop: false, result: file);
                                                },
                                                child: Center(
                                                  child: Text(
                                                    path.basename(file.path),
                                                    style: const TextStyle(color: Colors.white),
                                                  ),
                                                ),
                                              ),
                                            ),
                                          )
                                          .toList(),
                                )
                              ],
                            ),
                          ),
                    ...libraryFolders
                        .mapIndexed(
                          (folderIndex, folder) => Container(
                            padding: const EdgeInsets.symmetric(vertical: 20, horizontal: 16),
                            margin: const EdgeInsets.only(bottom: 17),
                            decoration: BoxDecoration(color: lightBackgrounds, borderRadius: BorderRadius.circular(6)),
                            child: Column(
                              children: [
                                Row(
                                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                  children: [
                                    Row(
                                      children: [
                                        const Icon(Icons.folder, color: Colors.white),
                                        const SizedBox(width: 14),
                                        Text(path.basename(folder.path), style: const TextStyle(color: Colors.white)),
                                      ],
                                    ),
                                    Obx(
                                      () => IconButton(
                                        icon: Icon(
                                          openLibraryFolders.contains(folderIndex) ? Icons.expand_less : Icons.expand_more,
                                          color: Colors.white,
                                        ),
                                        onPressed: () {
                                          openLibraryFolders.contains(folderIndex)
                                              ? openLibraryFolders.remove(folderIndex)
                                              : openLibraryFolders.add(folderIndex);
                                        },
                                      ),
                                    ),
                                  ],
                                ),
                                const Divider(color: white60),
                                if (openLibraryFolders.contains(folderIndex))
                                  Wrap(
                                    alignment: WrapAlignment.start,
                                    spacing: 14,
                                    children: libraryFolderFiles.isEmpty
                                        ? []
                                        : libraryFolderFiles[folderIndex]
                                            .where((file) => extensionFilter.isEmpty ? true : extensionFilter.contains(path.extension(file.path)))
                                            .map(
                                              (file) => Obx(
                                                () => Container(
                                                  width: 180,
                                                  height: 180,
                                                  decoration: BoxDecoration(
                                                    borderRadius: BorderRadius.circular(4),
                                                    border: selectedFiles.contains(file) ? Border.all(color: Colors.white, width: 2) : null,
                                                  ),
                                                  child: GestureDetector(
                                                    onTap: () {
                                                      if (!allowMultiple) {
                                                        return Get.back(canPop: false, result: [file]);
                                                      }
                                                      if (selectedFiles.contains(file)) {
                                                        selectedFiles.remove(file);
                                                      } else {
                                                        selectedFiles.add(file);
                                                      }
                                                    },
                                                    child: [".jpg", ".jpeg", ".png", ".gif"].contains(path.extension(file.path))
                                                        ? Image.file(File(file.path), fit: BoxFit.cover)
                                                        : Center(
                                                            child: Text(
                                                              path.basename(file.path),
                                                              style: const TextStyle(color: Colors.white),
                                                            ),
                                                          ),
                                                  ),
                                                ),
                                              ),
                                            )
                                            .toList(),
                                  )
                              ],
                            ),
                          ),
                        )
                        .toList(),
                  ]),
                ),
              ),
            ],
          ),
        ),
      ),
      actions: allowMultiple
          ? [
              TransparentButton(
                onPressed: () {
                  Get.back();
                },
                label: "Go Back",
              ),
              OrangeButton(
                onPressed: () {
                  Get.back(canPop: false, result: selectedFiles.toList());
                },
                label: "Select",
              )
            ]
          : [],
    );
  }
}
