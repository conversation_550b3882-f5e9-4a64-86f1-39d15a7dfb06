import 'dart:io';

import 'package:flame/components.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:flame/rendering.dart' as frendering;
import 'package:simsushare_player/components/ContentDialog.dart';
import 'package:simsushare_player/components/MainButtons.dart';
import 'package:simsushare_player/components/MiniPlayer.dart';
import 'package:simsushare_player/controllers/ClipboardController.dart';
import 'package:simsushare_player/controllers/SimController.dart';
import 'package:simsushare_player/flame/Labels/builder.dart';
import 'package:simsushare_player/models/Mask.dart';
import 'package:simsushare_player/models/SimObjects.dart';
import 'package:simsushare_player/utils/constants.dart';

const _clipboardEntryTextStyleLarge = TextStyle(
  color: Colors.white,
  fontSize: 16,
);

const _clipboardEntryTextStyleSmall = TextStyle(
  color: Colors.white,
  fontSize: 12,
);

final _clipboardContainerBoxDecoration = BoxDecoration(
  border: Border.all(color: Colors.white, width: 2),
  borderRadius: BorderRadius.circular(10),
);

const standardWidth = 180.0;
const standardHeight = 180.0;

class ClipboardDialog extends StatelessWidget {
  ClipboardDialog({
    Key? key,
  }) : super(key: key);

  final hoverTarget = Rx<Object?>(null);

  @override
  Widget build(BuildContext context) {
    final _clipboardController = Get.find<ClipboardController>();
    return ContentDialog(
      title: "Clipboard",
      content: Obx(
        () => _clipboardController.simObjects.isEmpty && _clipboardController.masks.isEmpty
            ? const Center(
                child: Text(
                  "Clipboard is empty",
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 24,
                  ),
                ),
              )
            : SingleChildScrollView(
                child: Wrap(
                  spacing: 5,
                  children: [
                    ...[..._clipboardController.simObjects.toSet(), ..._clipboardController.masks.toSet()].map(
                      (simObj) {
                        Widget preview;
                        // print("Sim Object Type: ${simObj.runtimeType}");
                        switch (simObj.runtimeType) {
                          case SimSprite:
                            preview = Container(
                              width: standardWidth,
                              height: standardHeight,
                              decoration: _clipboardContainerBoxDecoration.copyWith(
                                border: Border.all(
                                    color: _clipboardController.selectedObjects.contains(simObj) ? brick : Colors.white,
                                    width: hoverTarget.value == simObj ? 4 : 2),
                              ),
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.spaceAround,
                                children: [
                                  // const Icon(Icons.animation, color: Colors.white, size: 24),
                                  MiniPlayer(width: 100, height: 100, sprite: simObj as SimSprite),
                                  Text(simObj.name, style: _clipboardEntryTextStyleLarge),
                                ],
                              ),
                            );
                            break;
                          case SimShape:
                          case SimLocationJumper:
                            preview = Container(
                              width: standardWidth,
                              height: standardHeight,
                              decoration: _clipboardContainerBoxDecoration.copyWith(
                                border: Border.all(
                                    color: _clipboardController.selectedObjects.contains(simObj) ? brick : Colors.white,
                                    width: hoverTarget.value == simObj ? 4 : 2),
                              ),
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.spaceAround,
                                children: [
                                  // TODO: add correct shape icon
                                  const Icon(Icons.crop_square_sharp, color: Colors.white, size: 24),
                                  Text("${(simObj as SimShape).shape} ${simObj.id}", style: _clipboardEntryTextStyleLarge),
                                ],
                              ),
                            );
                            break;
                          case SimText:
                            preview = Container(
                              width: standardWidth,
                              height: standardHeight,
                              decoration: _clipboardContainerBoxDecoration.copyWith(
                                border: Border.all(
                                    color: _clipboardController.selectedObjects.contains(simObj) ? brick : Colors.white,
                                    width: hoverTarget.value == simObj ? 4 : 2),
                              ),
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.spaceAround,
                                children: [
                                  const Icon(Icons.text_fields, color: Colors.white, size: 44),
                                  Text(
                                    (simObj as SimText).text,
                                    style: _clipboardEntryTextStyleLarge
                                      ..copyWith(color: simObj.filterColor, backgroundColor: simObj.backgroundColor),
                                  ),
                                ],
                              ),
                            );
                            break;
                          case SimImage:
                            preview = Container(
                              width: standardWidth,
                              height: standardHeight,
                              decoration: _clipboardContainerBoxDecoration.copyWith(
                                border: Border.all(
                                    color: _clipboardController.selectedObjects.contains(simObj) ? brick : Colors.white,
                                    width: hoverTarget.value == simObj ? 4 : 2),
                              ),
                              child: Image.file(
                                File((simObj as SimImage).path),
                                fit: BoxFit.cover,
                                color: simObj.filterColor,
                                colorBlendMode: BlendMode.srcATop,
                              ),
                            );
                            break;
                          case SimLabel:
                            preview = Container(
                              width: standardWidth,
                              height: standardHeight,
                              decoration: _clipboardContainerBoxDecoration.copyWith(
                                border: Border.all(
                                    color: _clipboardController.selectedObjects.contains(simObj) ? brick : Colors.white,
                                    width: hoverTarget.value == simObj ? 4 : 2),
                              ),
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.spaceAround,
                                children: [
                                  // const Icon(Icons.animation, color: Colors.white, size: 24),
                                  FutureBuilder<PositionComponent>(
                                    future: buildLabelFromType((simObj as SimLabel).type, variables: simObj.variables, size: Vector2(100, 100)),
                                    builder: (context, snapshot) {
                                      if (snapshot.hasData) {
                                        return ComponentMiniPlayer(
                                          component: snapshot.data!
                                            ..anchor = Anchor.topLeft
                                            ..decorator.addLast(frendering.PaintDecorator.tint(simObj.filterColor)),
                                          height: 100,
                                          width: 100,
                                        );
                                      }
                                      return const SizedBox();
                                    },
                                  ),
                                  Text(simObj.name, style: _clipboardEntryTextStyleLarge),
                                ],
                              ),
                            );
                            break;
                          case SimPerson:
                            preview = Container(
                              width: standardWidth,
                              height: standardHeight,
                              decoration: _clipboardContainerBoxDecoration.copyWith(
                                border: Border.all(
                                    color: _clipboardController.selectedObjects.contains(simObj) ? brick : Colors.white,
                                    width: hoverTarget.value == simObj ? 4 : 2),
                              ),
                              child: Image.asset(
                                "assets/people/${peopleToAssetMapping[(simObj as SimPerson).type]!}/${simObj.posture}.png",
                                fit: BoxFit.contain,
                                color: simObj.filterColor,
                                colorBlendMode: BlendMode.srcATop,
                              ),
                            );
                            break;
                          case SimContainer:
                            preview = Container(
                              width: standardWidth,
                              height: standardHeight,
                              decoration: _clipboardContainerBoxDecoration.copyWith(
                                border: Border.all(
                                    color: _clipboardController.selectedObjects.contains(simObj) ? brick : Colors.white,
                                    width: hoverTarget.value == simObj ? 4 : 2),
                              ),
                              child: Image.asset(
                                "assets/containers/${containerAssetsMapping[(simObj as SimContainer).type]!}/${simObj.view}.png",
                                fit: BoxFit.contain,
                                color: simObj.filterColor,
                                colorBlendMode: BlendMode.srcATop,
                              ),
                            );
                            break;
                          case SimTimer:
                            preview = Container(
                              width: standardWidth,
                              height: standardHeight,
                              decoration: _clipboardContainerBoxDecoration.copyWith(
                                border: Border.all(
                                    color: _clipboardController.selectedObjects.contains(simObj) ? brick : Colors.white,
                                    width: hoverTarget.value == simObj ? 4 : 2),
                              ),
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.spaceAround,
                                children: [
                                  const Icon(Icons.timer, color: Colors.white, size: 44),
                                  Text((simObj as SimTimer).id, style: _clipboardEntryTextStyleLarge),
                                ],
                              ),
                            );
                            break;
                          case Mask:
                            preview = preview = Container(
                              width: standardWidth,
                              height: standardHeight,
                              decoration: _clipboardContainerBoxDecoration.copyWith(
                                border: Border.all(
                                    color: _clipboardController.selectedMasks.contains(simObj) ? brick : Colors.white,
                                    width: hoverTarget.value == simObj ? 4 : 2),
                              ),
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.spaceAround,
                                children: [
                                  const Icon(Icons.theater_comedy_rounded, color: Colors.white, size: 44),
                                  Text((simObj as Mask).name, style: _clipboardEntryTextStyleLarge),
                                ],
                              ),
                            );
                            break;
                          default:
                            preview = Container(
                              width: standardWidth,
                              height: standardHeight,
                              decoration: _clipboardContainerBoxDecoration.copyWith(
                                border: Border.all(
                                    color: _clipboardController.selectedObjects.contains(simObj) ? brick : Colors.white,
                                    width: hoverTarget.value == simObj ? 4 : 2),
                              ),
                              child: Text("Invalid type: ${simObj.runtimeType}", style: _clipboardEntryTextStyleLarge),
                            );
                        }
                        return SizedBox(
                          width: hoverTarget.value == simObj ? 185 : 180,
                          height: hoverTarget.value == simObj ? 185 : 180,
                          child: MouseRegion(
                            child: GestureDetector(
                              child: Stack(
                                children: [
                                  preview,
                                  Visibility(
                                    child: Positioned(
                                      top: 0,
                                      right: 0,
                                      // height: standardHeight,
                                      // width: standardWidth,
                                      child: IconButton(
                                        icon: const Icon(Icons.close, color: Colors.white),
                                        onPressed: () {
                                          if (simObj is SimObject) {
                                            if (_clipboardController.selectedObjects.contains(simObj)) {
                                              _clipboardController.selectedObjects.remove(simObj);
                                            }
                                            _clipboardController.simObjects.remove(simObj);
                                          } else if (simObj is Mask) {
                                            if (_clipboardController.selectedMasks.contains(simObj)) {
                                              _clipboardController.selectedMasks.remove(simObj);
                                            }
                                            _clipboardController.masks.remove(simObj);
                                          } else {
                                            print("Trying to delete invalid type: ${simObj.runtimeType} from clipboard");
                                          }
                                        },
                                      ),
                                    ),
                                    visible: hoverTarget.value == simObj,
                                  ),
                                ],
                              ),
                              onTap: () {
                                if (simObj is SimObject) {
                                  if (_clipboardController.selectedObjects.contains(simObj)) {
                                    _clipboardController.selectedObjects.remove(simObj);
                                  } else {
                                    _clipboardController.selectedObjects.add(simObj);
                                  }
                                  _clipboardController.selectedObjects.refresh();
                                } else if (simObj is Mask) {
                                  if (_clipboardController.selectedMasks.contains(simObj)) {
                                    _clipboardController.selectedMasks.remove(simObj);
                                  } else {
                                    _clipboardController.selectedMasks.add(simObj);
                                  }
                                  _clipboardController.selectedMasks.refresh();
                                }
                              },
                            ),
                            onEnter: (event) {
                              hoverTarget.value = simObj;
                            },
                            onExit: (event) {
                              hoverTarget.value = null;
                            },
                          ),
                        );
                      },
                    ).toList(),
                  ],
                ),
              ),
      ),
      actions: [
        TransparentButton(
          label: "Cancel",
          onPressed: () {
            Get.back();
          },
        ),
        OrangeButton(
          label: "Paste",
          onPressed: () {
            final _simController = Get.find<SimController>();
            final maskOldToNew = <String, String>{};
            for (final mask in _clipboardController.selectedMasks) {
              final currentLocation = _simController.currentLocation.value;
              final _copy = mask.copy();
              _copy.name = "Mask_" + (_simController.getLargestObjectTypeInLocation(_simController.currentLocation.value, "mask") + 1).toString();
              _copy.locationId = _simController.currentSim.value!.locations[currentLocation].id;
              // simObj.clone();
              maskOldToNew[mask.id] = _copy.id;
              _simController.currentSim.value!.masks.add(_copy);
              _simController.currentSim.refresh();
            }
            for (final simObj in _clipboardController.selectedObjects) {
              // print("Pasting: $simObj");
              final oldMaskIds = [...simObj.maskIds];
              final _copy = simObj.copy(clearMasks: true) as SimObject;
              // final _copy = simObj.copy(clearMasks: false) as SimObject;
              _copy.maskIds = oldMaskIds.map((e) => maskOldToNew[e] ?? "").where((element) => element.isNotEmpty).toList();
              // simObj.clone();
              final copyType = getSimObjectType(_copy);
              if (["sprite", "label", "person", "container"].contains(copyType)) {
                List<String> split = [];
                print(
                    "Largest object of type $copyType is: ${_simController.getLargestObjectTypeInLocation(_simController.currentLocation.value, copyType!)}");
                switch (copyType) {
                  case "sprite":
                    split = (_copy as SimSprite).name.split("_");
                    split.removeAt(split.length - 1);
                    split.add((_simController.getLargestObjectTypeInLocation(_simController.currentLocation.value, copyType,
                                // overridePrefix: _copy.assetName) +
                                overridePrefix: split[0]) +
                            1)
                        .toString());
                    _copy.name = split.join("_");
                    break;
                  case "label":
                    split = (_copy as SimLabel).name.split("_");
                    split.removeAt(split.length - 1);
                    split.add((_simController.getLargestObjectTypeInLocation(_simController.currentLocation.value, copyType) + 1).toString());
                    _copy.name = split.join("_");
                    break;
                  case "person":
                    split = (_copy as SimPerson).name.split("_");
                    split.removeAt(split.length - 1);
                    split.add((_simController.getLargestObjectTypeInLocation(_simController.currentLocation.value, copyType) + 1).toString());
                    _copy.name = split.join("_");
                    break;
                  case "container":
                    split = (_copy as SimContainer).name.split("_");
                    split.removeAt(split.length - 1);
                    split.add((_simController.getLargestObjectTypeInLocation(_simController.currentLocation.value, copyType) + 1).toString());
                    _copy.name = split.join("_");
                    break;
                }
              }
              _simController.addSimObject(_copy);
              _simController.currentSim.refresh();
            }
            Get.back();
          },
        )
      ],
    );
  }
}
