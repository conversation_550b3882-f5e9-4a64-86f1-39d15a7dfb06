import 'dart:io';

import 'package:archive/archive_io.dart';
import 'package:collection/collection.dart';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart' hide FormData, MultipartFile;
import 'package:jwt_decoder/jwt_decoder.dart';
import 'package:nanoid/nanoid.dart';
import 'package:path_provider/path_provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:simsushare_player/components/ConfirmationDialog.dart';
import 'package:simsushare_player/components/ContentDialog.dart';
import 'package:simsushare_player/components/MainButtons.dart';
import 'package:simsushare_player/controllers/UserController.dart';
import 'package:simsushare_player/utils/constants.dart';
import 'package:simsushare_player/utils/sim_upload/web.dart';
import 'package:xml/xml.dart' as xml;

class UploadSimDialog extends StatelessWidget {
  final String? scenarioDirectoryPath;
  final bool fromSelection;

  UploadSimDialog({
    Key? key,
    this.scenarioDirectoryPath,
    this.fromSelection = false,
  }) : super(key: key);

  // final repo = (baseurl.split("/api").first).obs;
  final repo = "".obs;

  final repoUsername = "".obs;
  final repoPassword = "".obs;

  final repos = <String>[
    baseurl.replaceAll("https://", "").replaceAll("http://", "").replaceAll("/api", ""),
  ].obs;

  _initialize() async {
    final prefs = await SharedPreferences.getInstance();
    final _userController = Get.find<UserController>();
    // repos.value = prefs.getStringList("repos_${_userController.user.value["id"]}") ?? [];
    // repos.addAll(prefs.getStringList("repos") ?? []);
    if (_userController.user.value?["id"] == null) return;
    print("**** prefs: ${prefs.getStringList("repos_${_userController.user.value["id"]}")}");
    repos.addAll(prefs.getStringList("repos_${_userController.user.value["id"]}") ?? []);
    print("**** repos.length: ${repos.length}");
    repos.value = repos.toSet().toList(); // remove duplicates

    repos.refresh();
    repo.value = repos.isEmpty ? "" : repos.first;
  }

  @override
  Widget build(BuildContext context) {
    _initialize();
    final _userController = Get.find<UserController>();
    print("REPO TOKENS: ${_userController.repoToken.keys}");
    return ContentDialog(
        title: "Upload Sim",
        width: 550,
        height: 450,
        content: Column(
          children: [
            Row(
              children: [
                Expanded(
                  child: Obx(() {
                    return DropdownButton(
                      hint: const Text("Select Repository", style: TextStyle(color: Colors.white)),
                      value: repo.value.isEmpty ? null : repo.value,
                      dropdownColor: lightBackgrounds,
                      isExpanded: true,
                      items: repos.map(
                        (element) {
                          final token = _userController.repoToken.entries
                              .firstWhereOrNull(
                                  (entry) => element == entry.key.replaceAll("https://", "").replaceAll("http://", "").replaceAll("/api", ""))
                              ?.value;
                          String loggedInUser = "";
                          if (token != null) {
                            final payload = JwtDecoder.tryDecode(token);
                            if (payload == null) {
                              print("Invalid token $token");
                            } else {
                              loggedInUser = payload["name"];
                            }
                          }
                          return DropdownMenuItem(
                            child: Text(element + (loggedInUser.isNotEmpty ? " ($loggedInUser)" : ""), style: const TextStyle(color: Colors.white)),
                            value: element,
                          );
                        },
                      ).toList(),
                      onChanged: (String? value) {
                        if (value == null) return;
                        repo.value = value;
                      },
                    );
                  }),
                ),
              ],
            ),
            Obx(() {
              // if (_userController.repoToken.containsKey(repo.value) && _userController.repoToken[repo.value] != null) {
              if (_userController.repoToken.keys
                  .map((key) => key.replaceAll("https://", "").replaceAll("http://", "").replaceAll("/api", ""))
                  .toSet()
                  .contains(repo.value)) {
                return TransparentButton(
                  onPressed: () {
                    _userController.repoToken.remove(repo.value);
                  },
                  label: "Clear Credentials",
                );
              }
              return Column(
                children: [
                  const SizedBox(height: 10),
                  // username and password prompts
                  const SizedBox(height: 10),
                  TextField(
                    decoration: const InputDecoration(
                      hintText: "Username",
                      hintStyle: TextStyle(color: Colors.white60),
                    ),
                    style: const TextStyle(color: Colors.white),
                    onChanged: (value) {
                      repoUsername.value = value;
                    },
                  ),
                  const SizedBox(height: 10),
                  TextField(
                    decoration: const InputDecoration(
                      hintText: "Password",
                      hintStyle: TextStyle(color: Colors.white60),
                    ),
                    style: const TextStyle(color: Colors.white),
                    obscureText: true,
                    onChanged: (value) {
                      repoPassword.value = value;
                    },
                  ),
                ],
              );
            })
          ],
        ),
        actions: [
          TransparentButton(
            label: "Cancel",
            onPressed: () {
              Get.back();
            },
          ),
          Obx(
            () => repo.value.isEmpty
                ? const SizedBox()
                : OrangeButton(
                    label: "Upload",
                    onPressed: () async {
                      // Go back
                      Get.back();

                      if (_userController.repoToken[repo.value] == null) {
                        try {
                          final logInToken =
                              await _userController.loginRepo(repoUrl: repo.value, username: repoUsername.value, password: repoPassword.value);
                          _userController.repoToken[repo.value] = logInToken;
                          // final prefs = await SharedPreferences.getInstance();
                          // prefs.setStringList("repos_${_userController.user.value["id"]}", repos);
                        } catch (err) {
                          print(err);
                          Get.showSnackbar(const GetSnackBar(
                            title: "Error",
                            message: "Failed to login to repository",
                            duration: Duration(seconds: 3),
                          ));
                          return;
                        }
                      }

                      final repoToken = _userController.repoToken[repo.value];

                      // if (_userController.token.value == null || _userController.token.value!.isEmpty) {
                      if (repoToken == null) {
                        Get.showSnackbar(const GetSnackBar(
                          title: "Error",
                          message: "You need to login to upload scenarios",
                          duration: Duration(seconds: 3),
                        ));
                        return;
                      }
                      // Show "Upload Started" SnackBar for 2 seconds
                      await Future.delayed(const Duration(milliseconds: 500), () {
                        Get.showSnackbar(const GetSnackBar(
                          title: "Upload Started",
                          message: "Uploading Scenario",
                          duration: Duration(seconds: 2),
                        ));
                      });

                      if (kIsWeb) {
                        // scenarioDirectoryPath is zip file url in the web version
                        await webUploadSim(scenarioDirectoryPath!, repo);
                        return;
                      }

                      print("UPLOADING USING REPOTOKEN: $repoToken with from selection: $fromSelection");
                      final scenarioPaths = <String>[];
                      if (fromSelection) {
                        print("FROM SELECTION true. Adding multiple paths: ${_userController.selectedSims.values.map((ss) => ss.directoryPath)}");
                        scenarioPaths.addAll(_userController.selectedSims.values.map((ss) => ss.directoryPath));
                      } else {
                        print("FROM SELECTION false. Adding single path: $scenarioDirectoryPath");
                        if (scenarioDirectoryPath == null) {
                          Get.showSnackbar(const GetSnackBar(
                            title: "Error",
                            message: "Failed to get scenario directory",
                            duration: Duration(seconds: 3),
                          ));
                          return;
                        }
                        scenarioPaths.add(scenarioDirectoryPath!);
                      }
                      print("UPLOADING SCENARIO PATHS: $scenarioPaths");

                      // final prefs = await SharedPreferences.getInstance();
                      // final companyId = prefs.getString("companyId")!;
                      final companyId = JwtDecoder.decode(repoToken)["company"]["_id"];
                      if (companyId == null) {
                        Get.showSnackbar(const GetSnackBar(
                          title: "Error",
                          message: "Failed to get company ID",
                          duration: Duration(seconds: 3),
                        ));
                        return;
                      }
                      final scenariosResp = await dio.get(
                          "https://" +
                              repo.value.substring(0, repo.value.endsWith("/") ? repo.value.length - 1 : repo.value.length) +
                              "/api/scenarios/$companyId",
                          options: Options(headers: {"Authorization": repoToken}));
                      final scenarioNames = (scenariosResp.data["scenarios"] as List<dynamic>).map((e) => e["title"]);

                      final pendingFutures = <Future<bool> Function()>[];
                      final failureMessages = List<String>.filled(scenarioPaths.length, "");

                      for (int j = 0; j < scenarioPaths.length; j++) {
                        final scenarioDirectoryPath = scenarioPaths[j];
                        // Create "Temporary Directory" if not exists, then put the files that will be uploaded inside it
                        final dir = Directory(scenarioDirectoryPath).listSync().whereType<Directory>().first;
                        print("Dir:" + dir.toString());
                        final subdirName = dir.path.substring(dir.parent.path.length + 1);
                        final tempDir = Directory((await getTemporaryDirectory()).path + "/upload/" + subdirName);
                        if (tempDir.existsSync()) {
                          tempDir.deleteSync(recursive: true);
                        }
                        tempDir.createSync(recursive: true);
                        dir.listSync().toList().forEach((f) {
                          print("Looking into ${f.path}");
                          if (f is File) {
                            print("Copying ${f.path} to ${tempDir.path}/${f.path.substring(f.parent.path.length + 1)}}");
                            f.copySync(tempDir.path + "/${f.path.substring(f.parent.path.length + 1)}");
                          }
                        });

                        // Parse simdef.xml
                        final def = File(dir.path + "/" + "/simdef.xml").readAsStringSync();
                        final simdef = xml.XmlDocument.parse(def);

                        // Update simdef.xml file --> [simId, simName]
                        final simId = simdef.getElement("sim")?.getAttribute("id");
                        String simName = simdef.getElement("sim")!.getAttribute("title")!;
                        simName = (String name) {
                          if (!scenarioNames.contains(name)) return name;
                          var i = 1;
                          while (scenarioNames.contains("$name ($i)")) {
                            i++;
                          }
                          return "$name ($i)";
                        }(simName);
                        String uploadSimId = simId!;
                        bool forceCreate = false; // we add this so that we don't have to override the ID in order to create a new sim
                        bool overwrite = false;
                        final conflictSim =
                            scenariosResp.data["scenarios"].firstWhere((e) => e["id"] == simId /*  || e["title"] == simName */, orElse: () => null);
                        if (conflictSim != null) {
                          final overwriteConfirmation = await Get.dialog<bool>(
                            ConfirmationDialog(
                              message: "The sim \"$simName\" already exists in your repository. Do you wish to overwrite it?",
                              onConfirmed: () {
                                return true;
                              },
                              confirmLabel: "Yes, overwrite",
                              cancelLabel: "No, create new",
                            ),
                          );
                          print("Overwrite Confirmation: $overwriteConfirmation");
                          if (overwriteConfirmation == null) {
                            if (scenarioPaths.length == 1) {
                              await Future.delayed(const Duration(milliseconds: 500), () {
                                Get.showSnackbar(const GetSnackBar(
                                  title: "Upload Canceled",
                                  message: "Scenario not uploaded",
                                  duration: Duration(seconds: 2),
                                ));
                              });
                            }
                            tempDir.deleteSync(recursive: true);
                            continue;
                          }
                          if (overwriteConfirmation) {
                            // print("Using conflict sim: $conflictSim");
                            // TODO: this doesn't fix the problem for some reason
                            uploadSimId = conflictSim["_id"];
                            overwrite = true;
                          } else {
                            uploadSimId = nanoid(12);
                          }
                        } else {
                          forceCreate = true;
                        }
                        print("Upload Sim ID: $uploadSimId");
                        simdef.getElement("sim")!.setAttribute("id", uploadSimId);
                        // .setAttribute("id", "${simId}_${(DateTime.now().toUtc().toIso8601String().split(".")..removeLast()).first}");

                        // simdef
                        //     .getElement("sim")!
                        //     .setAttribute("title", "${simName}_${(DateTime.now().toUtc().toIso8601String().split(".")..removeLast()).first}");
                        simdef.getElement("sim")!.setAttribute("title", simName);
                        final updatedSimdef = simdef.toXmlString(pretty: true, indent: "  ");

                        // Update simdef.xml file inside Temporary Directory
                        File(tempDir.path + "/simdef.xml").writeAsStringSync(updatedSimdef, mode: FileMode.write, flush: true);

                        // Create Zip file at "AppDocumentDirectory" based on "tempDir.parent" directory
                        final zipEncoder = ZipFileEncoder();
                        // Note: This method "zipDirectory" create the zipped file inside the project directory !!
                        // that's why we created "moveFileToAppDocumentDirectory" mehtod, to move file from "project directory" to "AppDocumentDirectory"
                        // print("Zipping ${tempDir.parent}");
                        zipEncoder.zipDirectory(tempDir.parent, filename: "${dir.path.substring(dir.parent.path.length + 1)}.zip");
                        print("Zipped to ${zipEncoder.zipPath}");
                        var zippedFile = File(Directory.current.path + "/${zipEncoder.zipPath}");
                        zippedFile = await moveFileToAppDocumentDirectory(zippedFile, "/${zipEncoder.zipPath}");

                        pendingFutures.add(() async {
                          _userController.totalUploads.value++;
                          // print("Zip location: ${zippedFile.path}");
                          // Start Uploading Zipped simdef file
                          print("Uploading $scenarioDirectoryPath");
                          // print("$uploadSimId (is equal to ${uploadSimId == simId}) $simId and forceCreate: $forceCreate");
                          // print("URL: https://" +
                          //     repo.value.substring(0, repo.value.endsWith("/") ? repo.value.length - 1 : repo.value.length) +
                          //     '/api/scenarios/${_userController.user.value["company"]["_id"]}${uploadSimId == simId && !forceCreate ? "/$uploadSimId" : ""}');
                          print("TARGET METHOD: ${overwrite ? "PUT" : "POST"} & URL: https://" +
                              repo.value.substring(0, repo.value.endsWith("/") ? repo.value.length - 1 : repo.value.length) +
                              '/api/scenarios/$companyId${(uploadSimId == simId && !forceCreate) || overwrite ? "/$uploadSimId" : ""}');
                          final response = await Dio(BaseOptions(
                            validateStatus: (status) => true,
                            method: overwrite ? "PUT" : "POST",
                            connectTimeout: const Duration(seconds: 5),
                          )).request(
                            "https://" +
                                repo.value.substring(0, repo.value.endsWith("/") ? repo.value.length - 1 : repo.value.length) +
                                '/api/scenarios/$companyId${(uploadSimId == simId && !forceCreate) || overwrite ? "/$uploadSimId" : ""}', // create if new, update if overwrite
                            data: FormData.fromMap(
                              {
                                "scenario": await MultipartFile.fromFile(zippedFile.path,
                                    filename: zippedFile.path.substring(zippedFile.parent.path.length + 1)),
                              },
                            ),
                            options: Options(
                              headers: {
                                // "Authorization": _userController.token.value,
                                "Authorization": repoToken,
                              },
                            ),
                          );

                          // When upload is completed, delete temporary directory
                          print("Upload complete $scenarioDirectoryPath");
                          tempDir.deleteSync(recursive: true);
                          // should delete zip file too
                          zippedFile.deleteSync();

                          _userController.completedUploads.value++;
                          // If it's failed to upload scenario, then sow snackbar message for 3 seconds
                          if (response.statusCode == null || response.statusCode! >= 400) {
                            failureMessages[j] = "Failed to upload scenario: ${response.data}";
                            print("Failed to upload scenario: ${response.statusCode} ${response.data}");
                            // Get.showSnackbar(const GetSnackBar(
                            //   title: "Error",
                            //   message: "Failed to upload scenario",
                            //   duration: Duration(seconds: 3),
                            // ));
                            return false;
                          }
                          return true;
                        });
                      }

                      // if (uploadResult.where((r) => r == false).isNotEmpty) {
                      //   print("Upload result returned false");
                      //   return;
                      // }
                      final results = await Future.wait(pendingFutures.map((f) => f()));

                      _userController.totalUploads.value -= results.length;
                      _userController.completedUploads.value -= results.length;

                      final failures = results.where((result) => !result).length;
                      // if they all fail
                      if (failures == pendingFutures.length) {
                        Get.showSnackbar(GetSnackBar(
                          title: "Error",
                          message: "Failed to upload scenario${failures > 1 ? "s" : ""}${failures == 1 ? " (" + failureMessages[0] + ")" : ""}",
                          duration: const Duration(seconds: 5),
                        ));
                        return;
                      }

                      // If it's succedded to upload scenario, then sow snackbar message for 3 seconds
                      Get.showSnackbar(GetSnackBar(
                        title: "${failures > 1 ? "Partial " : ""}Success",
                        message: "${results.length - failures} Scenario${results.length - failures > 1 ? "s" : ""} Uploaded",
                        duration: const Duration(seconds: 3),
                      ));
                    },
                  ),
          ),
        ]);
  }

  Future<File> moveFileToAppDocumentDirectory(File zippedFile, String zipPath) async {
    var newPath = (await getApplicationDocumentsDirectory()).parent.path + zipPath;
    try {
      // prefer using rename as it is probably faster
      return await zippedFile.rename(newPath);
    } on FileSystemException catch (e) {
      // if rename fails, copy the source file and then delete it
      print(e);
      final newFile = await zippedFile.copy(newPath);
      await zippedFile.delete();
      return newFile;
    }
  }
}
