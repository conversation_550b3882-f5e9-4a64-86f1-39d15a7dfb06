// ignore_for_file: must_be_immutable

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:simsushare_player/components/MainButtons.dart';
import 'package:simsushare_player/models/SimRepo.dart';
import 'package:simsushare_player/utils/constants.dart';

class UploadSimulations extends StatelessWidget {
  UploadSimulations({
    Key? key,
  }) : super(key: key);

  RxList<SimRepo> repos = RxList.empty(growable: true);

  _initialize() {}

  @override
  Widget build(BuildContext context) {
    _initialize();
    return Scaffold(
      appBar: AppBar(
        leading: SizedBox(
          width: 38,
          height: 38,
          child: IconButton(
            onPressed: () {
              Get.back();
            },
            icon: const Icon(Icons.chevron_left),
            iconSize: 12,
          ),
        ),
        title: const Text("Upload Simulation"),
      ),
      body: Container(
        padding: const EdgeInsets.symmetric(vertical: 24),
        alignment: Alignment.topCenter,
        child: SizedBox(
          width: Get.width < 500 ? Get.width * 0.95 : Get.width * 0.7,
          child: Obx(
            () => Column(
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    const Text("Connect to Sim Repository"),
                    OrangeButton(
                      onPressed: () {},
                      label: "Add Repository",
                      icon: Icons.add_outlined,
                    )
                  ],
                ),
                ...repos
                    .map(
                      (repo) => Container(
                        decoration: BoxDecoration(color: lightBackgrounds, borderRadius: BorderRadius.circular(6)),
                        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 26),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(repo.name),
                            Row(
                              children: [
                                TextButton(
                                  onPressed: () {},
                                  child: const Row(
                                    children: [
                                      Icon(Icons.edit),
                                      Text("Edit"),
                                    ],
                                  ),
                                ),
                                TextButton(
                                  onPressed: () {},
                                  child: const Text(
                                    "Connect",
                                    style: TextStyle(color: yellow),
                                  ),
                                )
                              ],
                            )
                          ],
                        ),
                      ),
                    )
                    .toList()
              ],
            ),
          ),
        ),
      ),
    );
  }
}
