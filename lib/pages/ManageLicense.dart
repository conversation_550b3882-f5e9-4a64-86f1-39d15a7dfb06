import 'package:flutter/material.dart';
import 'package:simsushare_player/components/MainPageContainer.dart';

class ManageLicense extends StatelessWidget {
  const ManageLicense({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return MainPageContainer(
        title: "Manage License",
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Text(
              "Your license does not expire, sweet!",
              style: TextStyle(fontSize: 24, fontWeight: FontWeight.w500),
            ),
            Container(
              padding: const EdgeInsets.symmetric(vertical: 20),
              child: ElevatedButton(
                child: const Text("Extend Subscription"),
                onPressed: () {},
                style: ElevatedButton.styleFrom(fixedSize: const Size(450, 50)),
              ),
            ),
            ElevatedButton(
              child: const Text("Deactivate on this device"),
              onPressed: () {},
              style: ElevatedButton.styleFrom(fixedSize: const Size(450, 50)),
            ),
          ],
        ));
  }
}
