// ignore_for_file: must_be_immutable

import 'dart:async';
import 'dart:io';
import 'dart:ui' as ui;

import 'package:collection/collection.dart';
import 'package:easy_debounce/easy_debounce.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:fuzzywuzzy/model/extracted_result.dart';
import 'package:get/get.dart';
import 'package:fuzzywuzzy/fuzzywuzzy.dart';
import 'package:simsushare_player/components/ClipboardDialog.dart';
import 'package:simsushare_player/components/ConfirmationDialog.dart';
import 'package:simsushare_player/components/SelectSingleImageDialog.dart';
import 'package:simsushare_player/components/WalkaroundMakerDialog.dart';
import 'package:simsushare_player/controllers/SimController.dart';
import 'package:simsushare_player/models/Mask.dart';
import 'package:simsushare_player/models/SimObjects.dart';
import 'package:simsushare_player/pages/NewScenarioWithPlayer/helpers/helper_methods.dart';
import 'package:simsushare_player/pages/NewScenarioWithPlayer/helpers/helper_widgets.dart';
import 'package:simsushare_player/pages/NewScenarioWithPlayer/helpers/optionBuilder.dart';
import 'package:simsushare_player/pages/NewScenarioWithPlayer/helpers/optionHandler.dart';
import 'package:simsushare_player/pages/Preview.dart';
import 'package:simsushare_player/utils/constants.dart';
import 'package:simsushare_player/utils/parsers.dart';

class NewScenarioTopbar extends StatelessWidget {
  final SimController simController;
  // RxBool recorderVisible;
  final categoryChoices = <List<ExtractedResult<String>>>[].obs;
  final searchElement = "".obs;
  final categories = [
    "Fire",
    "Smoke",
    "Containers",
    "Explosions",
    "HazMat",
    "Labels",
    "People",
  ];

  NewScenarioTopbar({
    Key? key,
    required this.simController,
    // required this.recorderVisible,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final searchBarFocusNode = FocusNode();
    // final searchBarTextController = TextEditingController();
    final searchController = SearchController();
    searchController.addListener(() {
      print(
        "-----> Search controller text: ${searchController.text}, isOpen: ${searchController.isOpen}, isAttached: ${searchController.isAttached}",
      );

      if (searchController.text.isNotEmpty && !searchController.isOpen) {
        searchController.openView();
      }
    });
    searchBarFocusNode.addListener(() {
      if (!searchBarFocusNode.hasFocus && searchController.isOpen) {
        searchBarFocusNode.requestFocus();
      }
    });

    return Container(
      decoration: const BoxDecoration(color: mainBackgrounds, border: Border(bottom: BorderSide(color: white60))),
      padding: context.isMobileScreen ? const EdgeInsets.symmetric(horizontal: 12) : const EdgeInsets.symmetric(vertical: 16, horizontal: 24),
      child: SingleChildScrollView(
        scrollDirection: Axis.vertical,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            //Back arrow
            Row(
              children: [
                TextButton(
                  child: Container(
                    decoration: BoxDecoration(border: Border.all(color: white60), borderRadius: BorderRadius.circular(50)),
                    padding: context.isMobileScreen
                        ? const EdgeInsets.symmetric(vertical: 6, horizontal: 6)
                        : const EdgeInsets.symmetric(vertical: 13, horizontal: 13),
                    child: Icon(
                      Icons.arrow_back_ios,
                      color: Colors.white,
                      size: context.isMobileScreen ? 10 : 12,
                    ),
                  ),
                  onPressed: () async {
                    handleConfirm() async {
                      // Get.back(closeOverlays: true);
                      if (simController.autosave.value) {
                        simController.currentSim.refresh();
                        simController.signalStream.add("autosave");
                        await Future.delayed(const Duration(milliseconds: 200));
                      }
                      Get.offAllNamed("/home");
                      return Future.delayed(Duration(milliseconds: Get.defaultTransitionDuration.inMilliseconds + 300), () async {
                        /* if (simController.autosave.value) {
                          simController.currentSim.refresh();
                          simController.signalStream.add("autosave");
                          await Future.delayed(const Duration(milliseconds: 200));
                        } */
                        simController.reset();
                      });
                    }

                    if (simController.saved.value || simController.autosave.value) {
                      handleConfirm();
                      return;
                    }
                    await Get.dialog(
                      ConfirmationDialog(
                        message: "Are you sure you want to go back. Any unsaved progress will be lost",
                        onConfirmed: () {
                          print("Confirming going back without saving");
                          handleConfirm();
                          // Get.back(closeOverlays: true);
                        },
                      ),
                    );
                  },
                ),
                const SizedBox(width: 10),
                Text(
                  simController.currentSim.value!.name,
                  style: TextStyle(fontSize: context.isMobileScreen ? 10 : 14, color: Colors.white),
                ),
              ],
            ),

            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                //Element
                PopupMenuButton(
                  child: const Column(
                    children: [
                      Icon(Icons.animation, color: Colors.white),
                      Text("Element", style: TextStyle(color: Colors.white)),
                    ],
                  ),
                  position: PopupMenuPosition.under,
                  padding: context.isMobileScreen ? EdgeInsets.zero : const EdgeInsets.all(8.0),
                  color: sidebarDark,
                  itemBuilder: (context) {
                    const widthAndHeight = 24.0;
                    const categories = ["Fire", "Smoke", "Explosions", "Labels", "Containers", "HazMat", "People"];
                    final categoryIcons = [
                      Image.asset(
                        "assets/images/new_element/icons8-fire-48 1.png",
                        width: widthAndHeight,
                        height: widthAndHeight,
                      ),
                      Image.asset(
                        "assets/images/new_element/icons8-smoke-48 1.png",
                        width: widthAndHeight,
                        height: widthAndHeight,
                      ),
                      Image.asset(
                        "assets/images/new_element/icons8-explosion-48 1.png",
                        width: widthAndHeight,
                        height: widthAndHeight,
                      ),
                      Image.asset(
                        "assets/images/new_element/icons8-error-48 1.png",
                        width: widthAndHeight,
                        height: widthAndHeight,
                      ),
                      Image.asset(
                        "assets/images/new_element/icons8-oil-drum-96 1.png",
                        width: widthAndHeight,
                        height: widthAndHeight,
                      ),
                      Image.asset(
                        "assets/images/new_element/icons8-dashing-away-96 1.png",
                        width: widthAndHeight,
                        height: widthAndHeight,
                      ),
                      Image.asset(
                        "assets/images/new_element/icons8-man-light-skin-tone-96 1.png",
                        width: widthAndHeight,
                        height: widthAndHeight,
                      ),
                    ];
                    return categories
                        .mapIndexed(
                          (index, category) => PopupMenuItem(
                            onTap: () {
                              print("Tapped popup menu: $category");
                            },
                            child: MouseRegion(
                              onHover: (event) {},
                              child: PopupMenuButton(
                                offset: ui.Offset(context.isMobileScreen ? 100 : 150, 0),
                                color: sidebarDark,
                                itemBuilder: (context) => spriteCategoryMapping[category]!
                                    .keys
                                    // .whereIndexed((index, spriteName) => spriteCategoryMapping[category]!.values.elementAt(index).isNotEmpty)
                                    .map(
                                      (spriteName) => PopupMenuItem(
                                        child: buildMenuOption(category: category, optionName: spriteName),
                                        onTap: buildOptionHandler(category: category, optionName: spriteName),
                                      ),
                                    )
                                    .toList(),
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.spaceAround,
                                  children: [
                                    categoryIcons[index],
                                    Text(
                                      categories[index],
                                      style: TextStyle(fontSize: context.isMobileScreen ? 10 : 14, color: Colors.white),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                        )
                        .toList();
                  },
                ),
                const SizedBox(
                  width: 8,
                ),
                PopupMenuButton(
                  child: const Column(
                    children: [
                      Icon(Icons.architecture, color: Colors.white),
                      Text("Utilities", style: TextStyle(color: Colors.white)),
                    ],
                  ),
                  position: PopupMenuPosition.under,
                  // padding: context.isMobileScreen ? EdgeInsets.zero : const EdgeInsets.all(8.0),
                  color: sidebarDark,
                  constraints: const BoxConstraints(
                    minWidth: 2.5 * 56.0,
                    maxWidth: 5.0 * 56.0,
                  ),
                  itemBuilder: (context) {
                    return [
                      PopupMenuItem(
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceAround,
                          children: [
                            const Icon(Icons.text_fields, color: Colors.white),
                            Text("Text", style: TextStyle(fontSize: context.isMobileScreen ? 10 : 14, color: Colors.white)),
                          ],
                        ),
                        onTap: () {
                          simController.currentSim.value!.locations[simController.currentLocation.value].texts.add(
                            SimText(x: 150, y: 150)..priority = getHighestPriorityObject().priority + 1,
                          );
                          simController.currentSim.refresh();
                          simController.selectedSimObjectIndex.value =
                              simController.currentSim.value!.locations[simController.currentLocation.value].texts.length - 1;
                          simController.selectedType.value = SimObjectType.text;
                        },
                      ),
                      PopupMenuItem(
                        child: const Row(
                          mainAxisAlignment: MainAxisAlignment.spaceAround,
                          children: [
                            Icon(Icons.timer, color: Colors.white),
                            Text("Timer", style: TextStyle(color: Colors.white)),
                          ],
                        ),
                        onTap: () {
                          final lastTimerIndex = int.tryParse(simController.currentSim.value!.locations[simController.currentLocation.value].timers
                                      .where((timer) => timer.id.startsWith("Timer_"))
                                      .sorted((a, b) => int.parse(a.id.split("Timer_")[1]) - int.parse(b.id.split("Timer_")[1]))
                                      .lastOrNull
                                      ?.id
                                      .split("Timer_")[1] ??
                                  "") ??
                              0;
                          simController.currentSim.value!.locations[simController.currentLocation.value].timers.add(
                            SimTimer(
                              id: "Timer_${lastTimerIndex + 1}",
                              type: SimTimerType.exercise,
                              format: SimTimerFormat.minuteSeconds,
                              seconds: 0,
                              x: 150,
                              y: 150,
                            )..priority = getHighestPriorityObject().priority + 1,
                          );
                          simController.currentSim.refresh();
                          simController.selectedSimObjectIndex.value =
                              simController.currentSim.value!.locations[simController.currentLocation.value].timers.length - 1;
                          simController.selectedType.value = SimObjectType.timer;
                        },
                      ),
                      PopupMenuItem(
                        child: const Row(
                          mainAxisAlignment: MainAxisAlignment.spaceAround,
                          children: [
                            Icon(Icons.add_photo_alternate_outlined, color: Colors.white),
                            Text("Picture", style: TextStyle(color: Colors.white)),
                          ],
                        ),
                        onTap: () async {
                          String? image = await Get.dialog(SelectSingleImageDialog(
                            title: "Import picture",
                          ));
                          if (image == null) return;
                          final file = File(image);
                          ui.Image img;
                          if (kIsWeb) {
                            img = await decodeImageFromList(await file.readAsBytes());
                          } else {
                            img = await decodeImageFromList(await File(file.path).readAsBytes());
                          }
                          final initialSize = getInitialSize();
                          final newSuffix = simController.getLargestObjectTypeInLocation(simController.currentLocation.value, "image") + 1;
                          simController.currentSim.value!.locations[simController.currentLocation.value].images.add(SimImage(
                            id: "Image_$newSuffix",
                            img: img,
                            path: file.path,
                            x: 150,
                            y: 150,
                            width: img.width.toDouble(),
                            height: img.height.toDouble(),
                            scale: initialSize / img.width.toDouble(),
                          )..priority = getHighestPriorityObject().priority + 1);
                          simController.currentSim.refresh();
                        },
                      )
                    ];
                  },
                ),
                const SizedBox(
                  width: 8,
                ),
                /* //Text
                TextButton(
                  child: Column(
                    children: [
                      const Icon(Icons.text_fields, color: Colors.white),
                      Text("Text", style: TextStyle(fontSize: context.isMobileScreen ? 10 : 14, color: Colors.white)),
                    ],
                  ),
                  onPressed: () {
                    simController.currentSim.value!.locations[simController.currentLocation.value].texts.add(
                      SimText(x: 150, y: 150)..priority = getHighestPriorityObject().priority + 1,
                    );
                    simController.currentSim.refresh();
                    simController.selectedSimObjectIndex.value =
                        simController.currentSim.value!.locations[simController.currentLocation.value].texts.length - 1;
                    simController.selectedType.value = SimObjectType.text;
                  },
                ),
                // Timer
                TextButton(
                  child: const Column(
                    children: [
                      Icon(Icons.timer, color: Colors.white),
                      Text("Timer", style: TextStyle(color: Colors.white)),
                    ],
                  ),
                  onPressed: () {
                    final lastTimerIndex = int.tryParse(simController.currentSim.value!.locations[simController.currentLocation.value].timers
                                .where((timer) => timer.id.startsWith("Timer_"))
                                .sorted((a, b) => int.parse(a.id.split("Timer_")[1]) - int.parse(b.id.split("Timer_")[1]))
                                .lastOrNull
                                ?.id
                                .split("Timer_")[1] ??
                            "") ??
                        0;
                    simController.currentSim.value!.locations[simController.currentLocation.value].timers.add(
                      SimTimer(
                        id: "Timer_${lastTimerIndex + 1}",
                        type: SimTimerType.exercise,
                        format: SimTimerFormat.minuteSeconds,
                        seconds: 0,
                        x: 150,
                        y: 150,
                      )..priority = getHighestPriorityObject().priority + 1,
                    );
                    simController.currentSim.refresh();
                    simController.selectedSimObjectIndex.value =
                        simController.currentSim.value!.locations[simController.currentLocation.value].timers.length - 1;
                    simController.selectedType.value = SimObjectType.timer;
                  },
                ), */
                //Shape
                PopupMenuButton(
                  child: const Column(
                    children: [
                      Icon(Icons.crop_square_sharp, color: Colors.white),
                      Text("Shape", style: TextStyle(color: Colors.white)),
                    ],
                  ),
                  onSelected: (value) {
                    String shape = "";
                    double x = 150;
                    double y = 150;
                    double widthScale = 1;
                    double heightScale = 1;
                    switch (value) {
                      case "rectangle":
                      case "black-windows":
                      case "square":
                      case "circle":
                      case "triangle":
                      case "arrow":
                      case "rounded-rectangle":
                        shape = value;
                        break;
                      default:
                        print("Invalid selected value $value");
                        return;
                    }
                    if (value == "black-windows") {
                      widthScale = 1.5;
                    }
                    if (value == "rectangle" || value == "rounded-rectangle") {
                      heightScale = 1.5;
                    }
                    // get the shape with the largest id
                    final newSuffix = simController.getLargestObjectTypeInLocation(simController.currentLocation.value, "shape") + 1;

                    // simController.currentSim.value!.locations[simController.currentLocation.value].shapes.
                    simController.currentSim.value!.locations[simController.currentLocation.value].shapes.add(
                      SimShape(shape: shape, id: "Shape_$newSuffix", x: x, y: y, widthScale: widthScale, heightScale: heightScale)
                        ..priority = getHighestPriorityObject().priority + 1
                        ..filterColor = value == "black-windows" ? Colors.black : Colors.white,
                    );
                    simController.currentSim.refresh();
                  },
                  position: PopupMenuPosition.under,
                  offset: const Offset(0, 15),
                  color: lightBackgrounds,
                  itemBuilder: (context) => [
                    //rectangle
                    PopupMenuItem(
                      child: Row(
                        children: [
                          Expanded(
                            child: Column(
                              children: [
                                const Icon(Icons.rectangle_outlined, color: Colors.white),
                                Text(
                                  "Rectangle",
                                  style: TextStyle(fontSize: context.isMobileScreen ? 10 : 14, color: Colors.white),
                                )
                              ],
                            ),
                          )
                        ],
                      ),
                      value: "rectangle",
                    ),
                    // Square
                    PopupMenuItem(
                      child: Row(
                        children: [
                          Expanded(
                            child: Column(
                              children: [
                                const Icon(Icons.square_outlined, color: Colors.white),
                                Text(
                                  "Square",
                                  style: TextStyle(fontSize: context.isMobileScreen ? 10 : 14, color: Colors.white),
                                )
                              ],
                            ),
                          )
                        ],
                      ),
                      value: "square",
                    ),
                    //Circle
                    PopupMenuItem(
                      child: Row(
                        children: [
                          Expanded(
                            child: Column(
                              children: [
                                const Icon(Icons.circle_outlined, color: Colors.white),
                                Text(
                                  "Circle",
                                  style: TextStyle(fontSize: context.isMobileScreen ? 10 : 14, color: Colors.white),
                                )
                              ],
                            ),
                          )
                        ],
                      ),
                      value: "circle",
                    ),
                    //Triangle
                    PopupMenuItem(
                      child: Row(
                        children: [
                          Expanded(
                            child: Column(
                              children: [
                                const Icon(Icons.change_history, color: Colors.white),
                                Text(
                                  "Triangle",
                                  style: TextStyle(fontSize: context.isMobileScreen ? 10 : 14, color: Colors.white),
                                )
                              ],
                            ),
                          )
                        ],
                      ),
                      value: "triangle",
                    ),
                    //Arrow
                    PopupMenuItem(
                      child: Row(
                        children: [
                          Expanded(
                            child: Column(
                              children: [
                                const Icon(Icons.arrow_forward_sharp, color: Colors.white),
                                Text(
                                  "Arrow",
                                  style: TextStyle(fontSize: context.isMobileScreen ? 10 : 14, color: Colors.white),
                                )
                              ],
                            ),
                          )
                        ],
                      ),
                      value: "arrow",
                    ),
                    // rounded-rectangle
                    PopupMenuItem(
                      child: Row(
                        children: [
                          Expanded(
                            child: Column(
                              children: [
                                const Icon(Icons.rounded_corner_outlined, color: Colors.white),
                                Text(
                                  "Rounded Rectangle",
                                  style: TextStyle(fontSize: context.isMobileScreen ? 10 : 14, color: Colors.white),
                                )
                              ],
                            ),
                          )
                        ],
                      ),
                      value: "rounded-rectangle",
                    ),
                    // black-windows
                    PopupMenuItem(
                      child: Row(
                        children: [
                          Expanded(
                            child: Column(
                              children: [
                                const Icon(Icons.rectangle_rounded, color: Colors.white),
                                Text(
                                  "Black Window",
                                  style: TextStyle(fontSize: context.isMobileScreen ? 10 : 14, color: Colors.white),
                                )
                              ],
                            ),
                          )
                        ],
                      ),
                      value: "black-windows",
                    ),
                  ],
                ),
                const SizedBox(width: 10),
                //Mask
                PopupMenuButton(
                  child: Column(
                    children: [
                      const Icon(Icons.copy_all_rounded, color: Colors.white),
                      Text("Mask", style: TextStyle(fontSize: context.isMobileScreen ? 10 : 14, color: Colors.white)),
                    ],
                  ),
                  position: PopupMenuPosition.under,
                  offset: const Offset(0, 15),
                  color: lightBackgrounds,
                  itemBuilder: (context) => [
                    const PopupMenuItem(
                      child: Row(
                        children: [
                          Icon(Icons.timeline_outlined, color: Colors.white),
                          SizedBox(width: 9),
                          Text("Mask", style: TextStyle(color: Colors.white)),
                        ],
                      ),
                      value: "free",
                    ),
                    const PopupMenuItem(
                      child: Row(
                        children: [
                          Icon(Icons.crop_free_outlined, color: Colors.white),
                          SizedBox(width: 9),
                          Text("Mask", style: TextStyle(color: Colors.white)),
                        ],
                      ),
                      value: "box",
                    ),
                  ],
                  onSelected: (value) {
                    // simController.newMask.value = Mask(type: MaskType.showWithin);
                    final sim = simController.currentSim.value!;
                    simController.selectedSimObjectIndex.value = -1;
                    simController.selectedType.value = null;
                    switch (value) {
                      case "free":
                        simController.newMask.value = Mask(
                          type: MaskType.showOutside,
                          locationId: sim.locations[simController.currentLocation.value].id,
                        );
                        simController.newMask.refresh();
                        break;
                      case "box":
                        sim.masks.add(
                          Mask(
                            type: MaskType.showOutside,
                            locationId: sim.locations[simController.currentLocation.value].id,
                            coordinates: [
                              Coordinate(0.15, 0.15),
                              Coordinate(0.30, 0.15),
                              Coordinate(0.30, 0.30),
                              Coordinate(0.15, 0.30),
                            ],
                          ),
                        );
                        simController.currentSim.refresh();
                        break;
                      default:
                        print("Invalid selected mask value: $value");
                    }
                  },
                ),
                /* TextButton(
                  child: Column(
                    children: [
                      const Icon(Icons.copy_all_rounded, color: Colors.white),
                      Text("Mask", style: TextStyle(fontSize: context.isMobileScreen ? 10 : 14, color: Colors.white)),
                    ],
                  ),
                  onPressed: () {
                    // simController.newMask.value = Mask(type: MaskType.showWithin);
                    simController.selectedSimObjectIndex.value = -1;
                    simController.selectedType.value = null;
                    simController.newMask.value = Mask(
                      type: MaskType.showOutside,
                      locationId: simController.currentSim.value!.locations[simController.currentLocation.value].id,
                    );
                    simController.newMask.refresh();
                  },
                ), */
                //Picture
                /* TextButton(
                  onPressed: () async {
                    String? image = await Get.dialog(SelectSingleImageDialog(
                      title: "Import picture",
                      breakpoint: breakpoint,
                    ));
                    if (image == null) return;
                    final file = File(image);
                    ui.Image img;
                    if (kIsWeb) {
                      img = await decodeImageFromList(await file.readAsBytes());
                    } else {
                      img = await decodeImageFromList(await File(file.path).readAsBytes());
                    }
                    final initialSize = getInitialSize();
                    final newSuffix = simController.getLargestObjectTypeInLocation(simController.currentLocation.value, "image") + 1;
                    simController.currentSim.value!.locations[simController.currentLocation.value].images.add(SimImage(
                      id: "Image_$newSuffix",
                      img: img,
                      path: file.path,
                      x: 150,
                      y: 150,
                      width: img.width.toDouble(),
                      height: img.height.toDouble(),
                      scale: initialSize / img.width.toDouble(),
                    )..priority = getHighestPriorityObject().priority + 1);
                    simController.currentSim.refresh();
                  },
                  child: const Column(
                    children: [
                      Icon(Icons.add_photo_alternate_outlined, color: Colors.white),
                      Text("Picture", style: TextStyle(color: Colors.white)),
                    ],
                  ),
                ), */
                //Clipboard
                TextButton(
                  onPressed: () {
                    Get.dialog(ClipboardDialog());
                  },
                  child: Column(
                    children: [
                      const Icon(Icons.grid_view_outlined, color: Colors.white),
                      Text("Clipboard", style: TextStyle(fontSize: context.isMobileScreen ? 10 : 14, color: Colors.white)),
                    ],
                  ),
                ),
                //More [audio - record - clear - clipboard - copy - copyAll - walkaround - clear navigation]
                PopupMenuButton(
                  onSelected: (value) async {
                    switch (value) {
                      case "audio":
                        await Get.dialog(const SelectAudioSource());
                        break;
                      case "record":
                        simController.selectedType.value = null;
                        simController.selectedSimObjectIndex.value = -1;
                        // recorderVisible.value = true;
                        simController.signalStream.add("recorder-visible-toggle");
                        break;
                      case "clear":
                        Get.dialog(
                          ConfirmationDialog(
                            message: "Are you sure you want to clear all elements?",
                            onConfirmed: (() {
                              simController.currentSim.value!.locations[simController.currentLocation.value].images.removeWhere((element) => true);
                              simController.currentSim.value!.locations[simController.currentLocation.value].shapes.removeWhere((element) => true);
                              simController.currentSim.value!.locations[simController.currentLocation.value].sounds.removeWhere((element) => true);
                              simController.currentSim.value!.locations[simController.currentLocation.value].sprites.removeWhere((element) => true);
                              simController.currentSim.value!.locations[simController.currentLocation.value].texts.removeWhere((element) => true);
                              simController.currentSim.value!.locations[simController.currentLocation.value].jumpers.removeWhere((element) => true);
                              simController.currentSim.value!.locations[simController.currentLocation.value].labels.removeWhere((element) => true);
                              simController.currentSim.value!.locations[simController.currentLocation.value].containers
                                  .removeWhere((element) => true);
                              simController.currentSim.value!.locations[simController.currentLocation.value].people.removeWhere((element) => true);
                              simController.currentSim.value!.locations[simController.currentLocation.value].timers.removeWhere((element) => true);
                              simController.currentSim.value!.masks.removeWhere(
                                  (mask) => mask.locationId == simController.currentSim.value!.locations[simController.currentLocation.value].id);
                              simController.selectedType.value = null;
                              simController.selectedSimObjectIndex.value = -1;
                              simController.currentSim.refresh();
                            }),
                          ),
                        );
                        break;
                      case "clipboard":
                        Get.dialog(ClipboardDialog());
                        break;
                      case "copy":
                        copyToClipboard();
                        break;
                      case "copyAll":
                        copyToClipboard(all: true);
                        break;
                      case "walkaroundMaker":
                        Get.dialog(WalkaroundMakerDialog());
                        break;
                      case "clearNavigation":
                        Get.dialog(
                          ConfirmationDialog(
                            message: "Are you sure you want to clear all navigations?",
                            onConfirmed: () {
                              simController.currentSim.value!.navigations.clear();
                              simController.currentSim.refresh();
                            },
                          ),
                        );
                        break;
                      case "resetNavigationClusterPosition":
                        Get.dialog(
                          ConfirmationDialog(
                            message: "Are you sure you want to reset the navigation cluster position?",
                            onConfirmed: () {
                              simController.currentSim.value!.navClusterX = 0;
                              simController.currentSim.value!.navClusterY = 0;
                              simController.currentSim.refresh();
                            },
                          ),
                        );
                        break;
                      case "changeBackground":
                        Future.delayed(const Duration(milliseconds: 100), () async {
                          Get.dialog(EditLocationBackground());
                        });
                        break;
                      case "changeBackgroundColor":
                        Future.delayed(const Duration(milliseconds: 100), () async {
                          Get.dialog(EditLocationBackgroundColor());
                        });
                        break;
                      case "timeline":
                        Future.delayed(const Duration(milliseconds: 100), () async {
                          Get.dialog(TimelineDialog());
                        });
                        break;
                      case "undo":
                        EasyDebounce.debounce("undo", const Duration(milliseconds: 300), () {
                          simController.undo();
                        });
                        break;
                      case "redo":
                        EasyDebounce.debounce("redo", const Duration(milliseconds: 300), () {
                          simController.redo();
                        });
                        break;
                      default:
                        print("Invalid selected item type: $value");
                    }
                  },
                  child: Column(
                    children: [
                      const Icon(Icons.more_horiz, color: Colors.white),
                      Text("More", style: TextStyle(fontSize: context.isMobileScreen ? 10 : 14, color: Colors.white)),
                    ],
                  ),
                  position: PopupMenuPosition.under,
                  offset: const Offset(0, 3),
                  color: lightBackgrounds,
                  itemBuilder: (context) => [
                    PopupMenuItem(
                      child: Text("Audio", style: TextStyle(fontSize: context.isMobileScreen ? 14 : 18, color: Colors.white)),
                      value: "audio",
                    ),
                    PopupMenuItem(
                      child: Text("Record", style: TextStyle(fontSize: context.isMobileScreen ? 14 : 18, color: Colors.white)),
                      value: "record",
                    ),
                    PopupMenuItem(
                      child: Text("Clear All", style: TextStyle(fontSize: context.isMobileScreen ? 14 : 18, color: Colors.white)),
                      value: "clear",
                    ),
                    // const PopupMenuItem(
                    //   child: Text("Clipboard", style: TextStyle(color: Colors.white)),
                    //   value: "clipboard",
                    // ),
                    PopupMenuItem(
                      child: Text("Copy to Clipboard",
                          style: simController.selectedType.value == null
                              ? TextStyle(fontSize: context.isMobileScreen ? 14 : 18, color: Colors.grey)
                              : TextStyle(fontSize: context.isMobileScreen ? 14 : 18, color: Colors.white)),
                      value: "copy",
                      enabled: simController.selectedType.value != null,
                    ),
                    PopupMenuItem(
                      child: Text("Copy All to Clipboard", style: TextStyle(fontSize: context.isMobileScreen ? 14 : 18, color: Colors.white)),
                      value: "copyAll",
                    ),
                    PopupMenuItem(
                      child: Text("Walkaround Maker", style: TextStyle(fontSize: context.isMobileScreen ? 14 : 18, color: Colors.white)),
                      value: "walkaroundMaker",
                    ),
                    PopupMenuItem(
                      child: Text("Clear Navigation", style: TextStyle(fontSize: context.isMobileScreen ? 14 : 18, color: Colors.white)),
                      value: "clearNavigation",
                    ),
                    PopupMenuItem(
                      child: Text("Reset Navigation Position", style: TextStyle(fontSize: context.isMobileScreen ? 14 : 18, color: Colors.white)),
                      value: "resetNavigationClusterPosition",
                    ),
                    PopupMenuItem(
                      child: Text(
                        "Change Background",
                        style: TextStyle(fontSize: context.isMobileScreen ? 14 : 18, color: Colors.white),
                      ),
                      value: "changeBackground",
                    ),
                    PopupMenuItem(
                      child: Text(
                        "Change Background Color",
                        style: TextStyle(fontSize: context.isMobileScreen ? 14 : 18, color: Colors.white),
                      ),
                      value: "changeBackgroundColor",
                    ),
                    PopupMenuItem(
                      child: Text(
                        "Timeline",
                        style: TextStyle(fontSize: context.isMobileScreen ? 14 : 18, color: Colors.white),
                      ),
                      value: "timeline",
                    ),
                    // PopupMenuItem(
                    //   // enabled: simController.undoStack.isNotEmpty,
                    //   enabled: simController.undoStack.length > 1,
                    //   child: Text(
                    //     "Undo",
                    //     style: TextStyle(fontSize: context.isMobileScreen ? 14 : 18, color: Colors.white),
                    //   ),
                    //   value: "undo",
                    // ),
                    // PopupMenuItem(
                    //   enabled: simController.redoStack.isNotEmpty,
                    //   child: Text(
                    //     "Redo",
                    //     style: TextStyle(fontSize: context.isMobileScreen ? 14 : 18, color: Colors.white),
                    //   ),
                    //   value: "redo",
                    // ),
                  ],
                ),
              ],
            ),
            if (!isMobile)
              Container(
                constraints: const BoxConstraints(
                  maxWidth: 280,
                ),
                child: SearchAnchor(
                  builder: (context, controller) {
                    return Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8),
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.white.withOpacity(.51)),
                        borderRadius: BorderRadius.circular(50),
                      ),
                      child: Row(
                        children: [
                          Icon(Icons.search, color: Colors.white.withOpacity(.51)),
                          const SizedBox(width: 5),
                          Expanded(
                            child: TextField(
                              controller: controller,
                              style: const TextStyle(color: Colors.white),
                              focusNode: searchBarFocusNode,
                              decoration: const InputDecoration(
                                hintText: "Search...",
                                hintStyle: TextStyle(color: Colors.white),
                                border: InputBorder.none,
                                focusedBorder: InputBorder.none,
                                enabledBorder: InputBorder.none,
                                errorBorder: InputBorder.none,
                                disabledBorder: InputBorder.none,
                                focusColor: brick,
                              ),
                              onChanged: (value) {
                                // print(
                                //     "searchController.text.isNotEmpty = ${searchController.text.isNotEmpty} && searchController.isOpen = ${searchController.isOpen} && !searchBarFocusNode.hasFocus = ${!searchBarFocusNode.hasFocus}");
                                // if (searchController.text.isNotEmpty && searchController.isOpen && !searchBarFocusNode.hasFocus) {
                                //   searchBarFocusNode.requestFocus();
                                // }

                                // searchElement.value = value;
                                searchController.text = value;
                                if (value.isEmpty) {
                                  // categoryChoices.clear();
                                  searchController.closeView("");
                                  searchBarFocusNode.unfocus();
                                } /*  else {
                                  if (!searchController.isOpen) {
                                    searchController.openView();
                                  }
                                } */
                              },
                            ),
                          ),
                        ],
                      ),
                    );
                  },
                  searchController: searchController,
                  suggestionsBuilder: (context, searchController) async {
                    print("IN SUGGESTIONS BUILDER: ${searchController.value}");
                    final categoryChoices = categories.map((category) {
                      final categoryChoices = spriteCategoryMapping[category]!.keys.toList();
                      return extractTop(query: searchController.text, choices: categoryChoices, limit: 5, cutoff: 50);
                    }).toList();
                    print("Suggestions: $categoryChoices");
                    return [
                      for (int i = 0; i < categories.length; i++)
                        if (categoryChoices[i].isNotEmpty)
                          Container(
                            color: mainBackgrounds,
                            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Container(
                                  padding: const EdgeInsets.only(left: 10, bottom: 5),
                                  child: Text(
                                    categories[i],
                                    style: TextStyle(fontSize: context.isMobileScreen ? 14 : 24, color: Colors.white),
                                  ),
                                ),
                                for (final choice in categoryChoices[i])
                                  PopupMenuItem(
                                    child: buildMenuOption(category: categories[i], optionName: choice.choice),
                                    onTap: buildOptionHandler(category: categories[i], optionName: choice.choice, preventClose: true),
                                  ),
                              ],
                            ),
                          ),
                    ];
                  },
                ),
                // CustomSearchBar(),
              ),

            Row(
              children: [
                //Play
                TextButton(
                  child: Row(
                    children: [
                      Icon(Icons.play_circle, size: context.isMobileScreen ? 14 : 20, color: Colors.white),
                      const SizedBox(width: 9),
                      Text(
                        "Play",
                        style: TextStyle(fontSize: context.isMobileScreen ? 10 : 14, color: Colors.white),
                      ),
                    ],
                  ),
                  onPressed: () {
                    // Tricks the new play into thinking that no object is selected
                    final objectIndexValue = simController.selectedSimObjectIndex.value;
                    final typeValue = simController.selectedType.value;
                    simController.selectedSimObjectIndex.value = -1;
                    simController.selectedType.value = null;
                    simController.currentSim.value!.inPlayMode = true;
                    simController.currentSim.refresh();
                    Get.off(Preview(), preventDuplicates: true, arguments: Map<String, dynamic>.from({"inEditor": "true"}));
                    // Get.to(Preview(breakpoint: breakpoint),
                    //     preventDuplicates: true, arguments: Map<String, dynamic>.from({"inEditor": "true", "breakpoint": breakpoint}));
                    // Get.to(() => Preview(breakpoint: breakpoint),
                    //     preventDuplicates: true, arguments: Map<String, dynamic>.from({"inEditor": "true", "breakpoint": breakpoint}));
                    Future.delayed(const Duration(milliseconds: 500), () async {
                      simController.selectedSimObjectIndex.value = objectIndexValue;
                      simController.selectedType.value = typeValue;
                    });
                  },
                ),
                SizedBox(width: context.isMobileScreen ? 0 : 10),
                //Save
                Obx(
                  () => simController.autosave.value
                      ? Container(
                          margin: const EdgeInsets.only(right: 10),
                          child: const Text("Auto-Save On", style: TextStyle(color: brick)),
                        )
                      : TextButton(
                          onPressed: saveSim,
                          child: Obx(
                            () => Row(
                              children: [
                                Icon(Icons.done,
                                    size: context.isMobileScreen ? 14 : 20, color: simController.saved.value ? Colors.grey[350] : yellow),
                                SizedBox(width: context.isMobileScreen ? 4 : 9),
                                Text("Save",
                                    style: TextStyle(
                                        fontSize: context.isMobileScreen ? 10 : 14, color: simController.saved.value ? Colors.grey[350] : yellow)),
                              ],
                            ),
                          ),
                        ),
                ),
                //[Change Background - Auto Save Switch]
                PopupMenuButton(
                  color: sidebarDark,
                  itemBuilder: (context) {
                    return [
                      PopupMenuItem(
                        child: Row(
                          children: [
                            Icon(Icons.auto_mode, size: context.isMobileScreen ? 14 : 20, color: Colors.white),
                            SizedBox(width: context.isMobileScreen ? 4 : 9),
                            Text(
                              "Auto-save",
                              style: TextStyle(fontSize: context.isMobileScreen ? 10 : 14, color: Colors.white),
                            ),
                            Expanded(
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.end,
                                children: [
                                  Obx(
                                    () => Transform.scale(
                                      scale: context.isMobileScreen ? 0.6 : 1,
                                      child: Switch(
                                        value: simController.autosave.value,
                                        onChanged: (value) {
                                          simController.autosave.value = value;
                                        },
                                        activeColor: brick,
                                        inactiveTrackColor: Colors.white.withOpacity(0.3),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            )
                          ],
                        ),
                        onTap: () {
                          Future.delayed(const Duration(milliseconds: 100), () async {});
                        },
                      ),
                    ];
                  },
                  child: Container(
                    decoration: BoxDecoration(borderRadius: BorderRadius.circular(50), border: Border.all(color: white60)),
                    padding: EdgeInsets.symmetric(horizontal: context.isMobileScreen ? 6 : 13, vertical: context.isMobileScreen ? 6 : 13),
                    child: const Icon(Icons.more_horiz, color: Colors.white, size: 12),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
