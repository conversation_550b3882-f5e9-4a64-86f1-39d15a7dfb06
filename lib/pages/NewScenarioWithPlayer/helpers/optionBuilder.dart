import 'package:flame/components.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:simsushare_player/components/MiniPlayer.dart';
import 'package:simsushare_player/components/SimPlayer.dart' if (dart.library.html) 'package:simsushare_player/components/SimPlayerWeb.dart';
import 'package:simsushare_player/flame/Containers/builder.dart';
import 'package:simsushare_player/flame/Labels/builder.dart';
import 'package:simsushare_player/utils/constants.dart';

Widget buildMenuOption({required String category, required String optionName}) {
  // print("Building menu options for $category => $optionName");
  switch (category) {
    case "Fire":
    case "Smoke":
    case "Explosions":
    case "HazMat":
      return _buildSpriteMenuOption(category: category, spriteName: optionName);
    case "Labels":
      return _buildLabelMenuOption(category: category, labelName: optionName);
    case "Containers":
      return _buildContainerMenuOption(labelName: optionName);
    case "People":
      return _buildPeopleMenuOption(labelName: optionName);
    default:
      return Row(
        children: [
          Text(
            optionName,
            style: const TextStyle(color: Colors.white),
          ),
        ],
      );
  }
}

Widget _buildSpriteMenuOption({required String category, required String spriteName}) {
  final assetName = spriteCategoryMapping[category]![spriteName]!;
  return Row(
    children: [
      if (kIsWeb)
        Image.network(
          "https://sus-assets.s3.amazonaws.com/sprites/thumbs/$assetName.png",
          width: 40,
        )
      else
        Image.asset(
          "assets/sprites/thumbs/$assetName.png",
          width: 40,
        ),
      // FutureBuilder<SimSprite>(
      //   future: Future(() async {
      //     final assetName = spriteCategoryMapping[category]![spriteName]!;
      //     // print("+++++++++++++ Loading asset: $assetName from category: $category and spriteName: $spriteName");
      //     final spriteAssetName = "assets/sprites/$assetName-frames-high";
      //     final imgAsset = await rootBundle.load(spriteAssetName + ".png");
      //     final img = await decodeImageFromList(imgAsset.buffer.asUint8List());
      //     print("============ Decoded img: ${spriteAssetName + ".png"} ==> ${img.width} x ${img.height}}");
      //     final meta = jsonDecode(await rootBundle.loadString(spriteAssetName + ".json"));
      //     for (var key in (meta["frames"] as Map<String, dynamic>).keys) {
      //       meta["frames"][key]["duration"] = 100;
      //     }
      //     final frames = (meta["frames"] as Map<String, dynamic>).values.map(
      //       (e) {
      //         return SpriteFrame(
      //           x: (e["frame"]["x"] as int).toDouble(),
      //           y: (e["frame"]["y"] as int).toDouble(),
      //           width: (e["frame"]["w"] as int).toDouble(),
      //           height: (e["frame"]["h"] as int).toDouble(),
      //           rotated: e["rotated"] as bool,
      //         );
      //       },
      //     ).toList();
      //     final sprite = SimSprite(
      //       x: 0,
      //       y: 0,
      //       name: assetName,
      //       img: img,
      //       frames: frames,
      //       assetName: assetName,
      //       aseprite: meta as Map<String, dynamic>,
      //       width: double.parse(meta["meta"]["size"]["w"].toString()),
      //       height: double.parse(meta["meta"]["size"]["h"].toString()),
      //     );
      //     return sprite;
      //   }),
      //   builder: ((context, snapshot) {
      //     if (snapshot.hasError) {
      //       print("Sprite Snapshot Error: ${snapshot.error}");
      //       return const SizedBox(width: 30);
      //     }
      //     return snapshot.hasData
      //         ? Container(
      //             decoration: BoxDecoration(
      //               border: Border.all(color: white60),
      //               borderRadius: BorderRadius.circular(4),
      //               color:
      //                   spriteCategoryColorMapping[category]![spriteName] ?? spriteCategoryColorMapping[category]!["_default"] ?? Colors.transparent,
      //             ),
      //             child: MiniPlayer(
      //               sprite: snapshot.data!,
      //               width: 30,
      //               height: 30,
      //               play: false,
      //             ),
      //           )
      //         : const CircularProgressIndicator();
      //   }),
      // ),
      const SizedBox(width: 20),
      Flexible(
        child: Text(
          spriteName,
          style: const TextStyle(color: Colors.white),
        ),
      ),
    ],
  );
}

Widget _buildLabelMenuOption({required String category, required String labelName}) {
  return Row(
    children: [
      FutureBuilder<PositionComponent>(
        future: buildLabelFromType(labelName, size: Vector2(30, 30)),
        builder: ((context, snapshot) {
          if (snapshot.hasError) {
            print("Error: ${snapshot.error}");
            return const SizedBox(width: 30);
          }
          return snapshot.hasData
              ? Container(
                  decoration: BoxDecoration(
                    border: Border.all(color: white60),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: ComponentMiniPlayer(
                    component: snapshot.data!..position = Vector2(15, 15),
                    width: 30,
                    height: 30,
                    play: false,
                  ),
                )
              : const CircularProgressIndicator();
        }),
      ),
      const SizedBox(width: 20),
      Text(
        labelName,
        style: const TextStyle(color: Colors.white),
      ),
    ],
  );
}

Widget _buildContainerMenuOption({required String labelName}) {
  return Row(
    children: [
      FutureBuilder<SimSpriteComponent>(
        future: buildContainer(type: labelName, view: 0, size: Vector2(20, 20)),
        builder: ((context, snapshot) {
          if (snapshot.hasError) {
            print("Error: ${snapshot.error}");
            return const SizedBox(width: 30);
          }
          return snapshot.hasData
              ? Container(
                  decoration: BoxDecoration(
                    border: Border.all(color: white60),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: ComponentMiniPlayer(
                    component: snapshot.data!
                      ..position = Vector2(15, 15)
                      ..anchor = Anchor.center,
                    width: 30,
                    height: 30,
                    play: false,
                  ),
                )
              : const CircularProgressIndicator();
        }),
      ),
      const SizedBox(width: 20),
      Text(
        labelName,
        style: const TextStyle(color: Colors.white),
      ),
    ],
  );
}

Widget _buildPeopleMenuOption({required String labelName}) {
  return Row(
    children: [
      Container(
        decoration: BoxDecoration(
          border: Border.all(color: white60),
          borderRadius: BorderRadius.circular(4),
        ),
        child: SizedBox(
          width: 30,
          height: 30,
          child: Image.asset("assets/people/${assetToPeopleMapping.entries.firstWhere((el) => el.value == labelName).key}/0.png"),
        ),
      ),
      const SizedBox(width: 20),
      Text(
        labelName,
        style: const TextStyle(color: Colors.white),
      ),
    ],
  );
}
