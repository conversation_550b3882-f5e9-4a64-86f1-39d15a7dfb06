// ignore_for_file: must_be_immutable

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:simsushare_player/controllers/SimController.dart';

class SimAnimatedDropdown extends StatelessWidget {
  final String title;
  Widget? body;
  RxBool? defaultOpen;

  SimAnimatedDropdown({
    Key? key,
    required this.title,
    this.body,
    this.defaultOpen,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(CustomDropdownController());
    var isExpanded = defaultOpen != null && defaultOpen!.value == true ? true.obs : false.obs;

    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: Colors.white.withOpacity(0.1)),
        borderRadius: BorderRadius.circular(8),
      ),
      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 15),
      margin: const EdgeInsets.only(bottom: 10),
      child: Column(
        children: [
          InkWell(
            onTap: () => isExpanded.toggle(),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: <Widget>[
                Text(
                  title,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.w700,
                  ),
                ),
                Obx(
                  () => Icon(
                    isExpanded.value ? CupertinoIcons.chevron_up : CupertinoIcons.chevron_down,
                    color: Colors.white,
                    size: 18,
                  ),
                )
              ],
            ),
          ),
          Obx(() {
            if (isExpanded.value) {
              return const Column(
                children: [
                  SizedBox(height: 20),
                  Divider(
                    color: Colors.white,
                    height: 1,
                  ),
                  SizedBox(height: 20)
                ],
              );
            } else {
              return const SizedBox();
            }
          }),
          AnimatedSize(
            duration: const Duration(milliseconds: 800),
            curve: Curves.fastOutSlowIn,
            child: Obx(() {
              if (isExpanded.value) {
                return body ??
                    Column(
                      children: controller.items.map((item) {
                        return Column(
                          children: [
                            InkWell(
                              onTap: () => controller.selectItem(item),
                              child: Row(
                                children: [
                                  CustomRadioButton(
                                    index: controller.items.indexOf(item),
                                    selectedIndex: controller.items.indexOf(controller.selectedItem.value),
                                  ),
                                  Text(
                                    item,
                                    style: const TextStyle(
                                      fontWeight: FontWeight.w400,
                                      color: Colors.white,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            const SizedBox(height: 5),
                          ],
                        );
                      }).toList(),
                    );
              } else {
                return InkWell(
                  onTap: () => isExpanded.toggle(),
                  child: body != null
                      ? const SizedBox(height: 10)
                      : Container(
                          width: 240,
                          height: 40,
                          decoration: BoxDecoration(
                            color: Colors.white.withOpacity(0.05),
                            border: Border.all(color: Colors.white.withOpacity(0.03)),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 8),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Text(
                                  controller.selectedItem.value,
                                  style: const TextStyle(
                                    fontSize: 14,
                                    fontWeight: FontWeight.w400,
                                    color: Colors.white,
                                  ),
                                ),
                                const Icon(
                                  Icons.arrow_drop_down_outlined,
                                  color: Colors.white,
                                  size: 16,
                                )
                              ],
                            ),
                          ),
                        ),
                );
              }
            }),
          ),
        ],
      ),
    );
  }
}

class CustomDropdownController extends GetxController {
  var selectedItem = "".obs;

  List<String> options(List<String> optionsList) => optionsList;

  List<String> get items => ["Scenario", "Location", "State"];
  Map<String, String> get itemsMap => {
        "Scenario": "scenario",
        "Location": "location",
        "State": "state",
      };

  void selectItem(String item) {
    selectedItem.value = item;
    final _simController = Get.find<SimController>();
    _simController.getCurrentSelectedObject()!.trigger = itemsMap[item]!;
  }
}

class CustomRadioButton extends StatelessWidget {
  const CustomRadioButton({
    Key? key,
    required this.index,
    this.selectedIndex,
  }) : super(key: key);

  final int index;
  final int? selectedIndex;

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 15,
      height: 15,
      decoration: BoxDecoration(
        shape: BoxShape.rectangle,
        borderRadius: BorderRadius.circular(5),
        border: Border.all(color: Colors.white.withOpacity(0.03)),
        color: Colors.white.withOpacity(0.05),
      ),
      margin: const EdgeInsets.only(right: 10),
      child: selectedIndex == null || selectedIndex != index
          ? Container()
          : Center(
              child: Container(
                width: 10,
                height: 10,
                decoration: BoxDecoration(
                  shape: BoxShape.rectangle,
                  borderRadius: BorderRadius.circular(3),
                  color: Colors.white,
                ),
              ),
            ),
    );
  }
}
