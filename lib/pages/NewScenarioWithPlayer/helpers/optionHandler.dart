import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:nanoid/nanoid.dart';
import 'package:simsushare_player/controllers/SimController.dart';
import 'package:simsushare_player/models/SimObjects.dart';
import 'package:simsushare_player/pages/NewScenarioWithPlayer/helpers/helper_methods.dart';
import 'package:simsushare_player/utils/constants.dart';

buildOptionHandler({required String category, required String optionName, bool preventClose = false}) {
  switch (category) {
    case "Fire":
    case "Smoke":
    case "Explosions":
    case "HazMat":
      return _buildSpriteOptionHandler(category: category, spriteName: optionName, preventClose: preventClose);
    case "Labels":
      return _buildLabelOptionHandler(category: category, labelName: optionName, preventClose: preventClose);
    case "Containers":
      return _buildContainerOptionHandler(containerName: optionName, preventClose: preventClose);
    case "People":
      return _buildPeopleOptionHandler(category: category, personType: optionName, preventClose: preventClose);
    default:
      return () {
        print("No option handler for $category => $optionName");
      };
  }
}

Future<dynamic> Function() _buildSpriteOptionHandler({required String category, required String spriteName, bool preventClose = false}) {
  return () async {
    final _simController = Get.find<SimController>();
    // print("Sprite Name: $spriteName , sprite asset name ${spriteAssetNames[index]}");
    final assetName = spriteCategoryMapping[category]![spriteName]!;
    final spriteAssetName = "assets/sprites/$assetName-frames-high";
    final audioAssetName = "asset://sprites/$assetName.mp3";
    final imgAsset = await rootBundle.load(spriteAssetName + ".png");
    final img = await decodeImageFromList(imgAsset.buffer.asUint8List());
    print("============ Decoded img: ${spriteAssetName + ".png"} ==> ${img.width} x ${img.height}}");
    // final meta = jsonDecode(
    //     await rootBundle.loadString(spriteAssetNames[index].replaceFirst(".png", ".json")));
    final meta = jsonDecode(await rootBundle.loadString(spriteAssetName + ".json"));
    // print("META: ${(meta["frames"] as Map<String, dynamic>).keys.toList()}");
    for (var key in (meta["frames"] as Map<String, dynamic>).keys) {
      meta["frames"][key]["duration"] = 100;
    }
    final frames = (meta["frames"] as Map<String, dynamic>).values.map(
      (e) {
        return SpriteFrame(
          x: (e["frame"]["x"] as int).toDouble(),
          y: (e["frame"]["y"] as int).toDouble(),
          width: (e["frame"]["w"] as int).toDouble(),
          height: (e["frame"]["h"] as int).toDouble(),
          rotated: e["rotated"] as bool,
        );
      },
    ).toList();
    final cs = _simController.currentSim.value!;
    final spriteId = assetName.replaceAll(" ", "") + "_" + nanoid(4);
    final spriteLength = cs.locations[_simController.currentLocation.value].sprites.where((element) => element.assetName == assetName).length;
    final newSpriteName = assetName.replaceAll(" ", "") + "_" + (spriteLength + 1).toString();
    final newSprite = SimSprite(
      id: spriteId,
      name: newSpriteName,
      img: img,
      frames: frames,
      assetName: assetName,
      aseprite: meta as Map<String, dynamic>,
      x: 0.1 + generateSimObjectDrift(),
      y: 0.1 + generateSimObjectDrift(),
      width: double.parse(meta["meta"]["size"]["w"].toString()),
      height: double.parse(meta["meta"]["size"]["h"].toString()),
      scaleFactor: double.parse(meta["meta"]["scale"].toString()),
    )..priority = getHighestPriorityObject().priority + 1;
    cs.locations[_simController.currentLocation.value].sprites.add(newSprite);
    try {
      await rootBundle.load(audioAssetName.replaceFirst("asset://", "assets/"));
      cs.locations[_simController.currentLocation.value].sounds.add(
        SimSound(
          id: "$newSpriteName-audio",
          path: audioAssetName,
          x: newSprite.x,
          y: newSprite.y,
          opacity: 0.4,
        )..priority = 1,
      );
    } catch (err) {
      print("No audio associated with sprite: $audioAssetName");
    }
    // _simController.updateCurrentSim(cs);
    _simController.currentSim.value = cs;
    _simController.currentSim.refresh();
    if (!preventClose) {
      Get.back(closeOverlays: true, canPop: false);
    }
  };
}

Future<dynamic> Function() _buildContainerOptionHandler({required String containerName, bool preventClose = false}) {
  return () async {
    final _simController = Get.find<SimController>();
    final cs = _simController.currentSim.value!;
    final newSuffix =
        _simController.getLargestObjectTypeInLocation(_simController.currentLocation.value, "container", overridePrefix: "${containerName}_") + 1;
    // final assetPath = "assets/containers/${containerAssetsMapping[labelName]}/0.png";
    // final imgAsset = await rootBundle.load(assetPath);
    // final img = await decodeImageFromList(imgAsset.buffer.asUint8List());
    final container = SimContainer(
      // id: containerName.replaceAll(" ", "") + "_" + nanoid(4),
      id: "${containerName}_$newSuffix",
      type: containerName,
      view: 0,
      name: "${containerName}_$newSuffix",
      x: 150 + generateSimObjectDrift() * 150,
      y: 150 + generateSimObjectDrift() * 150,
      // width: img.width.toDouble(),
      // height: img.height.toDouble(),
    )..priority = getHighestPriorityObject().priority + 1;
    cs.locations[_simController.currentLocation.value].containers.add(container);
    _simController.currentSim.value = cs;
    _simController.currentSim.refresh();
    if (!preventClose) {
      Get.back(closeOverlays: true, canPop: false);
    }
  };
}

Future<dynamic> Function() _buildPeopleOptionHandler({required String category, required String personType, bool preventClose = false}) {
  return () async {
    final _simController = Get.find<SimController>();

    final cs = _simController.currentSim.value!;
    final newSuffix =
        _simController.getLargestObjectTypeInLocation(_simController.currentLocation.value, "person", overridePrefix: "${personType}_") + 1;
    // final personLength = cs.locations[_simController.currentLocation.value].people.where((element) => element.type == personName).length;
    final newPerson = SimPerson(
      // id: personType + "_" + nanoid(4),
      id: "${personType}_${newSuffix}_${nanoid(4)}",
      name: "${personType}_$newSuffix",
      posture: 0,
      type: personType,
      x: 150 + generateSimObjectDrift(),
      y: 150 + generateSimObjectDrift(),
    )..priority = getHighestPriorityObject().priority + 1;
    cs.locations[_simController.currentLocation.value].people.add(newPerson);
    // _simController.updateCurrentSim(cs);
    _simController.currentSim.value = cs;
    _simController.currentSim.refresh();
    if (!preventClose) {
      Get.back(closeOverlays: true, canPop: false);
    }
  };
}

Future<dynamic> Function() _buildLabelOptionHandler({required String category, required String labelName, bool preventClose = false}) {
  return () async {
    final _simController = Get.find<SimController>();
    final cs = _simController.currentSim.value!;
    final labelId = labelName.replaceAll(" ", "") + "_" + nanoid(4);
    final newSuffix = _simController.getLargestObjectTypeInLocation(_simController.currentLocation.value, "label") + 1;
    final newLabel = SimLabel(
      id: labelId,
      name: "Label_$newSuffix",
      type: labelName,
      // TODO: need to copy the initialize default variables
      variables: <String, dynamic>{
        "type": 0,
      },
      x: 150 + generateSimObjectDrift(),
      y: 150 + generateSimObjectDrift(),
      width: 230,
      height: 230,
    )..priority = getHighestPriorityObject().priority + 1;
    cs.locations[_simController.currentLocation.value].labels.add(newLabel);
    // _simController.updateCurrentSim(cs);
    _simController.currentSim.value = cs;
    _simController.currentSim.refresh();
    if (!preventClose) {
      Get.back(closeOverlays: true, canPop: false);
    }
  };
}
