import 'dart:convert';
import 'dart:io';
import 'dart:math';

import 'package:collection/collection.dart';
import 'package:file_picker/file_picker.dart' show FileType;
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_colorpicker/flutter_colorpicker.dart';
import 'package:get/get.dart';
import 'package:nanoid/nanoid.dart';
import 'package:popover/popover.dart';
import 'package:simsushare_player/components/ConfirmationDialog.dart';
import 'package:simsushare_player/components/ContentDialog.dart';
import 'package:simsushare_player/components/GreyButton.dart';
import 'package:simsushare_player/components/LibraryDialog.dart';
import 'package:simsushare_player/components/MainButtons.dart';
import 'package:simsushare_player/components/PickLocationImage.dart';
import 'package:simsushare_player/controllers/SimController.dart';
import 'package:simsushare_player/models/Mask.dart';
import 'package:simsushare_player/models/SimObjects.dart';
import 'package:simsushare_player/models/Simulation.dart';
import 'package:simsushare_player/pages/NewScenarioWithPlayer/helpers/helper_methods.dart';
import 'package:simsushare_player/pages/NewScenarioWithPlayer/sliders/custom_slider.dart';
import 'package:simsushare_player/utils/constants.dart';
import 'package:simsushare_player/utils/picker.dart';
import 'package:simsushare_player/utils/preppers.dart';

class SelectAudioSource extends StatelessWidget {
  const SelectAudioSource({
    Key? key,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final SimController _simController = Get.find();
    final sim = _simController.currentSim.value!;
    return ContentDialog(
        title: "Select Source",
        height: null,
        content: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Text("Select file", style: TextStyle(fontSize: 16, color: Colors.white)),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                GreyButtonLarge(
                  child: const Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(Icons.upload, color: Colors.white),
                      SizedBox(width: 14),
                      Text(
                        "Upload From Disk",
                        style: TextStyle(color: Colors.white),
                      ),
                    ],
                  ),
                  onPressed: () async {
                    try {
                      final audioFile = await pickFiles(type: FileType.audio);
                      if (audioFile == null) {
                        print("No audio file selected");
                        return;
                      }
                      final highestPriorityObject = _simController
                          .getAllLocationObjects(_simController.currentLocation.value)
                          .reduce((value, element) => value.priority > element.priority ? value : element);
                      sim.locations[_simController.currentLocation.value].sounds.add(
                        SimSound(path: audioFile.files[0].path!, x: 150, y: 150)..priority = highestPriorityObject.priority + 1,
                      );
                      _simController.currentSim.refresh();
                      // return Get.back(result: result.files[0].path);
                      return Get.back();
                    } catch (err) {
                      print(err);
                    }
                  },
                ),
                const SizedBox(width: 12),
                const Text("OR", style: TextStyle(color: Colors.white)),
                const SizedBox(width: 12),
                GreyButtonLarge(
                  child: const Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(Icons.image, color: Colors.white),
                      SizedBox(width: 14),
                      Text(
                        "Select From Library",
                        style: TextStyle(color: Colors.white),
                      ),
                    ],
                  ),
                  onPressed: () async {
                    FileSystemEntity? file = await showDialog(
                      context: context,
                      builder: ((context) {
                        return LibraryDialog(
                          allowMultiple: false,
                          extensionFilter: const [".m4a", ".mp3", ".wav"],
                        );
                      }),
                    );
                    if (file == null) return;
                    final simDir = await getSimSaveDirectory(sim);
                    final generatedName = nanoid(20);
                    final simFilePath = simDir.path + "/$generatedName${file.path.split(".").last}";
                    await File(file.path).copy(simFilePath);
                    sim.locations[_simController.currentLocation.value].sounds.add(
                      // SimSound(path: file.path, x: 150, y: 150),
                      SimSound(path: simFilePath, x: 150, y: 150),
                    );
                    _simController.currentSim.refresh();
                    return Get.back();
                  },
                ),
              ],
            )
          ],
        ),
        actions: const []);
  }
}

class NewLocationDialog extends StatelessWidget {
  NewLocationDialog({
    Key? key,
  }) : super(key: key);

  final state = "".obs;
  final locations = <SimulationLocation>[].obs;

  @override
  Widget build(BuildContext context) {
    SimController _simController = Get.find();
    final sim = _simController.currentSim.value!;
    return ContentDialog(
      title: "New Location",
      width: null,
      height: null,
      content: Column(
        children: [
          context.isMobileScreen
              ? Container(
                  alignment: Alignment.center,
                  width: double.infinity,
                  height: 40,
                  child: _SelectStateDropdownWidget(
                    state: state,
                    sim: sim,
                  ),
                )
              : _SelectStateDropdownWidget(
                  state: state,
                  sim: sim,
                ),
          const SizedBox(height: 11),
          const Text(
            "Upload Image",
            style: TextStyle(fontSize: 14, color: Colors.white),
          ),
          const SizedBox(height: 11),
          PickLocationImage(
            multi: true,
            onImageSelected: (images) async {
              print(images);
              for (var index = 0; index < images.length; index++) {
                final img = images[index]!;
                locations.add(
                  makeLocationFromImage(
                    id: nanoid(),
                    name: "Location ${sim.locations.length + 1 + index}",
                    imgPath: img,
                    img: await decodeImageFromList(await File(img).readAsBytes()),
                    sim: sim,
                    state: state.value,
                  ),
                );
              }
              /* images.forEachIndexed(
                (index, img) {
                  locations.add(
                    SimulationLocation(
                      id: nanoid(),
                      name: "Location ${sim.locations.length + 1 + index}",
                      sprites: [],
                      image: img!,
                    ),
                  );
                },
              ); */
            },
          ),
          const SizedBox(height: 20),
          Expanded(
            child: _PickedLocationWidget(
              locations: locations,
              sim: sim,
              simController: _simController,
            ),
          )
        ],
      ),
      actions: [
        TransparentButton(
          onPressed: () {
            Get.back();
          },
          label: "Cancel",
        ),
        OrangeButton(
          onPressed: () {
            // print()
            if (state.isEmpty) {
              Get.showSnackbar(const GetSnackBar(
                title: "Error",
                message: "Missing State",
                duration: Duration(seconds: 2),
              ));
              return;
            }
            bool conflictingLocation = false;
            locations.forEachIndexed((index, loc) {
              final cl = sim.locations.firstWhereOrNull((simLoc) => simLoc.name == loc.name);
              if (cl != null) {
                print("Conflicting location: $cl");
                conflictingLocation = true;
              }
              locations[index].state = state.value;
            });
            if (conflictingLocation) {
              print("-------Conflicting location-------");
              Get.showSnackbar(const GetSnackBar(
                title: "Error",
                message: "Location name already exists",
                duration: Duration(seconds: 2),
              ));
              return;
            }
            sim.locations.addAll(locations);
            _simController.currentSim.refresh();
            Get.back();
          },
          label: "Create",
        )
      ],
    );
  }
}

class _PickedLocationWidget extends StatelessWidget {
  final RxList<SimulationLocation> locations;
  final Scenario sim;
  final SimController _simController;

  const _PickedLocationWidget({
    Key? key,
    required SimController simController,
    required this.locations,
    required this.sim,
  })  : _simController = simController,
        super(key: key);

  @override
  Widget build(BuildContext context) {
    return Obx(
      () => SingleChildScrollView(
        child: Column(
          children: locations
              .mapIndexed(
                (index, loc) => Container(
                  color: lightBackgrounds,
                  padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
                  margin: const EdgeInsets.only(bottom: 8),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Transform.rotate(
                        angle: loc.imageRotation,
                        child: kIsWeb
                            ? Image.memory(
                                base64Decode(loc.image),
                                fit: BoxFit.contain,
                                height: 64,
                                width: 94,
                              )
                            : Image.file(
                                File(loc.image),
                                fit: BoxFit.contain,
                                height: 64,
                                width: 94,
                              ),
                      ),
                      Expanded(
                        child: Container(
                          padding: const EdgeInsets.symmetric(horizontal: 16),
                          child: TextField(
                            decoration: const InputDecoration(
                              hintText: "Location name",
                              enabledBorder: InputBorder.none,
                              focusedBorder: InputBorder.none,
                            ),
                            style: const TextStyle(color: Colors.white, fontSize: 14),
                            onChanged: (value) {
                              print("Updating value $value");
                              if (value.isEmpty) {
                                locations[index].name = "Location ${sim.locations.length + 1 + index}";
                              } else {
                                locations[index].name = value;
                              }
                              // scenarioName.value = value;
                              print("New value ${locations[index].name}");
                              _simController.currentSim.refresh();
                            },
                          ),
                        ),
                      ),
                      TextButton(
                        onPressed: () {
                          locations.removeAt(index);
                          _simController.currentSim.refresh();
                        },
                        child: Container(
                          decoration: BoxDecoration(color: mainBackgrounds, borderRadius: BorderRadius.circular(50)),
                          padding: const EdgeInsets.all(10),
                          width: 44,
                          height: 44,
                          child: const Icon(Icons.delete, size: 18, color: Colors.white),
                        ),
                      ),
                    ],
                  ),
                ),
              )
              .toList(),
        ),
      ),
    );
  }
}

class _SelectStateDropdownWidget extends StatelessWidget {
  final RxString state;
  final Scenario sim;

  const _SelectStateDropdownWidget({
    Key? key,
    required this.state,
    required this.sim,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Obx(
      () => DropdownButtonFormField(
        value: state.value.isEmpty ? null : state.value,
        dropdownColor: bordersColor,
        hint: const Text(
          "Select State",
          style: TextStyle(color: Colors.white),
        ),
        items: sim.states
            .map(
              (e) => DropdownMenuItem(
                child: Text(
                  e.name,
                  style: const TextStyle(fontSize: 14, color: Colors.white),
                ),
                value: e.id,
              ),
            )
            .toList(),
        onChanged: ((String? value) {
          state.value = value!;
        }),
        borderRadius: BorderRadius.circular(12),
      ),
    );
  }
}

class NewStateDialog extends StatelessWidget {
  NewStateDialog({
    Key? key,
  }) : super(key: key);

  final stateName = "".obs;
  final copyState = "".obs;
  final selectedLocations = <String>[].obs;
  final ignoreSimObjects = false.obs;
  final copyNavigation = false.obs;
  final error = "".obs;

  @override
  Widget build(BuildContext context) {
    SimController _simController = Get.find();
    final sim = _simController.currentSim.value!;
    // final locationName = sim.locations[_simController.currentLocation.value].name;
    final locationNames = <String>{}.obs;
    Future.delayed(Duration.zero, () {
      for (var loc in sim.locations) {
        locationNames.add(loc.name);
      }
    });
    copyState.value = sim.states[0].id;
    return ContentDialog(
      title: "New State",
      content: Obx(
        () => SingleChildScrollView(
          child: Column(
            children: [
              Row(
                children: [
                  Expanded(
                    child: TextField(
                      decoration: const InputDecoration(hintText: "State Name"),
                      style: const TextStyle(color: white60),
                      onChanged: (value) {
                        stateName.value = value;
                      },
                    ),
                  ),
                  const SizedBox(width: 10),
                  Expanded(
                    child: Obx(
                      () {
                        final availableStates = sim.states.map((s) => s.id).toList();
                        // sim.locations.where((loc) => loc.name == locationName).map((loc) => loc.state).toList();
                        // if (copyState.value.isEmpty) {
                        //   copyState.value = availableStates[0];
                        // }
                        print(availableStates);
                        return Container(
                          decoration: BoxDecoration(
                            border: Border.all(color: white60),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          padding: const EdgeInsets.symmetric(horizontal: 10),
                          child: DropdownButton(
                            items: availableStates
                                .map(
                                  (stateId) => DropdownMenuItem(
                                    child: Text(
                                      sim.states.firstWhere((state) => stateId == state.id).name,
                                      style: const TextStyle(color: white60),
                                    ),
                                    value: stateId,
                                  ),
                                )
                                .toList(),
                            // dropdownColor: lightBackgrounds,
                            dropdownColor: bordersColor,
                            onChanged: (String? value) {
                              if (value == null) {
                                print("Null copy state");
                                return;
                              }
                              copyState.value = value;
                              selectedLocations.clear();
                              locationNames.clear();
                              locationNames.addAll(sim.locations.where((loc) => loc.state == value).map((loc) => loc.name).toSet());
                              // locationNames.refresh();
                              // print(
                              //     "Adding list: ${sim.locations.where((loc) => loc.state == value).map((loc) => loc.name).toSet()}");
                            },
                            value: copyState.value,
                            underline: const SizedBox(),
                            borderRadius: BorderRadius.circular(12),
                          ),
                        );
                      },
                    ),
                  ),
                  const SizedBox(width: 10),
                  Row(
                    children: [
                      Checkbox(
                        value: ignoreSimObjects.value,
                        onChanged: ((value) => ignoreSimObjects.value = value!),
                        activeColor: brick,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(4),
                          side: const BorderSide(color: Colors.white),
                        ),
                        side: const BorderSide(color: Colors.white),
                      ),
                      const SizedBox(width: 5),
                      const Text("Ignore Objects", style: TextStyle(color: Colors.white)),
                    ],
                  ),
                  Row(
                    children: [
                      Checkbox(
                        value: copyNavigation.value,
                        onChanged: ((value) => copyNavigation.value = value!),
                        activeColor: brick,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(4),
                          side: const BorderSide(color: Colors.white),
                        ),
                        side: const BorderSide(color: Colors.white),
                      ),
                      const SizedBox(width: 5),
                      const Text("Copy Navigation", style: TextStyle(color: Colors.white)),
                    ],
                  ),
                ],
              ),
              const SizedBox(height: 10),
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  Obx(
                    () => selectedLocations.length == locationNames.length
                        ? TextButton(
                            child: const Text("Deselect All", style: TextStyle(color: Colors.white)),
                            onPressed: () {
                              selectedLocations.clear();
                              selectedLocations.refresh();
                            },
                          )
                        : TextButton(
                            child: const Text("Select All", style: TextStyle(color: Colors.white)),
                            onPressed: () {
                              selectedLocations.clear();
                              selectedLocations.addAll(locationNames);
                              selectedLocations.refresh();
                            },
                          ),
                  ),
                ],
              ),
              const SizedBox(height: 10),
              ...locationNames.map(
                (locName) => Obx(
                  () => Theme(
                    data: ThemeData(unselectedWidgetColor: Colors.white),
                    child: CheckboxListTile(
                      title: Text(locName, style: const TextStyle(color: Colors.white)),
                      value: selectedLocations.contains(locName),
                      activeColor: Colors.white,
                      checkColor: Colors.black,
                      // checkboxShape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(1), side: const BorderSide(color: Colors.white, width: 2)),
                      onChanged: (selected) {
                        if (selected == true) {
                          selectedLocations.add(locName);
                        } else {
                          selectedLocations.removeWhere((element) => element == locName);
                        }
                        selectedLocations.refresh();
                      },
                    ),
                  ),
                ),
              )
            ],
          ),
        ),
      ),
      actions: [
        TransparentButton(
          onPressed: () {
            Get.back();
          },
          label: "Cancel",
        ),
        OrangeButton(
          onPressed: () async {
            SimulationState stateToUse;
            final sameState = sim.states.firstWhereOrNull((state) => state.name == stateName.value);
            if (sameState != null) {
              /* final duplicateLocation = sim.locations.where((loc) => locationNames.contains(loc.name)).firstWhereOrNull(
                  (loc) =>
                      loc.state == sameState.id && loc.id == sim.locations[_simController.currentLocation.value].id);
              if (duplicateLocation != null) {
                Get.showSnackbar(const GetSnackBar(
                  title: "Error",
                  message: "State with same name already exists for one or more location(s)",
                  duration: Duration(seconds: 2),
                ));
                return;
              } */
              stateToUse = sameState;
            } else {
              stateToUse = SimulationState(id: nanoid(10), name: stateName.value);
              sim.states.add(stateToUse);
            }
            // final newLocation = SimulationLocation(name: locationName, sprites: []);
            final newLocations = <SimulationLocation>[];
            if (selectedLocations.isEmpty) {
              Get.showSnackbar(const GetSnackBar(
                title: "Error",
                message: "No location selected",
                duration: Duration(seconds: 2),
              ));
              return;
            }
            final sortedLocations = locationNames.where((locName) => selectedLocations.contains(locName));
            final newMasks = <Mask>[];
            // for (var locName in selectedLocations) {
            final newToOldLocationMapping = <String, String>{};
            for (var locName in sortedLocations) {
              final conflictLocation = sim.locations.firstWhereOrNull((loc) => loc.name == locName && loc.state == stateToUse.id);
              if (conflictLocation != null) {
                Get.showSnackbar(const GetSnackBar(
                  title: "Error",
                  message: "State with same name already exists for one or more location(s)",
                  duration: Duration(seconds: 2),
                ));
                return;
              }
              // debugPrint(
              //     "Copying location $locName with original state ${stateToUse.id} while all locations are: ${sim.locations.map((e) => "name: ${e.name}, state: ${e.state}").toList()}",
              //     wrapWidth: 10000);
              // final sourceLocation = sim.locations.firstWhere((loc) => loc.name == locName && loc.state == stateToUse.id);
              final copyLocation = sim.locations.firstWhere((loc) => loc.name == locName && loc.state == copyState.value);
              final newLocation = copyLocation.copy(id: nanoid(12), ignoreObjects: ignoreSimObjects.value);
              final locationMasks = sim.masks.where((mask) => mask.locationId == copyLocation.id);
              final oldToNewMaskMapping = <String, String>{};
              final newLocationMasks = locationMasks.map((mask) {
                final newMask = mask.copy()..locationId = newLocation.id;
                oldToNewMaskMapping[mask.id] = newMask.id;
                return newMask;
              }).toList();
              newMasks.addAll(newLocationMasks);
              newLocation.migrateMasks(oldToNewMaskMapping: oldToNewMaskMapping);
              // TODO: masks should link up with the sim objects assigned to them in the previous state
              // final maskCopies =
              //     sim.masks.where((mask) => mask.locationId == sourceLocation.id).map((mask) => mask.copy()..locationId = newLocation.id).toList();
              newLocation.state = stateToUse.id;
              newLocations.add(newLocation);
              newToOldLocationMapping[newLocation.id] = copyLocation.id;
              // if (maskCopies.isNotEmpty) {
              //   sim.masks.addAll(maskCopies);
              // }
            }
            // sim.locations.add(newLocation);

            // NOTE: ignore created masks if ignoreSimObjects is true
            if (!ignoreSimObjects.value) {
              sim.masks.addAll(newMasks);
            }
            if (copyNavigation.value) {
              final newNavigations = <SimulationNavigation>[];
              final oldToNewLocationMapping = <String, String>{};
              newToOldLocationMapping.forEach((key, value) {
                oldToNewLocationMapping[value] = key;
              });
              for (var loc in newLocations) {
                final copyNavigation = sim.navigations.where((nav) => nav.from == newToOldLocationMapping[loc.id]);
                final newNavigation = copyNavigation
                    .map(
                      (nav) => nav.copy()
                        ..from = loc.id
                        ..to = oldToNewLocationMapping[nav.to] ?? nav.to,
                    )
                    .toList();
                newNavigations.addAll(newNavigation);
              }
              sim.navigations.addAll(newNavigations);
            }
            sim.locations.addAll(newLocations);
            _simController.currentSim.value = sim;
            _simController.currentSim.refresh();
            _simController.currentLocation.refresh();
            _simController.currentState.refresh();
            _simController.signalStream.add("new-state");
            Get.back();
          },
          label: "Create",
        ),
      ],
    );
  }
}

class EditStateDialog extends StatelessWidget {
  final SimulationState state;

  EditStateDialog({
    Key? key,
    required this.state,
  }) : super(key: key);

  final name = "".obs;

  @override
  Widget build(BuildContext context) {
    SimController _simController = Get.find();
    final sim = _simController.currentSim.value!;
    return ContentDialog(
        title: "Edit State",
        content: Column(
          children: [
            TextFormField(
              initialValue: state.name,
              style: const TextStyle(color: Colors.white),
              onChanged: ((value) {
                name.value = value;
              }),
            )
          ],
        ),
        actions: [
          TransparentButton(
            onPressed: () {
              Get.back();
            },
            label: "Cancel",
          ),
          OrangeButton(
            onPressed: () {
              if (name.value.isEmpty) return;
              if (name.value == state.name) return Get.back();
              final stateIndex = sim.states.indexWhere((element) => element.id == state.id);
              sim.states[stateIndex].name = name.value;
              _simController.currentSim.refresh();
              Get.back();
            },
            label: "Edit",
          ),
        ]);
  }
}

// TODO: use SelectImageDialog component
class EditLocationBackground extends StatelessWidget {
  EditLocationBackground({
    Key? key,
  }) : super(key: key);

  final _simController = Get.find<SimController>();
  final RxnString selectedImage = RxnString("");
  final rotation = 0.0.obs;
  final scale = 1.0.obs;
  final offset = Offset.zero.obs;
  final brightness = 0.0.obs;

  @override
  Widget build(BuildContext context) {
    final sim = _simController.currentSim.value!;
    rotation.value = sim.locations[_simController.currentLocation.value].imageRotation;
    scale.value = sim.locations[_simController.currentLocation.value].imageScale;
    offset.value = sim.locations[_simController.currentLocation.value].imageOffset;
    brightness.value = sim.locations[_simController.currentLocation.value].imageBrightness;
    return ContentDialog(
        title: "Edit Location Background",
        content: SingleChildScrollView(
          child: Column(
            children: [
              const Text(
                "Upload Image",
                style: TextStyle(color: Colors.white),
              ),
              const SizedBox(height: 11),
              PickLocationImage(
                multi: false,
                onImageSelected: (image) {
                  if (image.isEmpty) return;
                  selectedImage.value = image[0]!;
                  offset.value = Offset.zero;
                },
              ),
              const SizedBox(height: 10),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  TransparentButton(
                    onPressed: () {
                      selectedImage.value = null;
                    },
                    label: "Remove Background",
                  ),
                ],
              ),
              const SizedBox(height: 10),
              // Image
              Obx(() {
                if (selectedImage.value == null) {
                  return const SizedBox(
                    height: 40,
                    child: Text(
                      "No background",
                      style: TextStyle(color: Colors.white, fontSize: 16),
                    ),
                  );
                }
                return Center(
                  child: Container(
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.white, width: 1),
                    ),
                    child: SizedBox(
                      height: 640 / 2,
                      width: 940 / 2,
                      child: ClipRRect(
                        clipBehavior: Clip.hardEdge,
                        child: Transform.scale(
                          scale: scale.value,
                          origin: offset.value,
                          child: Transform.rotate(
                            angle: rotation.value * pi / 180,
                            child: GestureDetector(
                              onPanUpdate: (details) {
                                offset.value -= Offset(details.delta.dx, details.delta.dy);
                              },
                              child: Image.file(
                                File(
                                  selectedImage.value!.isNotEmpty ? selectedImage.value! : sim.locations[_simController.currentLocation.value].image,
                                ),
                                fit: BoxFit.contain,
                                height: 640 / 2,
                                width: 940 / 2,
                                color: brightness.value < 0
                                    ? Colors.black.withOpacity(brightness.value / -20)
                                    : Colors.white.withOpacity(brightness.value / 20),
                                colorBlendMode: brightness.value < 0 ? BlendMode.darken : BlendMode.lighten,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                );
              }),
              // const SizedBox(height: 10),
              // Rotate slider
              Obx(() {
                return Center(
                  child: SizedBox(
                    width: Get.width * 0.6,
                    child: Row(
                      children: [
                        const Text(
                          "Rotation",
                          style: TextStyle(color: Colors.white),
                        ),
                        const SizedBox(width: 10),
                        Expanded(
                          child: SliderTheme(
                            data: SliderThemeData(
                              thumbShape: CustomSliderThumbShape(
                                thumbRadius: 18.0,
                                sliderValue: rotation.value.toPrecision(2),
                                thumbColor: brick,
                                textColor: Colors.white,
                              ),
                            ),
                            child: Slider(
                              value: rotation.value,
                              onChanged: (value) {
                                rotation.value = value;
                              },
                              min: -180,
                              max: 180,
                              divisions: 360,
                              label: '${rotation.value.round()}',
                              activeColor: brick,
                              inactiveColor: brick.withOpacity(0.7),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                );
              }),
              // const SizedBox(height: 10),
              // Scale slider
              Obx(() {
                return Center(
                  child: SizedBox(
                    width: Get.width * 0.6,
                    child: Row(
                      children: [
                        const Text(
                          "Scale",
                          style: TextStyle(color: Colors.white),
                        ),
                        const SizedBox(width: 10),
                        Expanded(
                          child: SliderTheme(
                            data: SliderThemeData(
                              thumbShape: CustomSliderThumbShape(
                                thumbRadius: 18.0,
                                sliderValue: scale.value.toPrecision(2),
                                thumbColor: brick,
                                textColor: Colors.white,
                              ),
                            ),
                            child: Slider(
                              value: scale.value,
                              onChanged: (value) {
                                scale.value = value;
                                if (value == 1) {
                                  offset.value = Offset.zero;
                                }
                              },
                              min: 0.1,
                              max: 5,
                              divisions: 49,
                              label: "Scale",
                              activeColor: brick,
                              inactiveColor: brick.withOpacity(0.7),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                );
              }),
              // Brightness
              Obx(() {
                return Center(
                  child: SizedBox(
                    width: Get.width * 0.6,
                    child: Row(
                      children: [
                        const Text(
                          "Brightness",
                          style: TextStyle(color: Colors.white),
                        ),
                        const SizedBox(width: 10),
                        Expanded(
                          child: SliderTheme(
                            data: SliderThemeData(
                              thumbShape: CustomSliderThumbShape(
                                thumbRadius: 18.0,
                                sliderValue: double.parse(brightness.value.toStringAsFixed(1)),
                                thumbColor: brick,
                                textColor: Colors.white,
                              ),
                            ),
                            child: Slider(
                              value: brightness.value,
                              onChanged: (value) {
                                brightness.value = value;
                              },
                              min: -20,
                              max: 20,
                              divisions: 41,
                              label: "Brightness",
                              activeColor: brick,
                              inactiveColor: brick.withOpacity(0.7),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                );
              }),
            ],
          ),
        ),
        actions: [
          TransparentButton(
            onPressed: () {
              Get.back();
            },
            label: "Cancel",
          ),
          OrangeButton(
            onPressed: () {
              // if (selectedImage.value.isEmpty) {
              //   Get.snackbar("Error", "No background image selected");
              //   return;
              // }
              if (selectedImage.value == null) {
                sim.locations[_simController.currentLocation.value].image = "";
              } else if (selectedImage.value!.isNotEmpty) {
                sim.locations[_simController.currentLocation.value].image = selectedImage.value!;
              }
              sim.locations[_simController.currentLocation.value].imageRotation = rotation.value;
              sim.locations[_simController.currentLocation.value].imageScale = scale.value;
              sim.locations[_simController.currentLocation.value].imageOffset = offset.value;
              sim.locations[_simController.currentLocation.value].imageBrightness = brightness.value;
              _simController.backgroundNotifier.value += 1;
              Get.back();
            },
            label: "Save",
          ),
        ]);
  }
}

class EditLocationBackgroundColor extends StatelessWidget {
  EditLocationBackgroundColor({
    Key? key,
  }) : super(key: key);

  final _simController = Get.find<SimController>();
  final selectedColor = Colors.black.obs;

  @override
  Widget build(BuildContext context) {
    final sim = _simController.currentSim.value!;
    if (sim.locations[_simController.currentLocation.value].color.isNotEmpty) {
      selectedColor.value = Color(int.parse(sim.locations[_simController.currentLocation.value].color, radix: 16));
    }
    return ContentDialog(
        title: "Edit Location Background Color",
        content: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            SizedBox(
              width: 350,
              child: ColorPicker(
                pickerColor: selectedColor.value,
                hexInputBar: false,
                showLabel: false,
                displayThumbColor: true,
                enableAlpha: false,
                portraitOnly: true,
                pickerAreaHeightPercent: 1.0,
                pickerAreaBorderRadius: const BorderRadius.all(Radius.circular(8)),
                onColorChanged: (value) {
                  selectedColor.value = value;
                },
              ),
            ),
          ],
        )
        /* Row(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            SizedBox(
              height: 400,
              child: ColorPicker(
                pickerColor: selectedColor.value,
                onColorChanged: (value) {
                  selectedColor.value = value;
                },
                enableAlpha: false,
                labelTextStyle: const TextStyle(color: Colors.white),
                labelTypes: const [],
              ),
            )
          ],
        ) */
        ,
        actions: [
          TransparentButton(
            onPressed: () {
              Get.back();
            },
            label: "Cancel",
          ),
          OrangeButton(
            onPressed: () {
              sim.locations[_simController.currentLocation.value].color = selectedColor.value.toHexString();
              _simController.backgroundNotifier.value += 1;
              Get.back();
            },
            label: "Save",
          ),
        ]);
  }
}

class EditLocationNameDialog extends StatelessWidget {
  final SimulationLocation location;

  EditLocationNameDialog({
    Key? key,
    required this.location,
  }) : super(key: key);

  final newName = "".obs;

  @override
  Widget build(BuildContext context) {
    final _simController = Get.find<SimController>();
    final sim = _simController.currentSim.value!;
    return ContentDialog(
      title: "Edit Location Name",
      content: Column(
        children: [
          TextFormField(
            style: const TextStyle(color: Colors.white),
            onChanged: ((value) {
              newName.value = value;
            }),
            initialValue: location.name,
          )
        ],
      ),
      actions: [
        TransparentButton(
          onPressed: () {
            Get.back();
          },
          label: "Cancel",
        ),
        OrangeButton(
          onPressed: () {
            if (newName.value.isEmpty) return;
            if (newName.value == location.name) return Get.back();
            sim.locations.firstWhere((element) => element.id == location.id).name = newName.value;
            _simController.currentSim.refresh();
            Get.back();
          },
          label: "Edit",
        )
      ],
    );
  }
}

class TimeLineEntry {
  final double time;
  final String label;

  TimeLineEntry({
    required this.time,
    required this.label,
  });
}

class TimelineDialog extends StatelessWidget {
  TimelineDialog({Key? key}) : super(key: key);

  final timeline = <String, List<TimeLineEntry>>{}.obs;

  addComponentTimelineEntry(SimulationLocation loc, SimObject element) {
    String prefix = element.id;
    switch (element.runtimeType) {
      case SimSprite:
        prefix = (element as SimSprite).name;
        break;
      case SimLabel:
        prefix = (element as SimLabel).name;
        break;
      case SimPerson:
        prefix = (element as SimPerson).name;
        break;
      case SimContainer:
        prefix = (element as SimContainer).name;
        break;
    }
    if (element.fadeInWhen != 0) {
      timeline[loc.id]!.add(TimeLineEntry(
        time: element.fadeInWhen,
        label: "$prefix Fade In",
      ));
    }
    if (element.fadeOutWhen != 0) {
      timeline[loc.id]!.add(TimeLineEntry(
        time: element.fadeOutWhen + element.fadeInWhen + element.fadeInDuration,
        label: "$prefix Fade Out",
      ));
    }
    if (element is SimLocationJumper && element.delay != 0) {
      final _simController = Get.find<SimController>();
      final toName = _simController.currentSim.value!.locations.firstWhere((loc) => loc.id == element.to).name;
      final stateName = _simController.currentSim.value!.states.firstWhere((s) => s.id == loc.state).name;
      timeline[loc.id]!.add(TimeLineEntry(
        time: element.delay.toDouble(),
        label: "$prefix Auto jumps to $toName ($stateName)",
      ));
    }
  }

  prepareTimeline() {
    final _simController = Get.find<SimController>();
    final sim = _simController.currentSim.value!;

    for (var loc in sim.locations) {
      timeline[loc.id] = [];
      for (var element in loc.sprites) {
        addComponentTimelineEntry(loc, element);
      }
      for (var element in loc.images) {
        addComponentTimelineEntry(loc, element);
      }
      for (var element in loc.shapes) {
        addComponentTimelineEntry(loc, element);
      }
      for (var element in loc.jumpers) {
        addComponentTimelineEntry(loc, element);
      }
      for (var element in loc.texts) {
        addComponentTimelineEntry(loc, element);
      }
      for (var element in loc.labels) {
        addComponentTimelineEntry(loc, element);
      }
      for (var element in loc.containers) {
        addComponentTimelineEntry(loc, element);
      }
      for (var element in loc.people) {
        addComponentTimelineEntry(loc, element);
      }
      for (var element in loc.timers) {
        addComponentTimelineEntry(loc, element);
      }
      if (timeline[loc.id]!.isEmpty) {
        timeline.remove(loc.id);
      }
    }

    // timeline.sort((a, b) => a.time.compareTo(b.time));
    // print(timeline);
  }

  @override
  Widget build(BuildContext context) {
    final _simController = Get.find<SimController>();
    prepareTimeline();

    return ContentDialog(
        title: "Timeline",
        content: SingleChildScrollView(
          child: Column(
            children: timeline.entries.map(
              (kv) {
                final location = _simController.currentSim.value!.locations.firstWhere((loc) => loc.id == kv.key);
                kv.value.sort((a, b) => a.time.compareTo(b.time));
                return Column(
                  children: [
                    ListTile(
                      title: Text(
                        "${location.name} (${_simController.currentSim.value!.states.firstWhere((s) => s.id == location.state).name})",
                        style: const TextStyle(color: Colors.white, fontSize: 20),
                      ),
                    ),
                    ...kv.value.map(
                      (entry) => ListTile(
                        title: Text(
                          entry.label,
                          style: const TextStyle(color: Colors.white),
                        ),
                        subtitle: Text(
                          "${entry.time} seconds",
                          style: const TextStyle(color: Colors.white),
                        ),
                      ),
                    ),
                    const Divider(
                      height: 1,
                      color: white60,
                    ),
                  ],
                );
              },
            ).toList(),
          ),
        ),
        actions: [
          OrangeButton(
            onPressed: () {
              Get.back();
            },
            label: "OK",
          )
        ]);
  }
}

class LocationsPopupButton extends StatelessWidget {
  LocationsPopupButton({
    Key? key,
  }) : super(key: key);

  final filterState = "".obs;

  @override
  Widget build(BuildContext context) {
    final _simController = Get.find<SimController>();
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TextButton(
          onPressed: () {
            showPopover(
              width: 500,
              height: 270,
              context: context,
              direction: PopoverDirection.bottom,
              arrowDyOffset: -20,
              backgroundColor: lightBackgrounds,
              bodyBuilder: (context) {
                return Obx(
                  () => Container(
                    padding: const EdgeInsets.only(left: 20, top: 10, bottom: 10, right: 10),
                    child: _simController.currentSim.value == null
                        ? const SizedBox()
                        : Row(
                            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              Column(
                                children: [
                                  SizedBox(
                                    height: 240,
                                    width: 200,
                                    child: SingleChildScrollView(
                                      child: Column(
                                        children: [
                                          //Locations
                                          Row(
                                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                            children: [
                                              const Text(
                                                "Locations",
                                                style: TextStyle(color: Colors.white, fontSize: 20),
                                              ),
                                              IconButton(
                                                onPressed: () {
                                                  Get.dialog(NewLocationDialog());
                                                },
                                                icon: const Icon(Icons.add, color: white60, size: 16),
                                              ),
                                            ],
                                          ),
                                          const Divider(height: 1, color: white60),
                                          SizedBox(
                                            height: 200,
                                            child: ReorderableListView(
                                              buildDefaultDragHandles: false,
                                              children: _simController.currentSim.value!.locations
                                                  .where((element) => filterState.isEmpty ? true : element.state == filterState.value)
                                                  .mapIndexed(
                                                (index, location) {
                                                  final actualIndex =
                                                      _simController.currentSim.value!.locations.indexWhere((loc) => loc.id == location.id);
                                                  return Row(
                                                    key: ValueKey(location.id + "__" + index.toString()),
                                                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                                    children: [
                                                      ReorderableDragStartListener(
                                                        child: const Icon(
                                                          Icons.drag_indicator,
                                                          color: white60,
                                                          size: 14,
                                                        ),
                                                        index: index,
                                                      ),
                                                      Expanded(
                                                        child: TextButton(
                                                          child: Text(
                                                            "${location.name} (${_simController.currentSim.value!.states.firstWhere((s) => s.id == location.state).name})",
                                                            style: TextStyle(
                                                              fontSize: 14,
                                                              color: _simController
                                                                          .currentSim.value!.locations[_simController.currentLocation.value].id ==
                                                                      location.id
                                                                  ? yellow
                                                                  : Colors.white,
                                                            ),
                                                          ),
                                                          onPressed: () {
                                                            _simController.selectedSimObjectIndex.value = -1;
                                                            _simController.selectedType.value = null;
                                                            _simController.currentLocation.value = actualIndex;
                                                          },
                                                        ),
                                                      ),
                                                      SizedBox(
                                                        width: 20,
                                                        child: _simController.currentSim.value!.initialLocationId == location.id
                                                            ? const Icon(Icons.home, color: Colors.white, size: 16)
                                                            : null,
                                                      ),
                                                      PopupMenuButton(
                                                        icon: const Icon(
                                                          Icons.more_vert,
                                                          color: white60,
                                                          size: 16,
                                                        ),
                                                        color: lightBackgrounds,
                                                        tooltip: "",
                                                        itemBuilder: (context) => [
                                                          PopupMenuItem(
                                                            child: const Row(
                                                              children: [
                                                                Icon(Icons.edit, color: Colors.white, size: 16),
                                                                SizedBox(width: 9),
                                                                Text(
                                                                  "Edit",
                                                                  style: TextStyle(color: Colors.white),
                                                                ),
                                                              ],
                                                            ),
                                                            onTap: () {
                                                              Future.delayed(const Duration(milliseconds: 100), () async {
                                                                Get.dialog(
                                                                  EditLocationNameDialog(
                                                                    location: location,
                                                                  ),
                                                                );
                                                              });
                                                            },
                                                          ),
                                                          ..._simController.currentSim.value!.locations.isNotEmpty
                                                              ? [
                                                                  PopupMenuItem(
                                                                    child: const Row(
                                                                      children: [
                                                                        Icon(Icons.delete, color: Colors.white, size: 16),
                                                                        SizedBox(width: 9),
                                                                        Text(
                                                                          "Delete",
                                                                          style: TextStyle(color: Colors.white),
                                                                        ),
                                                                      ],
                                                                    ),
                                                                    onTap: () {
                                                                      Future.delayed(const Duration(milliseconds: 100), () async {
                                                                        Get.dialog(
                                                                          ConfirmationDialog(
                                                                            message:
                                                                                "Are you sure you want to delete location ${location.name} (${_simController.currentSim.value!.states.firstWhere((s) => s.id == location.state).name})",
                                                                            onConfirmed: () {
                                                                              deleteLocation(location);
                                                                              Get.back();
                                                                            },
                                                                          ),
                                                                        );
                                                                      });
                                                                    },
                                                                  )
                                                                ]
                                                              : [],
                                                          PopupMenuItem(
                                                            child: const Row(
                                                              // TODO: disable when location is initial location
                                                              children: [
                                                                // set icon color to grey when location is initial location
                                                                Icon(Icons.home, color: Colors.white, size: 16),
                                                                SizedBox(width: 9),
                                                                Text(
                                                                  "Set as initial location",
                                                                  style: TextStyle(color: Colors.white),
                                                                ),
                                                              ],
                                                            ),
                                                            onTap: () {
                                                              _simController.currentSim.value!.initialLocationId = location.id;
                                                              _simController.currentSim.value!.initialStateId = location.state;
                                                              _simController.currentSim.refresh();
                                                            },
                                                          ),
                                                        ],
                                                      ),
                                                    ],
                                                  );
                                                },
                                              ).toList(),
                                              onReorder: (oldIndex, newIndex) async {
                                                final filteredStateLocations = _simController.currentSim.value!.locations
                                                    .where((element) => filterState.isEmpty ? true : element.state == filterState.value)
                                                    .toList();
                                                final actualOld = filteredStateLocations[oldIndex];
                                                final actualOldIndex =
                                                    _simController.currentSim.value!.locations.indexWhere((loc) => loc.id == actualOld.id);
                                                // final actualNew = filteredStateLocations[newIndex >= filteredStateLocations.length? newIndex - 1 : newIndex];
                                                final actualNew =
                                                    filteredStateLocations[newIndex >= filteredStateLocations.length ? newIndex - 1 : newIndex];
                                                final actualNewIndex =
                                                    _simController.currentSim.value!.locations.indexWhere((loc) => loc.id == actualNew.id);
                                                final isLast = newIndex >= filteredStateLocations.length;

                                                // final loc = _simController.currentSim.value!.locations[actualOldIndex];
                                                final loc = _simController.currentSim.value!.locations.removeAt(actualOldIndex);
                                                // _simController.currentSim.value!.locations.insert(
                                                //     actualNewIndex > actualOldIndex ? (isLast ? actualNewIndex : actualNewIndex - 1) : actualNewIndex,
                                                //     loc);
                                                _simController.currentSim.value!.locations
                                                    .insert(actualNewIndex > actualOldIndex && !isLast ? (actualNewIndex - 1) : actualNewIndex, loc);
                                                // final loc = _simController.currentSim.value!.locations.removeAt(actualOldIndex);
                                                // _simController.currentSim.value!.locations.insert(actualNewIndex, loc);
                                                // _simController.currentLocation.value = actualNewIndex;
                                                _simController.currentSim.refresh();
                                                // await Future.delayed(const Duration(milliseconds: 100));
                                                _simController.currentLocation.value = actualNewIndex - (newIndex > oldIndex ? 1 : 0);
                                                // _simController.currentLocation.value =
                                                //     _simController.currentSim.value!.locations.indexWhere((loc) => loc.id == actualNew.id);
                                                // _simController.currentLocation.refresh();
                                                await Future.delayed(const Duration(milliseconds: 100));
                                                _simController.signalStream.add("reorder");
                                              },
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                              const VerticalDivider(color: white60),
                              //States
                              SizedBox(
                                height: 250,
                                width: 200,
                                child: SingleChildScrollView(
                                  child: Column(
                                    children: [
                                      Row(
                                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                        children: [
                                          const Text(
                                            "States",
                                            style: TextStyle(color: Colors.white, fontSize: 20),
                                          ),
                                          IconButton(
                                              onPressed: () {
                                                Get.dialog(NewStateDialog());
                                              },
                                              icon: const Icon(Icons.add, color: white60, size: 16))
                                        ],
                                      ),
                                      const Divider(height: 1, color: white60),
                                      ..._simController.currentSim.value!.states.map((state) {
                                        return Row(
                                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                          children: [
                                            Expanded(
                                              child: GestureDetector(
                                                child:
                                                    Text(state.name, style: TextStyle(color: filterState.value == state.id ? yellow : Colors.white)),
                                                onTap: () {
                                                  if (filterState.value == state.id) {
                                                    filterState.value = "";
                                                  } else {
                                                    filterState.value = state.id;
                                                    final currentLocationName =
                                                        _simController.currentSim.value!.locations[_simController.currentLocation.value].name;
                                                    final upcomingLocId = _simController.currentSim.value!.locations
                                                        .indexWhere((loc) => loc.state == state.id && loc.name == currentLocationName);
                                                    if (upcomingLocId != -1) {
                                                      _simController.currentLocation.value = upcomingLocId;
                                                    }
                                                  }
                                                },
                                              ),
                                            ),
                                            SizedBox(
                                              width: 20,
                                              child: _simController.currentSim.value!.initialStateId == state.id
                                                  ? const Icon(Icons.home, color: Colors.white, size: 16)
                                                  : null,
                                            ),
                                            const SizedBox(width: 5),
                                            IconButton(
                                              onPressed: () {
                                                Get.dialog(EditStateDialog(
                                                  state: state,
                                                ));
                                              },
                                              icon: const Icon(Icons.edit, color: white60, size: 16),
                                            ),
                                            const SizedBox(width: 3),
                                            _simController.currentSim.value!.states.length > 1
                                                ? IconButton(
                                                    icon: const Icon(Icons.remove, color: white60),
                                                    onPressed: () => deleteState(state),
                                                  )
                                                : const SizedBox()
                                          ],
                                        );
                                      }).toList(),
                                    ],
                                  ),
                                ),
                              )
                            ],
                          ),
                  ),
                );
              },
            );
          },
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 5),
            decoration: BoxDecoration(
              color: brick,
              borderRadius: BorderRadius.circular(5),
            ),
            child: const Text(
              "Locations & States",
              style: TextStyle(
                color: Colors.white,
                fontSize: 14,
              ),
            ),
          ),
        ),
        const SizedBox(height: 10),
        Obx(
          () => Text(
            "${_simController.currentSim.value!.locations[_simController.currentLocation.value].name} (${_simController.currentSim.value!.states.firstWhere((s) => s.id == _simController.currentSim.value!.locations[_simController.currentLocation.value].state).name})",
            style: const TextStyle(color: yellow),
          ),
        ),
      ],
    );
  }

  deleteLocation(SimulationLocation location) {
    final _simController = Get.find<SimController>();
    _simController.currentSim.value!.navigations.removeWhere((nav) => location.id == nav.from || location.id == nav.to);
    _simController.currentSim.value!.locations.remove(location);
    _simController.currentLocation.value = 0;
    _simController.currentSim.refresh();
  }

  deleteState(SimulationState state) {
    Get.dialog(
      ConfirmationDialog(
        message: "Are you sure you want to delete this state?",
        onConfirmed: () {
          bool goToFirstState = false;
          final _simController = Get.find<SimController>();
          if (_simController.currentSim.value!.locations[_simController.currentLocation.value].state == state.id) {
            goToFirstState = true;
          }
          final stateLocationIds = _simController.currentSim.value!.locations.where((loc) => loc.state == state.id).map((loc) => loc.id).toList();
          _simController.currentSim.value!.navigations.removeWhere((nav) => stateLocationIds.contains(nav.from) || stateLocationIds.contains(nav.to));
          _simController.currentSim.value!.locations.removeWhere((loc) => loc.state == state.id);
          _simController.currentSim.value!.states.removeWhere((element) => element.id == state.id);
          if (goToFirstState) {
            _simController.currentState.value = 0;
            _simController.currentLocation.value = _simController.currentSim.value!.locations
                .indexWhere((loc) => loc.state == _simController.currentSim.value!.states[_simController.currentState.value].id);
          }
          filterState.value = "";
          _simController.currentSim.refresh();
          _simController.currentState.refresh();
          _simController.currentLocation.refresh();
        },
      ),
    );
  }
}
