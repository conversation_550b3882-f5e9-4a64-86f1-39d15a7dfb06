import 'package:flutter/material.dart';

class NoGradientRoundedRectSliderTrackShape extends SliderTrackShape {
  final double borderRadius;

  NoGradientRoundedRectSliderTrackShape({this.borderRadius = 2});

  @override
  Rect getPreferredRect({
    required RenderBox parentBox,
    Offset offset = Offset.zero,
    required SliderThemeData sliderTheme,
    bool isEnabled = false,
    bool isDiscrete = false,
  }) {
    final double trackHeight = sliderTheme.trackHeight!;
    final double trackLeft = offset.dx;
    final double trackTop = offset.dy + (parentBox.size.height - trackHeight) / 2;
    final double trackWidth = parentBox.size.width;
    return Rect.fromLTWH(trackLeft, trackTop, trackWidth, trackHeight);
  }

  @override
  void paint(
    PaintingContext context,
    Offset offset, {
    required RenderBox parentBox,
    required SliderThemeData sliderTheme,
    required Animation<double> enableAnimation,
    required TextDirection textDirection,
    required Offset thumbCenter,
    Offset? secondaryOffset,
    bool isDiscrete = false,
    bool isEnabled = false,
  }) {
    if (sliderTheme.trackHeight == null) {
      return;
    }
    final Paint activePaint = Paint()..color = Colors.white;
    final Paint inactivePaint = Paint()..color = Colors.black;

    final Rect trackRect = getPreferredRect(
      parentBox: parentBox,
      offset: offset,
      sliderTheme: sliderTheme,
      isEnabled: isEnabled,
      isDiscrete: isDiscrete,
    );

    final RRect roundedTrackRect = RRect.fromRectAndRadius(trackRect, Radius.circular(borderRadius));

    final Rect activeTrackRect = Rect.fromPoints(
      trackRect.topLeft,
      Offset(thumbCenter.dx, trackRect.bottomRight.dy),
    );

    final RRect activeRoundedTrackRect = RRect.fromRectAndRadius(activeTrackRect, Radius.circular(borderRadius));

    ///The paint method first draws the entire track with inactivePaint and then draws
    ///the active part of the track with activePaint.
    context.canvas.drawRRect(roundedTrackRect, inactivePaint);
    context.canvas.drawRRect(activeRoundedTrackRect, activePaint);
  }
}
