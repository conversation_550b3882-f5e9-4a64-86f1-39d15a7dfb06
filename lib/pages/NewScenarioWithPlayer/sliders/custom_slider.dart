import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:get/get.dart';

import 'package:simsushare_player/pages/NewScenarioWithPlayer/sliders/bw_gradient_rectangle_slider_track.dart';
import 'package:simsushare_player/pages/NewScenarioWithPlayer/sliders/no_gradient_rounded_rectangle_slider_track.dart';
import 'package:simsushare_player/pages/NewScenarioWithPlayer/sliders/sidebar_slider_types_enum.dart';

class SidebarPanelSlider extends StatelessWidget {
  final String title;
  // final RxDouble sliderType;
  // final SimController simController;
  final RxDouble sliderValue;
  final SidebarSliderProperty sidebarSlider;
  final TextEditingController controller;
  final Function(double) onChanged;

  ///A flag to select Black and White gradient slider
  final SliderColorType sliderColorType;
  final double minValue;
  final double maxValue;
  final int? textPrecision;
  final String? textUnit;

  const SidebarPanelSlider({
    Key? key,
    required this.title,
    // required this.sliderType,
    // required this.simController,
    required this.sliderValue,
    required this.sidebarSlider,
    required this.sliderColorType,
    required this.controller,
    required this.minValue,
    required this.maxValue,
    required this.onChanged,
    this.textPrecision,
    this.textUnit,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final focusNode = FocusNode();
    focusNode.addListener(() {
      print("Focus node has focus: ${focusNode.hasFocus}");
      if (!focusNode.hasFocus) {
        onChanged(double.parse(textUnit == null ? controller.text : controller.text.split(textUnit!)[0]));
      }
    });
    return Obx(
      () => Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                title,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
              Container(
                width: sliderColorType == SliderColorType.rainbowGradient ? 65 : 52,
                height: 32,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8),
                  color: Colors.white.withOpacity(0.05),
                ),
                child: Center(
                  child: TextField(
                    controller: controller
                      ..text = textUnit != null
                          ? "${sliderValue.value.toStringAsFixed(textPrecision ?? 3)} $textUnit"
                          : sliderValue.value.toStringAsFixed(textPrecision ?? 2),
                    keyboardType: TextInputType.number,
                    style: const TextStyle(color: Colors.white, fontSize: 14),
                    decoration: const InputDecoration(
                      contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    ),
                    onSubmitted: (newText) => onChanged(double.parse(newText.isNotEmpty ? newText : minValue.toString())),
                    onChanged: (value) {
                      // onChanged(double.parse(value.isNotEmpty ? value : minValue.toString()));
                      controller.text = value;
                    },
                    focusNode: focusNode,
                  ),
                ),
              ),
            ],
          ),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 15),
            child: SliderTheme(
              data: SliderThemeData(
                trackHeight: 13,
                thumbShape: CustomSliderThumbShape(
                  thumbRadius: 15.0,
                  sliderValue: clampDouble(sliderValue.value.toPrecision(2), minValue, maxValue),
                  thumbColor: Colors.white,
                  textColor: Colors.black,
                  textFontSize: 12,
                ),
                trackShape:
                    sliderColorType == SliderColorType.bwGradient ? BWGradientRectSliderTrackShape() : NoGradientRoundedRectSliderTrackShape(),
                thumbColor: Colors.white,
                inactiveTrackColor: Colors.black,
                activeTrackColor: Colors.white,
              ),
              child: Slider(
                value: clampDouble(sliderValue.value, minValue, maxValue),
                min: minValue,
                max: maxValue,
                divisions: (maxValue - minValue) ~/ 0.1,
                onChanged: (newValue) => onChanged(newValue),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class CustomSliderThumbShape extends SliderComponentShape {
  final double thumbRadius;
  final double sliderValue;
  final Color thumbColor;
  final Color textColor;
  final double? textFontSize;

  CustomSliderThumbShape({
    required this.thumbRadius,
    required this.sliderValue,
    required this.thumbColor,
    required this.textColor,
    this.textFontSize,
  });

  @override
  Size getPreferredSize(bool isEnabled, bool isDiscrete) {
    return Size(thumbRadius * 2, thumbRadius * 2);
  }

  @override
  void paint(
    PaintingContext context,
    Offset center, {
    required Animation<double> activationAnimation,
    required Animation<double> enableAnimation,
    required bool isDiscrete,
    required TextPainter labelPainter,
    required RenderBox parentBox,
    required SliderThemeData sliderTheme,
    required TextDirection textDirection,
    required double value,
    required double textScaleFactor,
    required Size sizeWithOverflow,
  }) {
    final Canvas canvas = context.canvas;
    final Paint thumbPaint = Paint()..color = thumbColor;
    final TextPainter textPainter = TextPainter(
      text: TextSpan(
          text: sliderValue.toString(),
          style: TextStyle(
            color: textColor,
            fontSize: textFontSize ?? 14,
          )),
      textDirection: textDirection,
    );
    canvas.drawCircle(center, thumbRadius, thumbPaint);
    textPainter.layout();
    textPainter.paint(canvas, Offset(center.dx - textPainter.width / 2, center.dy - textPainter.height / 2));
  }
}
