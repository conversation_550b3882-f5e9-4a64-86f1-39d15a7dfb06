import 'package:flutter/material.dart';
import 'package:flutter_colorpicker/flutter_colorpicker.dart';
import 'package:get/get.dart';
import 'package:simsushare_player/controllers/SimController.dart';
import 'package:simsushare_player/pages/NewScenarioWithPlayer/sliders/color_value_rectangle_slider_tracker.dart';
import 'package:simsushare_player/pages/NewScenarioWithPlayer/sliders/no_gradient_rounded_rectangle_slider_track.dart';
import 'package:simsushare_player/pages/NewScenarioWithPlayer/sliders/rainbow_gradient_rectangle_slider_track.dart';
import 'package:simsushare_player/pages/NewScenarioWithPlayer/sliders/rectangle_slider_thumb_shape.dart';
import 'package:simsushare_player/pages/NewScenarioWithPlayer/sliders/saturation_rectangle_slider_tracker.dart';

class CustomColorPickerPage extends StatelessWidget {
  final SimController simController;
  final ColorController colorController = Get.put(ColorController());

  CustomColorPickerPage({
    Key? key,
    required this.simController,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      return SizedBox(
        width: 200,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  "Color",
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Container(
                  width: 70,
                  height: 32,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8),
                    color: Colors.white.withOpacity(0.05),
                  ),
                  child: Center(
                    child: Text(
                      colorToHex(
                        HSVColor.fromAHSV(colorController.intensity.value, colorController.hue.value, colorController.saturation.value, 1.0)
                            .toColor(),
                        includeHashSign: true,
                        enableAlpha: false,
                        toUpperCase: true,
                      ),
                      style: const TextStyle(color: Colors.white),
                    ),
                  ),
                ),
              ],
            ),
            
            //Hue == Color
            SliderTheme(
              data: SliderThemeData(
                trackHeight: 13,
                thumbShape: RectangularSliderThumbShape(),
                trackShape: RanibowGradientRectSliderTrackShape(),
                thumbColor: Colors.white,
                inactiveTrackColor: Colors.black,
                activeTrackColor: Colors.white,
              ),
              child: Slider(
                value: colorController.hue.value,
                min: 0.0,
                max: 330,
                onChanged: (value) {
                  colorController.updateHue(value);
                  simController.getCurrentSelectedObject()!.filterColor = HSVColor.fromAHSV(colorController.intensity.value,
                          colorController.hue.value, colorController.saturation.value, colorController.colorValue.value)
                      .toColor();
                  simController.currentSim.refresh();
                },
              ),
            ),
            
            //Saturation
            const Text(
              "Saturation",
              style: TextStyle(
                color: Colors.white,
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
            SliderTheme(
              data: SliderThemeData(
                trackHeight: 13,
                thumbShape: RectangularSliderThumbShape(),
                trackShape: SaturationRectSliderTrackShape(colorController: colorController),
                thumbColor: Colors.white,
                inactiveTrackColor: Colors.black,
                activeTrackColor: Colors.white,
              ),
              child: Slider(
                value: colorController.saturation.value,
                min: 0.0,
                max: 1.0,
                onChanged: (value) {
                  colorController.updateSaturation(value);
                  simController.getCurrentSelectedObject()!.filterColor = HSVColor.fromAHSV(colorController.intensity.value,
                          colorController.hue.value, colorController.saturation.value, colorController.colorValue.value)
                      .toColor();
                  simController.currentSim.refresh();
                },
              ),
            ),
            
            //Color Value
            const Text(
              "Color Value",
              style: TextStyle(
                color: Colors.white,
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
            SliderTheme(
              data: SliderThemeData(
                trackHeight: 13,
                thumbShape: RectangularSliderThumbShape(),
                trackShape: ColorValueRectSliderTrackShape(colorController: colorController),
                thumbColor: Colors.white,
                inactiveTrackColor: Colors.black,
                activeTrackColor: Colors.white,
              ),
              child: Slider(
                value: colorController.colorValue.value,
                min: 0.0,
                max: 1.0,
                onChanged: (value) {
                  colorController.updateColorValue(value);
                  simController.getCurrentSelectedObject()!.filterColor = HSVColor.fromAHSV(colorController.intensity.value,
                          colorController.hue.value, colorController.saturation.value, colorController.colorValue.value)
                      .toColor();
                  simController.currentSim.refresh();
                },
              ),
            ),

            //Intensity
            const Text(
              "Intensity",
              style: TextStyle(
                color: Colors.white,
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
            SliderTheme(
              data: SliderThemeData(
                trackHeight: 13,
                thumbShape: RectangularSliderThumbShape(),
                trackShape: NoGradientRoundedRectSliderTrackShape(),
                thumbColor: Colors.white,
                inactiveTrackColor: Colors.black,
                activeTrackColor: Colors.white,
              ),
              child: Slider(
                value: colorController.intensity.value,
                min: 0.0,
                max: 1.0,
                onChanged: (value) {
                  colorController.updateIntensity(value);
                  simController.getCurrentSelectedObject()!.filterColor =
                      HSVColor.fromAHSV(colorController.intensity.value, colorController.hue.value, colorController.saturation.value, 1.0).toColor();
                  simController.currentSim.refresh();
                },
              ),
            ),
            const SizedBox(height: 20),
          ],
        ),
      );
    });
  }
}

class ColorController extends GetxController {
  var hue = 0.0.obs;
  var colorValue = 1.0.obs;
  var intensity = 1.0.obs;
  var saturation = 1.0.obs;

  void updateHue(double newHue) {
    hue.value = newHue;
  }

  void updateColorValue(double newValue) {
    colorValue.value = newValue;
  }

  void updateIntensity(double newIntensity) {
    intensity.value = newIntensity;
  }

  void updateSaturation(double newPosition) {
    saturation.value = newPosition;
  }
}
