import 'package:flutter/material.dart';

class RanibowGradientRectSliderTrackShape extends SliderTrackShape {
  final double radius;
  
  RanibowGradientRectSliderTrackShape({this.radius = 2});

  @override
  Rect getPreferredRect({
    required RenderBox parentBox,
    Offset offset = Offset.zero,
    required SliderThemeData sliderTheme,
    bool isEnabled = false,
    bool isDiscrete = false,
  }) {
    final double trackHeight = sliderTheme.trackHeight!;
    final double trackLeft = offset.dx;
    final double trackTop = offset.dy + (parentBox.size.height - trackHeight) / 2;
    final double trackWidth = parentBox.size.width;
    return Rect.fromLTWH(trackLeft, trackTop, trackWidth, trackHeight);
  }

  @override
  void paint(
    PaintingContext context,
    Offset offset, {
    required RenderBox parentBox,
    required SliderThemeData sliderTheme,
    required Animation<double> enableAnimation,
    required Offset thumbCenter,
    Offset? secondaryOffset,
    bool isDiscrete = false,
    bool isEnabled = false,
    required TextDirection textDirection,
  }) {
    if (sliderTheme.trackHeight == null) {
      return;
    }

    final List<Color> rainbowColors = [
      Colors.red,
      Colors.orange,
      Colors.yellow,
      Colors.green,
      Colors.blue,
      Colors.indigo,
      Colors.purple,
    ];

    final List<double> stops = List.generate(
      rainbowColors.length,
      (index) => index / (rainbowColors.length - 1),
    );

    final Gradient gradient = LinearGradient(
      colors: rainbowColors,
      stops: stops,
    );

    final Paint paint = Paint()..shader = gradient.createShader(Rect.fromLTWH(offset.dx, offset.dy, parentBox.size.width, sliderTheme.trackHeight!));

    final Rect trackRect = getPreferredRect(
      parentBox: parentBox,
      offset: offset,
      sliderTheme: sliderTheme,
      isEnabled: isEnabled,
      isDiscrete: isDiscrete,
    );

    final RRect roundedTrackRect = RRect.fromRectAndRadius(trackRect, Radius.circular(radius));

    context.canvas.drawRRect(roundedTrackRect, paint);
  }
}
