import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:simsushare_player/controllers/SimController.dart';

class PlayMenu extends StatelessWidget {
  PlayMenu({Key? key}) : super(key: key);

  final hideMenu = false.obs;

  static const marginSize = 10.0;

  @override
  Widget build(BuildContext context) {
    return Positioned(
      child: Obx(() => PopupMenuButton(
            itemBuilder: (context) => [
              const PopupMenuItem(
                child: Row(children: [Icon(Icons.play_arrow_outlined), SizedBox(width: marginSize), Text("Play")]),
                value: "play",
              ),
              const PopupMenuItem(
                child: Row(children: [Icon(Icons.pause_outlined), SizedBox(width: marginSize), Text("Pause")]),
                value: "pause",
              ),
              const PopupMenuItem(
                child: Row(children: [Icon(Icons.restart_alt_outlined), SizedBox(width: marginSize), Text("Restart")]),
                value: "restart",
              ),
              hideMenu.value
                  ? const PopupMenuItem(
                      child: Row(children: [Icon(Icons.menu_outlined), SizedBox(width: marginSize), Text("Show Menu")]),
                      value: "show",
                    )
                  : const PopupMenuItem(
                      child: Row(children: [Icon(Icons.menu_outlined), SizedBox(width: marginSize), Text("Hide Menu")]),
                      value: "hide",
                    ),
            ],
            child: Container(
              decoration: BoxDecoration(
                border: Border.all(color: hideMenu.value ? Colors.transparent : Colors.white),
                borderRadius: BorderRadius.circular(20),
              ),
              padding: const EdgeInsets.all(5),
              child: Icon(
                Icons.menu_outlined,
                color: hideMenu.value ? Colors.transparent : Colors.white,
                size: 30,
              ),
            ),
            onSelected: (value) {
              final _simController = Get.find<SimController>();
              switch (value) {
                case "play":
                  _simController.play();
                  break;
                case "pause":
                  _simController.pause();
                  break;
                case "restart":
                  _simController.restart();
                  break;
                case "show":
                  hideMenu.value = false;
                  break;
                case "hide":
                  hideMenu.value = true;
                  break;
                default:
                  break;
              }
            },
          )),
      right: 20,
      top: 20,
    );
  }
}
