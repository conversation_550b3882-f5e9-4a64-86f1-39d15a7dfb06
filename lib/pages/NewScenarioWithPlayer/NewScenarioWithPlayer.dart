// ignore_for_file: must_be_immutable
import 'dart:io';

import 'dart:convert';
import 'package:flame/game.dart';
import 'package:flutter/foundation.dart';
import 'package:get/get_rx/src/rx_workers/utils/debouncer.dart';
import 'package:easy_debounce/easy_debounce.dart';
import 'package:simsushare_player/components/Navigator.dart';
import 'package:simsushare_player/pages/NewScenarioWithPlayer/helpers/helper_methods.dart';
import 'package:simsushare_player/pages/NewScenarioWithPlayer/new_scenario_side_bar.dart';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:simsushare_player/components/SimPlayer.dart' if (dart.library.html) 'package:simsushare_player/components/SimPlayerWeb.dart';
import 'package:simsushare_player/controllers/SimController.dart';
import 'package:simsushare_player/models/SimObjects.dart';
import 'package:simsushare_player/pages/NewScenarioWithPlayer/new_scenario_top_bar.dart';
import 'package:simsushare_player/utils/constants.dart';

final hexRegex = RegExp("([A-Fa-f0-9]{6})");

class NewScenarioWithPlayer extends StatelessWidget {
  NewScenarioWithPlayer({Key? key}) : super(key: key);

  final game = SimPlayer(playMode: false, inEditor: true);
  List<String> spriteNames = [];
  List<SimSprite> sprites = [];

  final args = Get.arguments;

  final _pickerColor = Colors.white.obs;
  // final recorderVisible = false.obs;
  final RxDouble _blur = RxDouble(0);
  final RxDouble _scale = RxDouble(1);
  final RxDouble _widthScale = RxDouble(1);
  final RxDouble _heightScale = RxDouble(1);
  final RxDouble _opacity = RxDouble(1);
  final RxDouble _rotation = RxDouble(0);
  final RxDouble _fadeInWhen = RxDouble(0);
  final RxDouble _fadeInDuration = RxDouble(0);
  final RxDouble _fadeOutWhen = RxDouble(0);
  final RxDouble _fadeOutDuration = RxDouble(0);
  final RxDouble _speed = RxDouble(1);

  final FocusNode _textFocusNode = FocusNode();
  final filterState = "".obs;

  _initialize(BuildContext context) async {
    if (!kIsWeb && (Platform.isAndroid || Platform.isIOS)) /* (breakpoint == Breakpoints.small) */ {
      SystemChrome.setPreferredOrientations([DeviceOrientation.landscapeLeft]);
      SystemChrome.setEnabledSystemUIMode(SystemUiMode.manual, overlays: []);
    }

    final manifest = await DefaultAssetBundle.of(context).loadString("AssetManifest.json");
    final jsonManifest = jsonDecode(manifest) as Map<String, dynamic>;
    final manifestSprites = jsonManifest.keys
        .where((element) => element.startsWith("assets/sprites/") && !element.startsWith("assets/sprites/thumbs/") && element.endsWith(".png"));
    spriteNames.addAll(
      manifestSprites.map((e) {
        // print(e);
        return e.substring(15, e.indexOf("-frames"));
      }),
    );
    if (Get.parameters["saveOnStart"] == "true") {
      saveSim(showNotification: false);
    }
  }

  _listenOnSignal() {
    final _simController = Get.find<SimController>();
    final deb = Debouncer(delay: const Duration(milliseconds: 500));
    saveFn() {
      final t = DateTime.now();
      final clone = _simController.currentSim.value!.copy(clone: true);
      saveSim(showNotification: false, simulation: clone);
      print("Autosaved called $t");
    }

    _simController.signalStream.stream.listen((signal) {
      if (signal.runtimeType == String && signal == "focusOnText") {
        _textFocusNode.requestFocus();
        return;
      }
      if (signal.runtimeType == String && signal == "autosave" && _simController.autosave.value && !_simController.currentSim.value!.inPlayMode) {
        // deb.call(saveFn);
        EasyDebounce.debounce("auto-save", const Duration(milliseconds: 700), () {
          saveFn();
        });
        return;
      }
    });

    _simController.backgroundNotifier.addListener(() {
      _simController.currentSim.refresh();
    });
  }

  _listenOnSelection() {
    final _simController = Get.find<SimController>();
    _simController.selectedSimObjectIndex.stream.listen((index) {
      if (index != -1 && _simController.selectedType.value != null && _simController.selectedType.value != SimObjectType.mask) {
        final obj = _simController.getCurrentSelectedObject()!;
        _blur.value = obj.blur;
        _scale.value = obj.scale;
        _widthScale.value = obj.widthScale;
        _heightScale.value = obj.heightScale;
        _opacity.value = obj.opacity;
        _pickerColor.value = obj.filterColor;
        _rotation.value = obj.rotation;
        _fadeInDuration.value = obj.fadeInDuration;
        _fadeInWhen.value = obj.fadeInWhen;
        _fadeOutWhen.value = obj.fadeOutWhen;
        _fadeOutDuration.value = obj.fadeOutDuration;
        _speed.value = obj is SimSprite ? obj.speed : 1;
      }
    });
  }

  /* _listenOnChange() {
    final _simController = Get.find<SimController>();
    _simController.currentSim.listen((sim) {
      if (sim != null && _simController.autosave.value) {
        saveSim(showNotification: false);
      }
    });
  } */

  @override
  Widget build(BuildContext context) {
    final SimController _simController = Get.find();
    _initialize(context);
    _listenOnSignal();
    _listenOnSelection();
    // _listenOnChange();
    return Scaffold(
      body: Column(
        children: [
          ///Top bar
          NewScenarioTopbar(
            simController: _simController,
            // recorderVisible: recorderVisible,
          ),
          Expanded(
            child: Row(
              children: [
                ///Side bar
                NewScenarioSidebar(
                  simController: _simController,
                  pickerColor: _pickerColor,
                  textFocusNode: _textFocusNode,
                  /* blur: _blur,
                  scale: _scale,
                  opacity: _opacity,
                  widthScale: _widthScale,
                  heightScale: _heightScale,
                  recorderVisible: recorderVisible,
                  fadeInDuration: _fadeInDuration,
                  fadeInWhen: _fadeInWhen,
                  fadeOutDuration: _fadeOutDuration,
                  fadeOutWhen: _fadeOutWhen,
                  rotation: _rotation,
                  speed: _speed, */
                ),

                ///Simulation
                Expanded(
                  child: Obx(() {
                    if (_simController.currentSim.value == null) {
                      return const SizedBox();
                    }
                    return Container(
                      height: double.infinity,
                      // width: double.infinity,
                      alignment: Alignment.center,
                      decoration: BoxDecoration(
                        // border: Border.all(color: Colors.cyan, width: 2),
                        color: _simController.currentSim.value!.locations[_simController.currentLocation.value].color.isNotEmpty
                            ? Color(int.parse(_simController.currentSim.value!.locations[_simController.currentLocation.value].color, radix: 16))
                            : Colors.black,
                      ),
                      child: Builder(builder: (stackContext) {
                        print("WidthX: $editorWidth");
                        print("Locations: ${_simController.currentSim.value!.locations}");
                        return ClipRRect(
                          child: Stack(
                            children: [
                              //RawKeyboardListener is used to handle keyboard shortcuts
                              KeyboardListener(
                                focusNode: FocusNode(),
                                autofocus: true,
                                child: Stack(
                                  children: [
                                    // Flame Game Widget
                                    Container(
                                      /* decoration: BoxDecoration(
                                          border: Border.all(color: Colors.cyan, width: 2),
                                        ), */
                                      alignment: Alignment.center,
                                      // width: 990,
                                      // height: 660,
                                      width: editorWidth.toDouble() - 0.5, // The -0.5 is to prevent an unnecessaary overflow
                                      height: editorHeight.toDouble(),
                                      child: GameWidget(game: game),
                                    ),
                                    //Simulation navigator button - hide mode, when tapped, it opens the Navigator pad
                                    const HideNavigatorButton(),
                                  ],
                                ),
                                onKeyEvent: (event) {
                                  if (HardwareKeyboard.instance.isControlPressed || HardwareKeyboard.instance.isMetaPressed) {
                                    if (RawKeyboard.instance.keysPressed.contains(LogicalKeyboardKey.keyC)) {
                                      if (_simController.selectedSimObjectIndex.value != -1 && _simController.selectedType.value != null) {
                                        copyToClipboard();
                                      }
                                    }
                                    if (RawKeyboard.instance.keysPressed.contains(LogicalKeyboardKey.keyS)) {
                                      saveSim();
                                    }
                                  }
                                  if (HardwareKeyboard.instance.isLogicalKeyPressed(LogicalKeyboardKey.backspace) ||
                                      HardwareKeyboard.instance.isLogicalKeyPressed(LogicalKeyboardKey.delete)) {
                                    if (_simController.selectedSimObjectIndex.value != -1 && _simController.selectedType.value != null) {
                                      deleteSelectedObject();
                                    }
                                  }
                                  // NOTE: all keyboard controlled moves have the following caveat:
                                  // if mask is being moved, it is moved in "PIXELS" (which will be changed)
                                  // if object is being moved, it is moved in "PERCENTAGE" of screen size
                                  if (HardwareKeyboard.instance.isLogicalKeyPressed(LogicalKeyboardKey.arrowUp)) {
                                    if (_simController.selectedSimObjectIndex.value != -1 && _simController.selectedType.value != null) {
                                      if (_simController.selectedType.value == SimObjectType.mask) {
                                        _simController.getCurrentSelectedMask().coordinates.forEach((element) {
                                          element.y -= 1;
                                        });
                                      } else {
                                        if (_simController.selectedType.value == SimObjectType.image) {
                                          _simController.getCurrentSelectedObject()!.y -= 1;
                                        } else {
                                          _simController.getCurrentSelectedObject()!.y -= 0.01;
                                        }
                                      }
                                      _simController.currentSim.refresh();
                                      return;
                                    }
                                    final nav = _simController.currentSim.value!.navigations.firstWhereOrNull((element) =>
                                        element.from == _simController.currentSim.value!.locations[_simController.currentLocation.value].id &&
                                        element.direction == "UP");
                                    if (nav != null) {
                                      _simController.currentLocation.value =
                                          _simController.currentSim.value!.locations.indexWhere((element) => element.id == nav.to);
                                      return;
                                    }
                                  }
                                  if (HardwareKeyboard.instance.isLogicalKeyPressed(LogicalKeyboardKey.arrowDown)) {
                                    if (_simController.selectedSimObjectIndex.value != -1 && _simController.selectedType.value != null) {
                                      if (_simController.selectedType.value == SimObjectType.mask) {
                                        _simController.getCurrentSelectedMask().coordinates.forEach((element) {
                                          element.y += 1;
                                        });
                                      } else {
                                        if (_simController.selectedType.value == SimObjectType.image) {
                                          _simController.getCurrentSelectedObject()!.y += 1;
                                        } else {
                                          _simController.getCurrentSelectedObject()!.y += 0.01;
                                        }
                                      }
                                      _simController.currentSim.refresh();
                                      return;
                                    }
                                    final nav = _simController.currentSim.value!.navigations.firstWhereOrNull((element) =>
                                        element.from == _simController.currentSim.value!.locations[_simController.currentLocation.value].id &&
                                        element.direction == "DOWN");
                                    if (nav != null) {
                                      _simController.currentLocation.value =
                                          _simController.currentSim.value!.locations.indexWhere((element) => element.id == nav.to);
                                      return;
                                    }
                                  }
                                  if (HardwareKeyboard.instance.isLogicalKeyPressed(LogicalKeyboardKey.arrowLeft)) {
                                    if (_simController.selectedSimObjectIndex.value != -1 && _simController.selectedType.value != null) {
                                      if (_simController.selectedType.value == SimObjectType.mask) {
                                        _simController.getCurrentSelectedMask().coordinates.forEach((element) {
                                          element.x -= 1;
                                        });
                                      } else {
                                        if (_simController.selectedType.value == SimObjectType.image) {
                                          _simController.getCurrentSelectedObject()!.x -= 1;
                                        } else {
                                          _simController.getCurrentSelectedObject()!.x -= 0.01;
                                        }
                                      }
                                      _simController.currentSim.refresh();
                                      return;
                                    }
                                  }
                                  if (HardwareKeyboard.instance.isLogicalKeyPressed(LogicalKeyboardKey.arrowRight)) {
                                    if (_simController.selectedSimObjectIndex.value != -1 && _simController.selectedType.value != null) {
                                      print("Pressing right on ${_simController.getCurrentSelectedObject()}");
                                      if (_simController.selectedType.value == SimObjectType.mask) {
                                        _simController.getCurrentSelectedMask().coordinates.forEach((element) {
                                          element.x += 1;
                                        });
                                      } else {
                                        if (_simController.selectedType.value == SimObjectType.image) {
                                          _simController.getCurrentSelectedObject()!.x += 1;
                                        } else {
                                          _simController.getCurrentSelectedObject()!.x += 0.01;
                                        }
                                      }
                                      _simController.currentSim.refresh();
                                      return;
                                    }
                                  }
                                },
                              ),
                              //Simulation navigator pad
                              Obx(() {
                                return Positioned(
                                  bottom: 20 + (_simController.currentSim.value!.navClusterY ?? 0),
                                  left: 20 + (_simController.currentSim.value!.navClusterX ?? 0),
                                  height: context.isMobileScreen ? 160 : Get.height * 0.2,
                                  width: context.isMobileScreen ? 160 : Get.height * 0.2,
                                  child: Draggable(
                                    feedback: Container(
                                      width: context.isMobileScreen ? 160 : Get.height * 0.2,
                                      height: context.isMobileScreen ? 160 : Get.height * 0.2,
                                      decoration: BoxDecoration(
                                        color: const Color(0XFF131517).withAlpha(127),
                                        borderRadius: BorderRadius.circular(4),
                                      ),
                                    ),
                                    onDragUpdate: (details) {
                                      print("Drag update: ${details.delta}");
                                      _simController.currentSim.value!.navClusterX =
                                          (_simController.currentSim.value!.navClusterX ?? 0) + (details.delta.dx * 1);
                                      _simController.currentSim.value!.navClusterY =
                                          (_simController.currentSim.value!.navClusterY ?? 0) + (details.delta.dy * -1);
                                    },
                                    onDragCompleted: () {
                                      _simController.currentSim.refresh();
                                      print("Drag completed");
                                    },
                                    onDragEnd: (details) {
                                      print("Drag end: ${details.offset}");
                                    },
                                    onDraggableCanceled: (vel, offset) {
                                      print("Drag Cancelled: $offset");
                                      _simController.currentSim.refresh();
                                    },
                                    child: SimNavigator(
                                      locId: _simController.currentSim.value!.locations[_simController.currentLocation.value].id,
                                      editorMode: true,
                                    ),
                                  ),
                                );
                              }),
                            ],
                          ),
                        );
                      }),
                    );
                  }),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
