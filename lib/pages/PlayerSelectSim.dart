import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:simsushare_player/components/MainPageContainer.dart';
import 'package:simsushare_player/controllers/SimController.dart';
import 'package:simsushare_player/models/Simulation.dart';
import 'package:simsushare_player/pages/Preview.dart';

class PlayerSelectSim extends StatelessWidget {
  PlayerSelectSim({
    Key? key,
  }) : super(key: key);

  RxList<Scenario> sims = RxList<Scenario>();

  _initialize() async {
    sims.addAll([Scenario(name: "Test", locations: []), Scenario(name: "Simsushare onboarding", locations: [])]);
  }

  @override
  Widget build(BuildContext context) {
    _initialize();
    SimController _simController = Get.find();
    return MainPageContainer(
      title: "Select SIM to play",
      child: Obx(
        () => ListView(
          children: sims
              .map(
                (sim) => ListTile(
                  title: Text(sim.name),
                  onTap: () {
                    _simController.currentSim.value = sim;
                    Get.to(() => Preview(), arguments: Map<String, String>.from({"inEditor": "false"}));
                  },
                  shape: const Border(bottom: BorderSide(color: Colors.grey, width: 1)),
                ),
              )
              .toList(),
        ),
      ),
    );
  }
}
