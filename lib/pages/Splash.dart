import 'dart:ui' as UI;

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
// import 'package:shared_preferences/shared_preferences.dart';
import 'package:simsushare_player/utils/constants.dart';

class Splash extends StatelessWidget {
  /* const  */ Splash({Key? key}) : super(key: key);

  Rx<UI.Image?> theImage = Rxn<UI.Image>();

  RxDouble offset = RxDouble(0);

  DateTime _time = DateTime.now();

  bool _updateOffset(double val) {
    // print("Updating offset");
    final deltaTime = DateTime.now().difference(_time);
    _time = DateTime.now();
    // print("Delta Time: ${deltaTime.inMilliseconds}");
    if (offset.value + val >= 420) return true;
    offset.value += val;
    return false;
  }

  _initialize() async {
    // (await SharedPreferences.getInstance()).remove("favorite-sims");
    final img = await decodeImageFromList((await rootBundle.load("assets/images/logo_ctc.png")).buffer.asUint8List());
    theImage.value = img;
    /* await Future.doWhile(() async {
      await Future.delayed(const Duration(milliseconds: 15));
      return !_updateOffset(10);
    }); */
    // .buffer.asUint8List();
    return Future.delayed(const Duration(milliseconds: 500), () async {
      Get.offAndToNamed("/home");
    });
  }

  @override
  Widget build(BuildContext context) {
    _initialize();
    return Scaffold(
      body: Container(
        color: lightBackgrounds,
        alignment: Alignment.center,
        child: Obx(
          () => theImage.value == null
              ? const SizedBox(
                  width: 400,
                  height: 200,
                )
              : SizedBox(
                  width: 400,
                  height: 100,
                  // decoration: BoxDecoration(border: Border.all(width: 3, color: Colors.black)),
                  child: Image.asset("assets/images/logo_ctc.png"),
                  /* child: CustomPaint(
                    size: const UI.Size(200, 94),
                    painter: LogoPainter(
                      img: theImage.value!,
                      left: offset.value,
                      top: 0,
                      width: 400,
                      height: 94,
                    ),
                    willChange: true,
                    // size: UI.Size(400, 50),
                  ), */
                ),
        ),
      ),
    );
  }
}

class LogoPainter extends CustomPainter {
  final UI.Image img;
  final double left, top, width, height;

  LogoPainter({required this.img, required this.left, required this.top, required this.width, required this.height});

  final DateTime _time = DateTime.now();

  @override
  void paint(Canvas canvas, Size size) {
    // canvas.clipRect(Rect.fromLTWH(left, top, width, height));
    // print("${img.width} x ${img.height}");
    // Rect.fromLTWH(0, 0, img.width.toDouble(), img.height.toDouble()),
    canvas.drawAtlas(
      img,
      [
        UI.RSTransform.fromComponents(rotation: 0, scale: 1, anchorX: 0, anchorY: 0, translateX: 0, translateY: 0),
      ],
      [
        Rect.fromLTWH(left, top, width, height),
      ],
      null,
      UI.BlendMode.src,
      null,
      Paint(),
    );
  }

  @override
  bool shouldRepaint(LogoPainter oldDelegate) => true;

  @override
  bool shouldRebuildSemantics(LogoPainter oldDelegate) => false;
}
