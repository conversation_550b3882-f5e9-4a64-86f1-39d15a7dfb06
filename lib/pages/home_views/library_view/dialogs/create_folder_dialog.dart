// ignore_for_file: must_be_immutable

import 'dart:io';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:path_provider/path_provider.dart';
import 'package:simsushare_player/components/ContentDialog.dart';
import 'package:simsushare_player/components/MainButtons.dart';
import 'package:simsushare_player/utils/constants.dart';

class CreateFolderDialog extends StatelessWidget {
  CreateFolderDialog({Key? key}) : super(key: key);

  var folderName = "".obs;

  @override
  Widget build(BuildContext context) {
    return ContentDialog(
      width: 550,
      height: 375,
      title: "New Folder",
      content: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          const SizedBox(height: 24),
          const Text("Enter folder name", style: TextStyle(color: Colors.white)),
          const SizedBox(height: 12),
          SizedBox(
            width: 470,
            child: TextField(
              onChanged: ((value) {
                folderName.value = value;
              }),
              style: const TextStyle(color: Colors.white),
              decoration: const InputDecoration(
                labelText: "New Folder",
                labelStyle: TextStyle(color: whiteBordersColor),
              ),
            ),
          ),
        ],
      ),
      actions: [
        OrangeButton(
          label: "Create",
          onPressed: () async {
            final dir = await getApplicationDocumentsDirectory();
            final exists = await Directory(dir.path + "/library/$folderName").exists();
            if (exists) {
              print("Directory already exists");
              return;
            }
            final newDir = await Directory(dir.path + "/library/$folderName").create(recursive: true);
            print("Created dir: ${newDir.path}");
            Get.back(result: folderName, canPop: false);
          },
        ),
      ],
    );
  }
}
