import 'dart:io';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:path/path.dart' as path;
import 'package:simsushare_player/components/ContentDialog.dart';
import 'package:simsushare_player/components/MainButtons.dart';

class EditLibraryFileDialog extends StatelessWidget {
  final String filePath;
  final Function()? onUpdate;

  EditLibraryFileDialog({
    Key? key,
    required this.filePath,
    this.onUpdate,
  }) : super(key: key);

  final fileName = "".obs;
  final textFieldController = TextEditingController();

  @override
  Widget build(BuildContext context) {
    fileName.value = path.basenameWithoutExtension(filePath);
    return ContentDialog(
        title: "Edit",
        width: 550,
        height: 370,
        content: Column(
          children: [
            Obx(
              () => TextField(
                style: const TextStyle(color: Colors.white),
                controller: textFieldController..text = fileName.value,
                decoration: const InputDecoration(
                  labelText: "File Name",
                  labelStyle: TextStyle(color: Colors.white),
                ),
                onChanged: ((value) {
                  fileName.value = value;
                }),
              ),
            )
          ],
        ),
        actions: [
          TransparentButton(
            onPressed: () {
              Get.back();
            },
            label: "Cancel",
          ),
          OrangeButton(
            onPressed: () async {
              await File(filePath).rename(filePath.replaceFirst(path.basename(filePath), fileName.value + path.extension(filePath)));
              if (onUpdate != null) {
                onUpdate!();
              }
              Get.back();
            },
            label: "Save",
          )
        ]);
  }
}
