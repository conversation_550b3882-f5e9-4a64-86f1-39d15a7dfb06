// ignore_for_file: must_be_immutable

import 'dart:io';

import 'package:collection/collection.dart';
import 'package:file_picker/file_picker.dart' show PlatformFile;
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:path/path.dart' as path;
import 'package:image/image.dart' as img;
import 'package:path_provider/path_provider.dart';

import 'package:simsushare_player/components/ContentDialog.dart';
import 'package:simsushare_player/components/MainButtons.dart';
import 'package:simsushare_player/pages/home_views/library_view/dialogs/create_folder_dialog.dart';
import 'package:simsushare_player/pages/home_views/library_view/dialogs/edit_library_file_dialog.dart';
import 'package:simsushare_player/utils/constants.dart';
import 'package:simsushare_player/utils/picker.dart';

class LibraryView extends StatelessWidget {
  LibraryView({Key? key}) : super(key: key);

  final _libraryController = Get.put(LibraryController());

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Container(
            padding: isMobileScreen
                ? const EdgeInsets.all(12)
                : const EdgeInsets.symmetric(
                    vertical: 27,
                    horizontal: 44,
                  ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  "Library/Cache",
                  style: defaultTextStyle.copyWith(fontSize: 24),
                ),
                //"New Folder" button
                // if (isMobileScreen)
                // Container(
                //   margin: const EdgeInsets.symmetric(vertical: 5),
                //   decoration: BoxDecoration(
                //     color: brick,
                //     borderRadius: BorderRadius.circular(50),
                //   ),
                //   child: IconButton(
                //     onPressed: () async {
                //       final result = await showDialog(context: context, builder: ((context) => CreateFolderDialog()));
                //       if (result == null) return;
                //       final dir = await getApplicationDocumentsDirectory();
                //       controller.libraryFolders.add(
                //         LibraryFolder(
                //           directory: Directory(dir.path + "/library/" + result.value).obs,
                //           files: <FileSystemEntity>[].obs,
                //           isExpanded: false.obs,
                //         ),
                //       );
                //     },
                //     icon: const Icon(
                //       Icons.add,
                //       color: Colors.white,
                //     ),
                //   ),
                // )
                if (!isMobileScreen)
                  OrangeButton(
                    label: "New Folder",
                    icon: Icons.add_circle_outline,
                    onPressed: () async {
                      final result = await showDialog(context: context, builder: ((context) => CreateFolderDialog()));
                      if (result == null) return;
                      final dir = await getApplicationDocumentsDirectory();
                      _libraryController.libraryFolders.add(
                        LibraryFolder(
                          directory: Directory(dir.path + "/library/" + result.value).obs,
                          files: <FileSystemEntity>[].obs,
                          isExpanded: false.obs,
                        ),
                      );
                    },
                  ),
              ],
            ),
          ),

          //Body
          Obx(
            () => _libraryController.libraryFolders.isEmpty
                //Library / Cache is empty
                ? const Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(Icons.photo_library, color: Colors.white, size: 33),
                        SizedBox(height: 26),
                        Text("Library / Cache is empty", style: TextStyle(color: Colors.white, fontSize: 24)),
                        SizedBox(height: 8),
                        Text(
                          "Try creating a folder and upload audio or image files",
                          style: TextStyle(color: Colors.white, fontSize: 16),
                          textAlign: TextAlign.center,
                        )
                      ],
                    ),
                  )
                //List of folders
                : Container(
                    padding: isMobileScreen
                        ? const EdgeInsets.symmetric(horizontal: 12, vertical: 12)
                        : const EdgeInsets.symmetric(horizontal: 44, vertical: 44),
                    child: SingleChildScrollView(
                      child: Column(
                        children: _libraryController.libraryFolders
                            .mapIndexed(
                              (folderIndex, folder) => Container(
                                margin: const EdgeInsets.only(bottom: 17),
                                padding: const EdgeInsets.all(16),
                                decoration: const BoxDecoration(color: lightBackgrounds, borderRadius: BorderRadius.all(Radius.circular(6))),
                                child: Column(
                                  children: [
                                    Row(
                                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                      children: [
                                        Row(
                                          children: [
                                            const Icon(Icons.folder, color: Colors.white),
                                            const SizedBox(width: 14),
                                            //Folder name
                                            Text(path.basename(folder.directory.value.path), style: const TextStyle(color: Colors.white)),
                                          ],
                                        ),
                                        Row(
                                          children: [
                                            //Expand less/more icon
                                            Obx(
                                              () => IconButton(
                                                onPressed: () {
                                                  folder.isExpanded.value = !folder.isExpanded.value;
                                                },
                                                icon: folder.isExpanded.value
                                                    ? const Icon(Icons.expand_less, color: Colors.white)
                                                    : const Icon(Icons.expand_more, color: Colors.white),
                                              ),
                                            ),
                                            //Hamburger menu for edit and delete
                                            PopupMenuButton<int>(
                                              icon: const Icon(Icons.more_vert, color: Colors.white),
                                              onSelected: (value) {
                                                if (value == 0) {
                                                  addFilesMethod(context, folder.directory.value, folderIndex);
                                                  // Get.dialog(UploadFileDialog(directory: folder.directory.value));
                                                } else if (value == 1) {
                                                  renameFolder(folder.directory.value);
                                                } else if (value == 2) {
                                                  deleteFolder(folder.directory.value);
                                                }
                                              },
                                              itemBuilder: (context) => [
                                                const PopupMenuItem(
                                                  value: 0,
                                                  child: Row(
                                                    children: [
                                                      Icon(Icons.add),
                                                      SizedBox(width: 12),
                                                      Text("Add Files"),
                                                    ],
                                                  ),
                                                ),
                                                const PopupMenuItem(
                                                  value: 1,
                                                  child: Row(
                                                    children: [
                                                      Icon(Icons.edit),
                                                      SizedBox(width: 12),
                                                      Text("Rename"),
                                                    ],
                                                  ),
                                                ),
                                                const PopupMenuItem(
                                                  value: 2,
                                                  child: Row(
                                                    children: [
                                                      Icon(Icons.delete),
                                                      SizedBox(width: 12),
                                                      Text("Delete"),
                                                    ],
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ],
                                        ),
                                      ],
                                    ),
                                    !folder.isExpanded.value ? const Divider(color: white60) : const SizedBox(),
                                    //List of added files to that folder
                                    ...folder.files.isEmpty
                                        ? [
                                            const Text(
                                              "No files uploaded yet",
                                              style: TextStyle(color: Colors.white24, fontSize: 18),
                                            )
                                          ]
                                        : !folder.isExpanded.value
                                            ? [const SizedBox()]
                                            : folder.files.mapIndexed(
                                                (fileIndex, file) {
                                                  final ext = path.extension(file.path);
                                                  // IconData? ic;
                                                  Widget? leading;
                                                  switch (ext) {
                                                    case ".jpg":
                                                    case ".png":
                                                    case ".jpeg":
                                                    case ".gif":
                                                      leading = GestureDetector(
                                                        onTap: () {
                                                          Get.dialog(
                                                            ContentDialog(
                                                              title: path.basename(file.path),
                                                              content: Image.file(
                                                                File(file.path),
                                                                fit: BoxFit.cover,
                                                                width: Get.width * 0.7,
                                                              ),
                                                              actions: const [],
                                                            ),
                                                          );
                                                        },
                                                        child: ClipRRect(
                                                          borderRadius: BorderRadius.circular(3),
                                                          child: Image.file(
                                                            File(file.path),
                                                            fit: BoxFit.cover,
                                                            width: 50,
                                                            height: 50,
                                                          ),
                                                        ),
                                                      );
                                                      break;
                                                    case ".mp3":
                                                    case ".wav":
                                                      leading = const Icon(Icons.music_note, color: Colors.white);
                                                      break;
                                                    default:
                                                      leading = const Icon(Icons.article, color: Colors.white);
                                                  }
                                                  return Container(
                                                    padding: const EdgeInsets.symmetric(vertical: 3),
                                                    child: Row(
                                                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                                      children: [
                                                        Expanded(
                                                          child: Row(
                                                            children: [
                                                              leading,
                                                              SizedBox(width: isMobileScreen ? 11 : 17),
                                                              Expanded(
                                                                child: Text(
                                                                  path.basename(file.path),
                                                                  style: const TextStyle(color: Colors.white),
                                                                ),
                                                              ),
                                                            ],
                                                          ),
                                                        ),
                                                        Row(
                                                          children: [
                                                            IconButton(
                                                              onPressed: () async {
                                                                Get.dialog(
                                                                  EditLibraryFileDialog(
                                                                    filePath: file.path,
                                                                    onUpdate: () {
                                                                      _libraryController.libraryFolders.removeWhere((_) => true);
                                                                      folder.files.removeWhere((_) => true);
                                                                      _libraryController.getFiles();
                                                                    },
                                                                  ),
                                                                );
                                                              },
                                                              icon: const Icon(Icons.edit, color: Colors.white),
                                                            ),
                                                            IconButton(
                                                              onPressed: () async {
                                                                await file.delete();
                                                                folder.files.removeAt(fileIndex);
                                                              },
                                                              icon: const Icon(Icons.delete, color: Colors.white),
                                                            ),
                                                          ],
                                                        )
                                                      ],
                                                    ),
                                                  );
                                                },
                                              )
                                  ],
                                ),
                              ),
                            )
                            .toList(),
                      ),
                    ),
                  ),
          )
        ],
      ),
    );
  }

  renameFolder(Directory folder) async {
    final name = path.basename(folder.path).obs;
    return Get.dialog(
      ContentDialog(
        title: "Rename Folder",
        width: 550,
        height: 370,
        content: TextField(
          decoration: const InputDecoration(
            hintText: "Folder Name",
            hintStyle: TextStyle(color: Colors.white24),
            enabledBorder: UnderlineInputBorder(
              borderSide: BorderSide(color: Colors.white24),
            ),
            focusedBorder: UnderlineInputBorder(
              borderSide: BorderSide(color: Colors.white),
            ),
          ),
          style: const TextStyle(color: Colors.white),
          onChanged: (value) {
            name.value = value;
          },
          controller: TextEditingController(text: name.value),
        ),
        actions: [
          TransparentButton(
            onPressed: () {
              Get.back(result: false);
            },
            label: "No",
          ),
          OrangeButton(
            onPressed: () async {
              await folder.rename(folder.parent.path + "/" + name.value);
              _libraryController.libraryFolders.removeWhere((_) => true);
              await _libraryController.getFiles();
              Get.back(result: true);
            },
            label: "Yes",
          ),
        ],
      ),
    );
  }

  deleteFolder(Directory folder) async {
    return Get.dialog(
      ContentDialog(
        title: "Delete Folder",
        content: const Text("Are you sure you want to delete this folder?", style: TextStyle(color: Colors.white)),
        actions: [
          TransparentButton(
            onPressed: () {
              Get.back(result: false);
            },
            label: "No",
          ),
          OrangeButton(
            onPressed: () async {
              await folder.delete(recursive: true);
              _libraryController.libraryFolders.removeWhere((_) => true);
              _libraryController.getFiles();
              Get.back(result: true);
            },
            label: "Yes",
          ),
        ],
      ),
    );
  }

  addFilesMethod(BuildContext context, Directory folder, int folderIndex) async {
    // open add file to folder dialog
    final newfiles = RxList<PlatformFile>.empty(growable: true);
    final uploading = false.obs;
    await Get.dialog(
      ContentDialog(
        title: "Upload File",
        width: 550,
        height: 500,
        content: Obx(
          () => Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              TransparentButton(
                label: "Select Files to Upload",
                onPressed: () async {
                  final pickedFiles = await pickFiles(
                    allowMultiple: true,
                    allowedExtensions: ["jpg", "jpeg", "png", "gif", "mp3", "wav"],
                  );
                  newfiles.addAll(pickedFiles!.files);
                  newfiles.refresh();
                },
              ),
              Expanded(
                child: SingleChildScrollView(
                  child: Column(
                    children: [
                      ...newfiles.mapIndexed(
                        (index, file) => Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Expanded(
                              child: Text(
                                file.path == null ? "No file selected" : file.path!.split("/").last,
                                style: const TextStyle(color: Colors.white),
                              ),
                            ),
                            IconButton(
                              onPressed: () {
                                newfiles.removeAt(index);
                                newfiles.refresh();
                              },
                              icon: const Icon(Icons.delete, color: Colors.white),
                            )
                          ],
                        ),
                      )
                    ],
                  ),
                ),
              )
            ],
          ),
        ),
        actions: [
          OrangeButton(
            label: "Upload",
            disabled: uploading.value,
            onPressed: () async {
              uploading.value = true;
              if (newfiles.isEmpty) return;
              final newFiles = <File>[].obs;
              await Future.wait(newfiles.map((file) async {
                String fileName = file.name;
                String fileNameWithoutExt = path.basenameWithoutExtension(fileName);
                String ext = path.extension(fileName);
                int suffix = 1;

                while (await File('${folder.path}/$fileName').exists()) {
                  fileName = '$fileNameWithoutExt($suffix)$ext';
                  suffix++;
                }
                // final newFile = await File(file.path!).copy(folder.path + "/" + file.name);
                final newFile = await File(file.path!).copy(folder.path + "/" + fileName);
                newFiles.add(newFile);
                // return newFiles;
                return newFile;
              }));

              // final uniqueFilesList = await addFilesWithUniqueNames(folder.path, newfiles);

              // controller.libraryFolders[folderIndex].files.addAll(uniqueFilesList);
              _libraryController.libraryFolders[folderIndex].files.addAll(newFiles);
              _libraryController.libraryFolders[folderIndex].files.refresh();

              uploading.value = false;
              Get.back();
            },
          )
        ],
      ),
    );
  }
}

class LibraryFolder {
  Rx<Directory> directory;
  RxList<FileSystemEntity> files;
  RxBool isExpanded;

  LibraryFolder({
    required this.directory,
    required this.files,
    required this.isExpanded,
  });
}

class LibraryController extends GetxController {
  var libraryFolders = RxList<LibraryFolder>.empty(growable: true);

  getFiles() async {
    // NOTE: Path provider doesn't support the web
    if (kIsWeb) return;
    var dir = await getApplicationDocumentsDirectory();
    dir = await Directory(dir.path + "/library").create();
    final folders = (await dir.list().toList()).where((entry) => !entry.path.contains(".DS_Store"));
    libraryFolders.addAll(folders.map((f) => LibraryFolder(
          directory: Directory(f.path).obs,
          files: Directory(f.path).listSync().obs,
          isExpanded: false.obs,
        )));
  }

  @override
  void onInit() {
    super.onInit();
    getFiles();
  }
}
