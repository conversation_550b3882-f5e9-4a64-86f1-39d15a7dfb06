import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:simsushare_player/components/LoginDialog.dart';
import 'package:simsushare_player/controllers/UserController.dart';
import 'package:simsushare_player/pages/home_views/home_screen.dart';
import 'package:simsushare_player/pages/home_views/my_simulations_view/dialogs/manage_license_dialog.dart';
import 'package:simsushare_player/pages/home_views/my_simulations_view/dialogs/registration_dialog.dart';
import 'package:simsushare_player/pages/home_views/widgets/trailing_item.dart';
import 'package:simsushare_player/utils/constants.dart';

class TrailingList extends StatelessWidget {
  ///When screen size is large, we show title, otherwise we show icons only.
  final bool showTitle;
  final String view;

  const TrailingList({
    Key? key,
    required this.showTitle,
    required this.view,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final UserController _userController = Get.find();
    final navigationRailController = Get.find<NavigationRailController>();

    return Expanded(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.end,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // //Help & About
          // TrailingItem(
          //   title: "Help & About",
          //   icon: Icons.help,
          //   view: view,
          //   viewKey: "help",
          //   showTitle: showTitle,
          //   index: 0,
          //   onTap: () {
          //    //remove top section selection
          //    navigationRailController.changeNavRailIndex(null);
          //    //select item # in the trailing section.
          //    navigationRailController.changeTrailingRailIndex(index.value);
          //   },
          // ),

          // //Manage License
          // TrailingItem(
          //   title: "Manage License",
          //   icon: Icons.article,
          //   view: view,
          //   viewKey: "ml",
          //   showTitle: showTitle,
          //   onTap: () {
          //    //remove top section selection
          //    navigationRailController.changeNavRailIndex(null);
          //    //select item # in the trailing section.
          //    navigationRailController.changeTrailingRailIndex(index.value);
          //   },
          //   index: 1,
          // ),

          // Upload status
          Obx(() {
            if (_userController.totalUploads.value == 0) {
              return const SizedBox();
            }
            return TrailingItem(
              title: "Uploads: ${_userController.completedUploads.value}/${_userController.totalUploads.value}",
              icon: Icons.upload_rounded,
              trailing: Container(
                margin: const EdgeInsets.only(left: 10),
                child: CircularProgressIndicator(color: Colors.grey[400], strokeWidth: 2.0),
                height: 20,
                width: 20,
              ),
              view: view,
              viewKey: "",
              showTitle: showTitle,
              index: 1,
              onTap: () {},
            );
          }),

          const SizedBox(height: 15),

          Obx(() {
            return _userController.registered.value
                //Manage License
                ? TrailingItem(
                    title: "Manage License",
                    icon: Icons.article,
                    view: view,
                    viewKey: "ml",
                    showTitle: showTitle,
                    onTap: () {
                      Get.dialog(ManageLicenseDialog());
                    },
                    index: -1,
                  )
                //Registration Required
                : TrailingItem(
                    title: "Register Device",
                    icon: Icons.article,
                    view: view,
                    viewKey: "ml",
                    showTitle: showTitle,
                    index: -1,
                    onTap: () {
                      Get.dialog(RegistrationDialog());
                    },
                  );
          }),

          const SizedBox(height: 15),

          //Log In
          Obx(
            () {
              return _userController.token.value == null
                  //Login
                  ? TrailingItem(
                      title: "Log In",
                      icon: Icons.login_rounded,
                      view: view,
                      viewKey: "",
                      showTitle: showTitle,
                      index: 2,
                      onTap: () async {
                        final user = await Get.dialog(LoginDialog());
                        if (user != null) {
                          _userController.user.value = user;
                        }
                      },
                    )
                  //Sign out
                  : TrailingItem(
                      title: "Sign Out",
                      icon: Icons.logout_rounded,
                      view: view,
                      viewKey: "",
                      showTitle: showTitle,
                      index: 3,
                      special: true,
                      onTap: () async {
                        _userController.user.value = null;
                        _userController.token.value = null;
                        final prefs = await SharedPreferences.getInstance();
                        prefs.remove("token");
                      },
                    );
            },
          ),
          const SizedBox(height: 15),
          //Version
          Padding(
            padding: const EdgeInsets.only(left: 8.0, bottom: 8.0),
            child: Text(
              "Version 1.3.2 Beta-1",
              style: unselectedDrawerItemTextStyle.copyWith(fontSize: 12),
              textAlign: TextAlign.center,
            ),
          ),
        ],
      ),
    );
  }
}
