import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:simsushare_player/utils/constants.dart';

class TrailingItem extends StatelessWidget {
  final String title;
  final IconData icon;
  final Widget? trailing;
  late final RxString view;
  final String viewKey;
  final bool showTitle;
  late final RxInt index;
  final void Function() onTap;
  late final RxBool special;

  TrailingItem({
    Key? key,
    required this.title,
    required this.icon,
    this.trailing,
    required String view,
    required this.viewKey,
    required this.showTitle,
    required int index,
    required this.onTap,
    bool special = false,
  }) : super(key: key) {
    this.view = view.obs;
    this.index = index.obs;
    this.special = special.obs;
  }

  // bool hovered = false;

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      child: Center(
        child: Obx(
          () => InkWell(
            onTap: onTap,
            hoverColor: Theme.of(context).colorScheme.primary.withOpacity(0.04),
            customBorder: const StadiumBorder(),
            borderRadius: BorderRadius.circular(50),
            child: Padding(
              padding: showTitle ? const EdgeInsets.symmetric(vertical: 25, horizontal: 20) : const EdgeInsets.all(22),
              child: index.value == 3
                  ? Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // const Icon(
                        //   Icons.person,
                        //   size: 80,
                        //   color: Colors.white,
                        // ),
                        Row(
                          mainAxisAlignment: showTitle ? MainAxisAlignment.start : MainAxisAlignment.center,
                          children: [
                            Icon(
                              icon,
                              size: 25,
                              color: Colors.red,
                            ),
                            showTitle ? const SizedBox(width: 12) : Container(),
                            showTitle
                                ? Text(
                                    title,
                                    style: const TextStyle(
                                      color: Colors.red,
                                      fontSize: 18,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  )
                                : Container(),
                          ],
                        ),
                      ],
                    )
                  : Row(
                      mainAxisAlignment: showTitle ? MainAxisAlignment.start : MainAxisAlignment.center,
                      children: [
                        Icon(
                          icon,
                          color: view.value == viewKey ? yellow : white60,
                        ),
                        showTitle ? const SizedBox(width: 12) : const SizedBox(),
                        showTitle
                            ? Text(
                                title,
                                style: view.value == viewKey ? selectedDrawerItemTextStyle : unselectedDrawerItemTextStyle,
                              )
                            : const SizedBox(),
                        trailing != null ? const SizedBox(width: 12) : const SizedBox(),
                        trailing ?? const SizedBox(),
                      ],
                    ),
            ),
          ),
        ),
      ),
    );
  }
}
