import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:simsushare_player/components/ConfirmationDialog.dart';
import 'package:simsushare_player/components/ContentDialog.dart';
import 'package:simsushare_player/components/MainButtons.dart';
import 'package:simsushare_player/controllers/UserController.dart';

class ManageLicenseDialog extends StatelessWidget {
  ManageLicenseDialog({Key? key}) : super(key: key);

  final args = Get.arguments;

  @override
  Widget build(BuildContext context) {
    final userController = Get.find<UserController>();
    return ContentDialog(
        title: "Manage License",
        content: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Text("Your license does not expire, sweet!", style: TextStyle(fontSize: 24, fontWeight: FontWeight.w500)),
            SizedBox(
              width: 450,
              child: OrangeButton(
                label: "Extend Subscription",
                onPressed: () {},
              ),
            ),
            const SizedBox(height: 20),
            SizedBox(
              width: 450,
              child: <PERSON><PERSON><PERSON><PERSON>(
                label: "Deactivate on this device",
                onPressed: () {
                  Get.dialog(ConfirmationDialog(
                    message: "Are you sure you want to remove the registration token from this device?",
                    onConfirmed: () {
                      userController.deactivateDevice();
                      Get.back();
                    },
                  ));
                },
                backgroundColor: Colors.red.withAlpha(125),
              ),
            ),
          ],
        ),
        actions: const []);
  }
}
