import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:simsushare_player/components/ContentDialog.dart';
import 'package:simsushare_player/components/MainButtons.dart';
import 'package:simsushare_player/models/Simulation.dart';
import 'package:simsushare_player/utils/constants.dart';

class CloneDialog extends StatelessWidget {
  final Scenario scenario;
  final List<Scenario> allScenarios;

  CloneDialog({
    Key? key,
    required this.scenario,
    required this.allScenarios,
  }) : super(key: key);

  final name = "".obs;

  @override
  Widget build(BuildContext context) {
    return ContentDialog(
      width: Get.width * 0.6,
      height: 500,
      title: "<PERSON>lone Scenario (${scenario.name})",
      content: Column(
        children: [
          const SizedBox(height: 20),
          const Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text("Enter a new name for cloned scenario", style: TextStyle(color: white60)),
            ],
          ),
          const Sized<PERSON><PERSON>(height: 20),
          TextField(
            decoration: const InputDecoration(
              hintText: "New scenario name",
            ),
            style: textFieldTextStyle,
            onChanged: (value) {
              name.value = value;
              name.refresh();
            },
          )
        ],
        crossAxisAlignment: CrossAxisAlignment.center,
      ),
      actions: [
        Obx(
          () => OrangeButton(
            onPressed: () {
              if (name.value.isEmpty) return;
              if (allScenarios.firstWhereOrNull((element) => element.name == name.value) != null) {
                Get.snackbar("Error", "Scenario with name ${name.value} already exists", duration: const Duration(seconds: 2)).show();
                return;
              }
              final clone = Scenario(
                name: name.value,
                locations: [...scenario.locations],
                currentState: scenario.currentState,
                width: scenario.width,
                height: scenario.height,
              )..masks.addAll([...scenario.masks]);
              // return Get.back(closeOverlays: true, canPop: false, result: clone);
              return Get.back(result: clone);
            },
            label: "Clone",
            backgroundColor: name.value.isEmpty ? Colors.grey : brick,
          ),
        ),
      ],
    );
  }
}
