import 'dart:io';

import 'package:simsushare_player/models/Simulation.dart';
import 'package:xml/xml.dart' as xml;

   Future<void> updateSimDefRoot(String newDirPath, Scenario sim) async {
  final subfolder = (await Directory(newDirPath).list().toList()).where((e) => !e.path.endsWith(".DS_Store")).toList()[0];
  final newSimDefFile = File(subfolder.path + "/simdef.xml");
  final newSimDef = await newSimDefFile.readAsString();
  final newSimDefXml = xml.XmlDocument.parse(newSimDef);
  // We are using the old ID format because it will make sense to the users using the old app
  newSimDefXml.getElement("sim")!.setAttribute("id", sim.name.toUpperCase().replaceAll(RegExp(r"\s|_"), ""));
  newSimDefXml.getElement("sim")!.setAttribute("title", sim.name);
  await newSimDefFile.writeAsString(newSimDefXml.toXmlString(pretty: true, indent: "  "));
}