// ignore_for_file: must_be_immutable

import 'dart:async';
import 'dart:io';
import 'package:easy_debounce/easy_debounce.dart';
import 'package:flutter/foundation.dart';
import 'package:path/path.dart' as path;

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:collection/collection.dart';
import 'package:path_provider/path_provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:simsushare_player/components/LoginDialog.dart';
import 'package:simsushare_player/components/UploadSimDialog.dart';
import 'package:simsushare_player/components/add_map_dialog.dart';
import 'package:simsushare_player/controllers/SimController.dart';
import 'package:simsushare_player/controllers/UserController.dart';
import 'package:simsushare_player/models/CloudScenario.dart';
import 'package:simsushare_player/pages/Preview.dart';
import 'package:simsushare_player/pages/home_views/my_simulations_view/dialogs/clone_dialog.dart';
import 'package:simsushare_player/pages/home_views/my_simulations_view/my_simulations_view_top_bar.dart';
import 'package:simsushare_player/pages/home_views/my_simulations_view/dialogs/rename_dialog.dart';
import 'package:simsushare_player/pages/home_views/my_simulations_view/update_sim_def_root.dart';
import 'package:simsushare_player/pages/home_views/web_home.dart';
import 'package:simsushare_player/utils/parsers.dart';
import 'package:io/io.dart' as io;

import 'package:simsushare_player/components/ConfirmationDialog.dart';
import 'package:simsushare_player/models/Simulation.dart';
import 'package:simsushare_player/utils/constants.dart';

class MySimulationsView extends StatelessWidget {
  MySimulationsView({
    Key? key,
  }) : super(key: key);

  RxList<Scenario> scenarios = RxList.from([], growable: true);

  final favoritesOnly = false.obs;
  final favoriteSims = RxSet<String>();
  final search = "".obs;
  final isSelecting = false.obs;

  late SharedPreferences prefs;

  Future<void> _getSimulations() async {
    await Future.delayed(const Duration(seconds: 1));
    if (kIsWeb) {
      UserController _userController = Get.find();
      if (_userController.user.value == null) {
        await Future.delayed(const Duration(seconds: 1));
        final user = await Get.dialog(LoginDialog());
        if (user != null) {
          _userController.user.value = user;
        } else {
          print("User value is null in user controller");
          return;
        }
      }
      /* try { */
      // print(_userController.user.value);
      final response = await dio.get("/scenarios/" + _userController.user.value["company"]["_id"]);
      // print("${response.statusCode} ${response.data}");
      if (response.statusCode != 200) {
        return print("Failed to fetch scenarios: ${response.statusCode} ${response.data}");
      }
      final scenariosData = response.data["scenarios"];
      scenarios.addAll((scenariosData as List).map((s) {
        var cloudScenario = CloudScenario.fromJson(s);
        return Scenario(
          name: cloudScenario.title,
          locations: [],
          id: cloudScenario.id,
          directoryPath: cloudScenario.archiveURL,
        );
      }));
      return;
    }
    prefs = await SharedPreferences.getInstance();
    final favoriteSimsList = prefs.getStringList("favorite-sims") ?? [];
    for (var key in favoriteSimsList) {
      favoriteSims.add(key);
    }
    print("Favorite keys: $favoriteSims");
    final dir = Directory((await getApplicationDocumentsDirectory()).path + "/simulations");
    if (!(await dir.exists())) {
      await dir.create();
    }
    final sims = await dir.list().toList();
    final latestUpdated = sims.fold<Map<String, DateTime>>(<String, DateTime>{}, (value, sim) {
      if (sim.statSync().type != FileSystemEntityType.directory) {
        value[sim.path] = DateTime(1800);
        return value;
      }
      final firstFolder = Directory(sim.path).listSync().firstWhereOrNull((subfile) => subfile.statSync().type == FileSystemEntityType.directory);
      if (firstFolder == null) {
        value[sim.path] = DateTime(1900);
        return value;
      }
      final subfolder = Directory(firstFolder.path).listSync();
      subfolder.sort((a, b) => b.statSync().modified.compareTo(a.statSync().modified));
      value[sim.path] = subfolder.first.statSync().modified;
      return value;
    });
    // sims.sort((a, b) => b.statSync().modified.compareTo(a.statSync().modified));
    sims.sort((a, b) => latestUpdated[b.path]!.compareTo(latestUpdated[a.path]!));
    scenarios.clear();
    scenarios.addAll(
      sims.where((sim) => sim.statSync().type == FileSystemEntityType.directory /* !sim.path.contains("DS_Store") */).map(
            (s) => Scenario(name: path.basename(s.path), locations: [], directoryPath: s.path, id: path.basename(s.path)),
          ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final _simController = Get.find<SimController>();
    final _userController = Get.find<UserController>();
    print("IS MOBILE SCREEN: ${context.isMobileScreen}");
    Timer? listenerDebounce;
    _simController.signalStream.stream.listen((signal) {
      print("Listener active? ${listenerDebounce?.isActive}");
      // if (listenerDebounce?.isActive ?? false) listenerDebounce?.cancel();
      if (listenerDebounce?.isActive ?? false) return;
      listenerDebounce = Timer(const Duration(milliseconds: 200), () {
        print("On signal stream -- signal: $signal");
        if (signal.runtimeType == String && (signal == "saved" || signal == "downloaded" || signal == "get-sims")) {
          scenarios.clear();
          _getSimulations();
        }
      });
    });

    _getSimulations();
    return DefaultTabController(
      length: 2,
      child: Column(
        children: [
          MySimulationsViewTopBar(
            userController: _userController,
            getSimulations: () {
              scenarios.clear();
              _getSimulations();
            },
            selectedScenarios: _userController.selectedSims.values.toList(),
            onSearch: (searchQuery) {
              EasyDebounce.debounce("search-element", const Duration(milliseconds: 100), () {
                search.value = searchQuery;
              });
              // search.refresh();
              // final sims = [...scenarios];
              // scenarios.clear();
              // scenarios.addAll(sims);
              // scenarios.refresh();
            },
          ),
          if (!kIsWeb)
            TabBar(
              tabs: const [
                Tab(text: "All"),
                Tab(text: "Favorites"),
              ],
              onTap: (tabIndex) {
                switch (tabIndex) {
                  case 0:
                    favoritesOnly.value = false;
                    break;
                  case 1:
                    favoritesOnly.value = true;
                    break;
                  default:
                    print("Invalid tab index");
                }
                favoritesOnly.refresh();
              },
            ),
          if (kIsWeb)
            Obx(
              () => Padding(
                padding: _simController.downloadedSims.isEmpty ? EdgeInsets.zero : const EdgeInsets.symmetric(horizontal: 44, vertical: 54),
                child: Column(
                  children: [
                    for (final (i, sim) in _simController.downloadedSims.entries.indexed)
                      () {
                        final scenario = sim.value;
                        return Container(
                          key: Key(scenario.id + scenario.name + scenario.directoryPath),
                          padding: const EdgeInsets.symmetric(vertical: 22, horizontal: 16),
                          margin: const EdgeInsets.only(bottom: 17),
                          decoration: const BoxDecoration(borderRadius: BorderRadius.all(Radius.circular(6)), color: Color(0xFF1A1D21)),
                          child: Row(
                            children: [
                              //Drag handler
                              // ReorderableDragStartListener(
                              //   index: index,
                              //   child: const Icon(
                              //     Icons.drag_indicator,
                              //     color: white60,
                              //     size: 20,
                              //   ),
                              // ),
                              const SizedBox(width: 5),
                              //Scenario name
                              Expanded(child: Text(scenario.name, style: const TextStyle(color: Colors.white))),
                              const SizedBox(width: 8),
                              //Favorite icon
                              if (!kIsWeb)
                                InkWell(
                                  child: Icon(
                                    favoriteSims.contains(scenario.id) ? Icons.star_rate : Icons.star_border_outlined,
                                    color: yellow,
                                  ),
                                  onTap: () => favoriteScenarioMethod(scenario),
                                ),
                              const SizedBox(width: 8),
                              //Play button
                              InkWell(
                                child: Row(
                                  children: [
                                    const Icon(Icons.play_circle, color: Colors.white),
                                    if (!kIsWeb && !context.isMobileScreen) const SizedBox(width: 5),
                                    if (!kIsWeb && !context.isMobileScreen)
                                      const Text("Play", style: TextStyle(color: Colors.white, fontWeight: FontWeight.w400)),
                                  ],
                                ),
                                onTap: () => kIsWeb ? WebHomeController.playScenarioMethod(scenario.directoryPath) : playScenarioMethod(scenario),
                              ),
                              const SizedBox(width: 8),
                              //Edit Button
                              InkWell(
                                child: Row(
                                  children: [
                                    const Icon(Icons.edit, color: Colors.white),
                                    if (!kIsWeb && !context.isMobileScreen) const SizedBox(width: 5),
                                    if (!kIsWeb && !context.isMobileScreen)
                                      const Text("Edit", style: TextStyle(color: Colors.white, fontWeight: FontWeight.w400)),
                                  ],
                                ),
                                onTap: () => editScenarioMethod(scenario),
                              ),
                              //More PopupMenuButton [Add Map, Cole, Delete, Rename, Upload]
                              SizedBox(
                                width: 25,
                                child: PopupMenuButton(
                                    icon: const Icon(Icons.more_vert, color: Colors.white),
                                    color: sidebarDark,
                                    padding: EdgeInsets.zero,
                                    itemBuilder: (context) {
                                      final controller = Get.put(AddMapController());
                                      controller.loadLocations(scenario);
                                      return scenarioMoreActions
                                          .mapIndexed(
                                            (aIndex, action) => PopupMenuItem(
                                              child: Row(
                                                children: [
                                                  Icon(
                                                    scenarioMoreIcons[aIndex],
                                                    color: popupMenuIconColor,
                                                    size: popupMenuIconSize,
                                                  ),
                                                  const SizedBox(width: 10),
                                                  Obx(() {
                                                    final title = "".obs;
                                                    title.value = aIndex == 0 && controller.jsonFileExists.value ? "Edit Map" : action;
                                                    return Text(title.value, style: popMenuTextStyle);
                                                  }),
                                                ],
                                              ),
                                              value: action,
                                              onTap: () => moreIconTapMethod(scenario, action, context, i * -1, _userController),
                                            ),
                                          )
                                          .toList();
                                    },
                                    onSelected: (option) {}),
                              ),

                              const SizedBox(width: 7),
                              //Checkbox
                              if (!isMobileScreen)
                                SizedBox(
                                  width: 10,
                                  child: Checkbox(
                                    value: _userController.selectedSims.containsKey(scenario.id),
                                    onChanged: (value) => onChangedCheckboxValue(value, scenario),
                                    activeColor: brick,
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(4),
                                      side: const BorderSide(color: Colors.white),
                                    ),
                                    side: const BorderSide(color: Colors.white),
                                  ),
                                ),
                            ],
                          ),
                        );
                      }(),
                  ],
                ),
              ),
            ),
          Obx(
            () => Expanded(
              child: SingleChildScrollView(
                child: Container(
                  padding: context.isMobileScreen
                      ? const EdgeInsets.symmetric(vertical: 18, horizontal: 14)
                      : const EdgeInsets.symmetric(horizontal: 44, vertical: 54),
                  child: scenarios.isEmpty
                      //No simulations found
                      ? const Center(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.center,
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(Icons.view_agenda, color: Colors.white, size: 27),
                              SizedBox(height: 20),
                              Text(
                                "No Simulations Found",
                                style: TextStyle(color: Colors.white, fontSize: 24, fontWeight: FontWeight.w400),
                              ),
                              SizedBox(height: 8),
                              Text(
                                "Try creating a simulation by clicking on New Simulation button",
                                style: TextStyle(color: Colors.white, fontSize: 16, fontWeight: FontWeight.w400),
                              )
                            ],
                          ),
                        )
                      //List of simulations
                      : Column(
                          children: (favoritesOnly.value ? scenarios.where((s) => favoriteSims.contains(s.id)) : scenarios)
                              .where((scenario) => search.value.isEmpty || scenario.name.toLowerCase().contains(search.value.toLowerCase()))
                              .mapIndexed(
                                (index, scenario) => GestureDetector(
                                  key: Key(scenario.name),
                                  onTap: () {
                                    if (isSelecting.value == false) return;
                                    final v = _userController.selectedSims.containsKey(scenario.id);
                                    onChangedCheckboxValue(!v, scenario);
                                    if (_userController.selectedSims.isEmpty) {
                                      isSelecting.value = false;
                                      isSelecting.refresh();
                                    }
                                  },
                                  onLongPress: () {
                                    onChangedCheckboxValue(true, scenario);
                                    isSelecting.value = !isSelecting.value;
                                    isSelecting.refresh();
                                  },
                                  child: Container(
                                    key: Key(scenario.name),
                                    padding: EdgeInsets.symmetric(
                                      vertical: context.isMobileScreen ? 4 : 22,
                                      horizontal: context.isMobileScreen ? 10 : 16,
                                    ),
                                    margin: const EdgeInsets.only(bottom: 8),
                                    decoration: const BoxDecoration(borderRadius: BorderRadius.all(Radius.circular(6)), color: Color(0xFF1A1D21)),
                                    child: Row(
                                      children: [
                                        //Drag handler
                                        AnimatedSwitcher(
                                          duration: const Duration(milliseconds: 200),
                                          child: isSelecting.value
                                              ? Checkbox(
                                                  value: _userController.selectedSims.containsKey(scenario.id),
                                                  onChanged: (value) {
                                                    onChangedCheckboxValue(value, scenario);
                                                    if (_userController.selectedSims.isEmpty) {
                                                      isSelecting.value = false;
                                                      isSelecting.refresh();
                                                    }
                                                  },
                                                  activeColor: brick,
                                                  shape: RoundedRectangleBorder(
                                                    borderRadius: BorderRadius.circular(4),
                                                    side: const BorderSide(color: Colors.white),
                                                  ),
                                                  side: const BorderSide(color: Colors.white),
                                                )
                                              : ReorderableDragStartListener(
                                                  index: index,
                                                  child: const Icon(
                                                    Icons.drag_indicator,
                                                    color: white60,
                                                    size: 20,
                                                  ),
                                                ),
                                        ),
                                        const SizedBox(width: 8),
                                        //Scenario name
                                        Expanded(
                                          child: Text(
                                            scenario.name,
                                            style: TextStyle(
                                              color: Colors.white,
                                              fontSize: isMobileScreen ? null : 16,
                                            ),
                                          ),
                                        ),
                                        SizedBox(width: isMobileScreen ? 8 : 12),
                                        //Favorite icon
                                        if (!kIsWeb)
                                          InkWell(
                                            child: Icon(
                                              favoriteSims.contains(scenario.id) ? Icons.star_rate : Icons.star_border_outlined,
                                              color: yellow,
                                            ),
                                            onTap: () => favoriteScenarioMethod(scenario),
                                          ),
                                        SizedBox(width: isMobileScreen ? 8 : 12),
                                        //Play button
                                        InkWell(
                                          child: Row(
                                            children: [
                                              const Icon(Icons.play_circle, color: Colors.white),
                                              if (!kIsWeb && !context.isMobileScreen) const SizedBox(width: 8),
                                              if (!kIsWeb && !context.isMobileScreen)
                                                const Text("Play", style: TextStyle(color: Colors.white, fontWeight: FontWeight.w400)),
                                            ],
                                          ),
                                          onTap: () =>
                                              kIsWeb ? WebHomeController.playScenarioMethod(scenario.directoryPath) : playScenarioMethod(scenario),
                                        ),
                                        SizedBox(width: isMobileScreen ? 8 : 12),
                                        //Edit Button
                                        InkWell(
                                          child: Row(
                                            children: [
                                              const Icon(Icons.edit, color: Colors.white),
                                              if (!kIsWeb && !context.isMobileScreen) const SizedBox(width: 8),
                                              if (!kIsWeb && !context.isMobileScreen)
                                                const Text("Edit", style: TextStyle(color: Colors.white, fontWeight: FontWeight.w400)),
                                            ],
                                          ),
                                          onTap: () => editScenarioMethod(scenario),
                                        ),
                                        SizedBox(width: isMobileScreen ? null : 12),
                                        //More PopupMenuButton [Add Map, Cole, Delete, Rename, Upload]
                                        SizedBox(
                                          width: context.isMobileScreen ? 25 : 50,
                                          child: PopupMenuButton(
                                              icon: const Icon(Icons.more_vert, color: Colors.white),
                                              color: sidebarDark,
                                              padding: EdgeInsets.zero,
                                              itemBuilder: (context) {
                                                final controller = Get.put(AddMapController());
                                                controller.loadLocations(scenario);
                                                return scenarioMoreActions
                                                    .mapIndexed(
                                                      (aIndex, action) => PopupMenuItem(
                                                        child: Row(
                                                          children: [
                                                            Icon(
                                                              scenarioMoreIcons[aIndex],
                                                              color: popupMenuIconColor,
                                                              size: popupMenuIconSize,
                                                            ),
                                                            const SizedBox(width: 10),
                                                            Obx(() {
                                                              final title = "".obs;
                                                              title.value = aIndex == 0 && controller.jsonFileExists.value ? "Edit Map" : action;
                                                              return Text(title.value, style: popMenuTextStyle);
                                                            }),
                                                          ],
                                                        ),
                                                        value: action,
                                                        onTap: () => moreIconTapMethod(scenario, action, context, index, _userController),
                                                      ),
                                                    )
                                                    .toList();
                                              },
                                              onSelected: (option) {}),
                                        ),
                                        SizedBox(width: isMobileScreen ? 8 : 12),
                                        //Checkbox
                                        if (!isMobileScreen)
                                          SizedBox(
                                            width: context.isMobileScreen ? 10 : 20,
                                            child: Checkbox(
                                              value: _userController.selectedSims.containsKey(scenario.id),
                                              onChanged: (value) => onChangedCheckboxValue(value, scenario),
                                              activeColor: brick,
                                              shape: RoundedRectangleBorder(
                                                borderRadius: BorderRadius.circular(4),
                                                side: const BorderSide(color: Colors.white),
                                              ),
                                              side: const BorderSide(color: Colors.white),
                                            ),
                                          ),
                                      ],
                                    ),
                                  ),
                                ),
                              )
                              .toList(),
                        ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  favoriteScenarioMethod(Scenario scenario) async {
    final missing = !favoriteSims.contains(scenario.id);
    print("ID: ${scenario.id} and Favourit values: $favoriteSims and flip is $missing");
    if (missing) {
      favoriteSims.add(scenario.id);
    } else {
      favoriteSims.remove(scenario.id);
    }
    print("Now favorites are: ${favoriteSims.map((element) => element).toList()}");
    await prefs.setStringList(
      "favorite-sims",
      favoriteSims.mapIndexed((index, element) => favoriteSims.elementAt(index)).toList(),
    );
    favoriteSims.refresh();
  }

  playScenarioMethod(Scenario scenario) async {
    if (kIsWeb) {
      WebHomeController.playScenarioMethod(scenario.directoryPath);
      return;
    }
    final dir = Directory(
      (await Directory(scenario.directoryPath).list().toList()).where((element) => !element.path.contains("DS_Store")).toList()[0].path,
    );
    final files = await dir.list().toList();
    final simdefFile = files.firstWhere((file) => file.path.endsWith("simdef.xml"));
    final simdefXml = await File(simdefFile.path).readAsString();
    final parsedScenario = await parseSimDef(simdefXml, dir);
    SimController _simController = Get.find();
    _simController.currentSim.value = parsedScenario;
    _simController.currentSim.value!.inPlayMode = true;
    _simController.currentLocation.value =
        _simController.currentSim.value!.locations.indexWhere((loc) => loc.id == _simController.currentSim.value!.initialLocationId);
    if (_simController.currentLocation.value == -1) {
      print("Defaulting to first location");
      _simController.currentLocation.value = 0;
    }
    Future.delayed(const Duration(milliseconds: 100), () async {
      Get.to(
        () => Preview(),
        preventDuplicates: false,
        arguments: Map<String, dynamic>.from({"inEditor": "false"}),
      );
    });
  }

  editScenarioMethod(Scenario scenario) async {
    if (kIsWeb) {
      WebHomeController.editScenarioMethod(scenario.directoryPath);
      return;
    }
    final dir = Directory(
      (await Directory(scenario.directoryPath).list().toList()).where((element) => !element.path.contains("DS_Store")).toList()[0].path,
    );
    final files = await dir.list().toList();
    final simdefFile = files.firstWhere((file) => file.path.endsWith("simdef.xml"));
    final simdefXml = await File(simdefFile.path).readAsString();
    final parsedScenario = await parseSimDef(simdefXml, dir);
    SimController _simController = Get.find();
    _simController.currentLocation.value = 0;
    _simController.currentSim.value = parsedScenario;
    _simController.currentLocation.value =
        _simController.currentSim.value!.locations.indexWhere((loc) => loc.id == _simController.currentSim.value!.initialLocationId);
    if (_simController.currentLocation.value == -1) {
      print("Defaulting to first location");
      _simController.currentLocation.value = 0;
    }
    _simController.currentSim.value!.directoryPath = scenario.directoryPath;
    Future.delayed(const Duration(milliseconds: 100), () async {
      Get.toNamed("/create", arguments: Map<String, dynamic>.from({}), preventDuplicates: false);
    });
  }

  moreIconTapMethod(Scenario scenario, String action, BuildContext context, int index, UserController userController) {
    Future.delayed(const Duration(), () async {
      switch (action) {
        case 'Add Map':
          final dir = Directory(
            (await Directory(scenario.directoryPath).list().toList()).where((element) => !element.path.contains("DS_Store")).toList()[0].path,
          );

          await Get.dialog(
              AddMapDialog(
                scenario: scenario,
                directory: dir,
              ),
              barrierDismissible: false);
          return;
        case 'Clone':
          final Scenario? clone = await Get.dialog(
            CloneDialog(scenario: scenario, allScenarios: scenarios),
          );
          print("Clone: $clone");
          if (clone == null) return;
          // final originalDir = Directory(scenario.directoryPath);
          final newDirPath = (scenario.directoryPath.split("/")..removeLast()).join("/") + "/" + clone.name;
          await io.copyPath(scenario.directoryPath, newDirPath);
          await updateSimDefRoot(newDirPath, scenario);
          scenarios.removeWhere((element) => true);
          _getSimulations();
          return;
        case 'Delete':
          return showDialog(
              context: context,
              builder: (context) {
                return ConfirmationDialog(
                  message: "Are you sure you want to delete scenario ${scenario.name}",
                  onConfirmed: () async {
                    await Directory((await getApplicationDocumentsDirectory()).path + "/simulations/" + scenario.name).delete(recursive: true);
                    scenarios.removeAt(index);
                    userController.selectedSims.remove(scenario.id);
                    scenarios.refresh();
                  },
                );
              });
        case 'Rename':
          final Scenario? updated = await Get.dialog(RenameDialog(scenario: scenario));
          if (updated == null) return;
          Get.find<SimController>()
            ..downloadedSims.remove(scenario.directoryPath)
            ..downloadedSims[updated.directoryPath] = updated;

          Get.find<SimController>().downloadedSims.refresh();

          if (!kIsWeb) scenarios.removeWhere((element) => true);
          if (!kIsWeb) _getSimulations();
          return;
        case "Upload":
          final _userController = Get.find<UserController>();
          if (_userController.user.value == null) {
            final user = await Get.dialog(LoginDialog());
            if (user == null) return null;
            _userController.user.value = user;
          }
          if (index < 0) {
            Get.dialog(UploadSimDialog(
              scenarioDirectoryPath: Get.find<SimController>().downloadedSims[index * -1]!.directoryPath,
            ));
            return;
          }
          Get.dialog(UploadSimDialog(
            scenarioDirectoryPath: scenarios[index].directoryPath,
          ));
          return;
        default:
          print("Invalid action");
      }
    });
  }

  onChangedCheckboxValue(bool? value, Scenario scenario) {
    final userController = Get.find<UserController>();
    if (value!) {
      userController.selectedSims[scenario.id] = scenario;
    } else {
      userController.selectedSims.remove(scenario.id);
    }
    userController.selectedSims.refresh();
  }

  onReorderMethod(oldIndex, newIndex) {
    final movable = scenarios[oldIndex];
    if (oldIndex > newIndex) {
      scenarios.removeAt(oldIndex);
      scenarios.insert(newIndex, movable);
    } else {
      scenarios.removeAt(oldIndex);
      scenarios.insert(newIndex - 1, movable);
    }
    scenarios.refresh();
  }
}
