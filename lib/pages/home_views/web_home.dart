import 'package:archive/archive.dart';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:simsushare_player/controllers/SimController.dart';
import 'package:simsushare_player/controllers/UserController.dart';
import 'package:simsushare_player/models/CloudScenario.dart';
import 'package:simsushare_player/pages/Preview.dart';
import 'package:simsushare_player/utils/constants.dart';
import 'package:simsushare_player/utils/web_parser.dart';

class WebHomeController extends GetxController {
  final scenarios = RxList<CloudScenario>([]);

  fetchData() async {
    UserController _userController = Get.find();
    if (_userController.user.value == null) {
      print("User value is null in user controller");
      return;
    }
    /* try { */
    // print(_userController.user.value);
    final response = await dio.get("/scenarios/" + _userController.user.value["company"]["_id"]);
    // print("${response.statusCode} ${response.data}");
    if (response.statusCode != 200) {
      return print("Failed to fetch scenarios: ${response.statusCode} ${response.data}");
    }
    final scenariosData = response.data["scenarios"];
    scenarios.value = List<CloudScenario>.from(scenariosData.map((s) => CloudScenario.fromJson(s)), growable: true);
  }

  static playScenarioMethod(String scenarioUrl) async {
    SimController _simController = Get.find();
    if (kIsWeb && _simController.downloadedSims[scenarioUrl] != null) {
      _simController.currentSim.value = _simController.downloadedSims[scenarioUrl];
      _simController.currentSim.value!.inPlayMode = true;
      _simController.currentLocation.value =
          _simController.currentSim.value!.locations.indexWhere((loc) => loc.id == _simController.currentSim.value!.initialLocationId);
      if (_simController.currentLocation.value == -1) {
        print("Defaulting to first location");
        _simController.currentLocation.value = 0;
      }
      Get.to(
        () => Preview(),
        preventDuplicates: false,
        arguments: Map<String, dynamic>.from({"inEditor": "false"}),
      );
      return;
    }
    final response = await dio.get(scenarioUrl, options: Options(responseType: ResponseType.bytes));
    if (response.statusCode != 200) {
      print(String.fromCharCodes(response.data));
      debugPrint("Failed to load archive: ${response.statusCode}");
      return;
    }
    final archive = response.data as Uint8List;
    final archiveData = ZipDecoder().decodeBytes(archive);
    final parsedScenario = await parseWebSimDef(archiveData);
    _simController.currentSim.value = parsedScenario;
    _simController.currentSim.value!.inPlayMode = true;
    _simController.currentLocation.value =
        _simController.currentSim.value!.locations.indexWhere((loc) => loc.id == _simController.currentSim.value!.initialLocationId);
    if (_simController.currentLocation.value == -1) {
      print("Defaulting to first location");
      _simController.currentLocation.value = 0;
    }
    Get.to(
      () => Preview(),
      preventDuplicates: false,
      arguments: Map<String, dynamic>.from({"inEditor": "false"}),
    );
    if (kIsWeb) {
      _simController.downloadedSims[scenarioUrl] = parsedScenario;
    }
  }

  static editScenarioMethod(String scenarioUrl) async {
    SimController _simController = Get.find();
    print(scenarioUrl);
    print(_simController.downloadedSims.keys);
    if (kIsWeb && _simController.downloadedSims[scenarioUrl] != null) {
      _simController.currentLocation.value = 0;
      _simController.currentSim.value = _simController.downloadedSims[scenarioUrl];
      _simController.currentLocation.value =
          _simController.currentSim.value!.locations.indexWhere((loc) => loc.id == _simController.currentSim.value!.initialLocationId);
      if (_simController.currentLocation.value == -1) {
        print("Defaulting to first location");
        _simController.currentLocation.value = 0;
      }
      Future.delayed(const Duration(milliseconds: 100), () async {
        Get.toNamed("/create", arguments: Map<String, dynamic>.from({}), preventDuplicates: false);
      });
      return;
    }
    print("Donwoading");
    final response = await Dio().get(scenarioUrl, options: Options(responseType: ResponseType.bytes));
    if (response.statusCode != 200) {
      debugPrint("Failed to load archive: ${response.statusCode}");
      return;
    }
    print("Donwoadedd");
    final archive = response.data as Uint8List;
    final archiveData = ZipDecoder().decodeBytes(archive);
    final parsedScenario = await parseWebSimDef(archiveData);
    _simController.currentLocation.value = 0;
    _simController.currentSim.value = parsedScenario;
    _simController.currentLocation.value =
        _simController.currentSim.value!.locations.indexWhere((loc) => loc.id == _simController.currentSim.value!.initialLocationId);
    if (_simController.currentLocation.value == -1) {
      print("Defaulting to first location");
      _simController.currentLocation.value = 0;
    }
    Future.delayed(const Duration(milliseconds: 100), () async {
      Get.toNamed("/create", arguments: Map<String, dynamic>.from({}), preventDuplicates: false);
    });
    if (kIsWeb) {
      _simController.downloadedSims[scenarioUrl] = parsedScenario..directoryPath = scenarioUrl;
    }
  }

  @override
  void onInit() {
    fetchData();
    super.onInit();
  }
}
