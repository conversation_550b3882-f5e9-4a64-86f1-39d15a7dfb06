import 'dart:io';
import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:path_provider/path_provider.dart';
import 'package:simsushare_player/pages/home_views/library_view/library_view.dart';
import 'package:simsushare_player/pages/home_views/my_simulations_view/MySimulationsView.dart';
import 'package:simsushare_player/pages/home_views/widgets/trailing_list.dart';
import 'package:simsushare_player/utils/constants.dart';

import 'library_view/dialogs/create_folder_dialog.dart';
import 'my_simulations_view/dialogs/new_simulation_dialog.dart';

class HomeScreen extends StatelessWidget {
  const HomeScreen({Key? key}) : super(key: key);

  final List<NavigationDestination> destinations = const [
    NavigationDestination(
      icon: Icon(Icons.dashboard),
      label: "My Simulations",
    ),
    NavigationDestination(
      icon: Icon(Icons.photo_library),
      label: 'Library/Cache',
    ),
  ];

  _init() async {
    if (!kIsWeb) {
      if (Platform.isAndroid || Platform.isIOS) {
        SystemChrome.setPreferredOrientations([DeviceOrientation.portraitUp]);
        SystemChrome.setEnabledSystemUIMode(SystemUiMode.manual, overlays: []);
      }
    }
    // final _userController = Get.find<UserController>();
    // if (_userController.isRegistrationRequired.value && !_userController.registered.value) {
    //   Future.delayed(const Duration(milliseconds: 500), () => Get.dialog(RegistrationDialog(), barrierDismissible: false));
    // }
  }

  @override
  Widget build(BuildContext context) {
    final navigationRailController = Get.put(NavigationRailController());
    _init();
    return Scaffold(
      bottomNavigationBar: !isMobileScreen
          ? null
          : Obx(
              () => BottomNavigationBar(
                currentIndex: navigationRailController.selectedNavRailIndex.value,
                onTap: navigationRailController.changeNavRailIndex,
                items: destinations
                    .map((e) => BottomNavigationBarItem(
                          icon: e.icon,
                          label: e.label,
                        ))
                    .toList(),
              ),
            ),
      floatingActionButton: Container(
        margin: const EdgeInsets.symmetric(vertical: 5),
        decoration: BoxDecoration(
          color: brick,
          borderRadius: BorderRadius.circular(50),
        ),
        child: IconButton(
          icon: const Icon(Icons.add, color: Colors.white),
          onPressed: () async {
            if (navigationRailController.view.value == "simulations") {
              Get.dialog(NewSimulationDialog());
            } else {
              final controller = Get.put(LibraryController());

              final result = await showDialog(context: context, builder: ((context) => CreateFolderDialog()));
              if (result == null) return;
              final dir = await getApplicationDocumentsDirectory();
              controller.libraryFolders.add(
                LibraryFolder(
                  directory: Directory(dir.path + "/library/" + result.value).obs,
                  files: <FileSystemEntity>[].obs,
                  isExpanded: false.obs,
                ),
              );
            }
          },
        ),
      ),
      body: Row(
        children: [
          if (!isMobileScreen)
            Obx(
              () => SizedBox(
                height: 1000,
                width: 250,
                child: NavigationRail(
                  extended: true,
                  // padding: const EdgeInsets.all(0),
                  //Logo
                  leading: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 10),
                    child: Image.asset("assets/images/logo_ctc.png"),
                  ),
                  destinations: destinations.map((e) {
                    return NavigationRailDestination(
                      icon: e.icon,
                      label: Text(e.label),
                    );
                  }).toList(),
                  onDestinationSelected: navigationRailController.changeNavRailIndex,
                  selectedIndex: navigationRailController.selectedNavRailIndex.value,
                  trailing: TrailingList(
                    showTitle: true,
                    view: navigationRailController.view.value,
                  ),
                ),
              ),
            ),
          Expanded(
            child: SizedBox(
              height: double.infinity,
              child: Obx(() {
                switch (navigationRailController.view.value) {
                  case "simulations":
                    return MySimulationsView();
                  case "library":
                    return LibraryView();
                  default:
                    return const Center(
                      child: Text(
                        "Something went wrong",
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 25,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    );
                }
              }),
            ),
          ),
        ],
      ),
    );
  }
}

class NavigationRailController extends GetxController {
  final view = "simulations".obs;

  ///It's used to set the index of the trailing section of the nav rail
  RxInt selectedTrailingNavRailIndex = 0.obs;

  ///It's used to set the index of the top section of the nav rail. It's nullable so that we can
  ///remove top section selection when we select any item in the trailing section.
  RxInt selectedNavRailIndex = 0.obs;

  changeNavRailIndex(int? index) {
    selectedNavRailIndex.value = index ?? 0;
    view.value = index == 0 ? "simulations" : "library";
  }

  changeTrailingRailIndex(int index) {
    selectedTrailingNavRailIndex.value = index;
    index == 0 ? view.value = "help" : view.value = "ml";
  }
}
