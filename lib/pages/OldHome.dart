/* import 'dart:io';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:multi_window/multi_window.dart';
import 'package:simsushare_player/components/MainDialog.dart';
import 'package:simsushare_player/components/OptionsButton.dart';
import 'package:simsushare_player/helpText.dart';

const menuItemTextStyle = TextStyle(color: Colors.white);
final mainMenuButtonStyle = ElevatedButton.styleFrom(primary: Colors.blue.shade900);
const mainMenuButtonTextStyle = TextStyle(color: Colors.white, fontSize: 24);
final mainDialogButtonStyle =
    mainMenuButtonStyle.copyWith(padding: ElevatedButton.styleFrom(padding: const EdgeInsets.symmetric(vertical: 16)).padding);
const mainDialogTextStyle = TextStyle(fontSize: 36, fontWeight: FontWeight.bold);

class Home extends StatelessWidget {
  Home({Key? key}) : super(key: key);

  RxBool menuActive = RxBool(false);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        color: Colors.black,
        child: Column(
          children: [
            Expanded(
              child: Stack(
                children: [
                  GestureDetector(
                    onTap: () {
                      if (menuActive.isTrue) {
                        menuActive.value = false;
                      }
                    },
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 10),
                      child: Container(
                        decoration: BoxDecoration(
                          color: Colors.blue.shade900,
                          image: DecorationImage(
                            image: AssetImage("assets/images/logo_ctc.png"),
                            fit: BoxFit.contain,
                          ),
                        ),
                      ),
                    ),
                  ),
                  Obx(
                    () => menuActive.value
                        ? Positioned(
                            width: 300,
                            child: Container(
                              decoration: BoxDecoration(color: Colors.blue.shade900, border: Border.all(color: Colors.white, width: 2)),
                              child: Column(children: [
                                ListTile(
                                  title: Text(
                                    "Set Language",
                                    style: menuItemTextStyle,
                                  ),
                                  onTap: () {
                                    showDialog(context: context, builder: (context) => ChangeLanguageDialog());
                                  },
                                ),
                                Divider(color: Colors.grey),
                                ListTile(
                                  title: Text(
                                    "Help",
                                    style: menuItemTextStyle,
                                  ),
                                  onTap: () {
                                    showDialog(context: context, builder: (context) => HelpDialog());
                                  },
                                ),
                                Divider(color: Colors.grey),
                                ListTile(
                                  title: Text(
                                    "Manage License",
                                    style: menuItemTextStyle,
                                  ),
                                  onTap: () {
                                    Get.toNamed("/manage-license");
                                  },
                                ),
                                Divider(color: Colors.grey),
                                ListTile(
                                  title: Text(
                                    "Scan for new sims",
                                    style: menuItemTextStyle,
                                  ),
                                  onTap: () {},
                                ),
                                Divider(color: Colors.grey),
                                ListTile(
                                  title: Text(
                                    "Start CTC multiplayer mode",
                                    style: menuItemTextStyle,
                                  ),
                                  onTap: () {},
                                ),
                                Divider(color: Colors.grey),
                                ListTile(
                                  title: Text(
                                    "Change main sim folder",
                                    style: menuItemTextStyle,
                                  ),
                                  onTap: () {},
                                ),
                                Divider(color: Colors.grey),
                                ListTile(
                                  title: Text(
                                    "Start another window",
                                    style: menuItemTextStyle,
                                  ),
                                  onTap: () {
                                    MultiWindow.create(DateTime.now().toIso8601String());
                                  },
                                ),
                                Divider(color: Colors.grey),
                                ListTile(
                                  title: Text(
                                    "Close App",
                                    style: menuItemTextStyle,
                                  ),
                                  onTap: () {
                                    exit(0);
                                  },
                                ),
                              ]),
                            ),
                            top: 0,
                            right: 0,
                          )
                        : SizedBox(),
                  ),
                  Positioned(
                    child: OptionsButton(
                      onTap: () {
                        menuActive.value = !menuActive.value;
                      },
                    ),
                    top: 20,
                    right: 20,
                  ),
                ],
              ),
              flex: 8,
            ),
            Expanded(
              child: Padding(
                padding: const EdgeInsets.only(bottom: 10),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceAround,
                  children: [
                    Column(
                      children: [
                        Expanded(
                          child: ElevatedButton(
                            onPressed: () {
                              // Get.toNamed("/player");
                              showDialog(
                                  context: context,
                                  builder: (context) {
                                    return NewSimDialog();
                                  });
                            },
                            child: Text(
                              "NEW",
                              style: mainMenuButtonTextStyle,
                            ),
                            style: mainMenuButtonStyle,
                          ),
                        ),
                      ],
                    ),
                    Column(
                      children: [
                        Expanded(
                          child: ElevatedButton(
                            onPressed: () {},
                            child: Text(
                              "EDIT",
                              style: mainMenuButtonTextStyle,
                            ),
                            style: mainMenuButtonStyle,
                          ),
                        ),
                      ],
                    ),
                    Column(
                      children: [
                        Expanded(
                          child: ElevatedButton(
                            onPressed: () {
                              Get.toNamed("/player-select-sim");
                            },
                            child: Text(
                              "PLAY",
                              style: mainMenuButtonTextStyle,
                            ),
                            style: mainMenuButtonStyle,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            )
          ],
        ),
      ),
    );
  }
}

enum Languages { English, German, Danish, Spanish, French, Italian, Norwegian, Romanian, Sweedish }

class ChangeLanguageDialog extends StatelessWidget {
  ChangeLanguageDialog({Key? key}) : super(key: key);

  RxString language = RxString("English");

  @override
  Widget build(BuildContext context) {
    return MainDialog(
      child: Column(
        children: [
          Expanded(
            child: Text(
              "Set Language",
              style: mainDialogTextStyle,
            ),
            flex: 2,
          ),
          Expanded(
            child: ListView(
              children: Languages.values
                  .map(
                    (lang) => ListTile(
                      title: Text(lang.name),
                      leading: GestureDetector(
                        child: Radio(
                          value: lang.name,
                          groupValue: language.value,
                          onChanged: (val) {
                            language.value = val.toString();
                          },
                        ),
                      ),
                    ),
                  )
                  .toList(),
            ),
            flex: 6,
          ),
          Expanded(
            child: Row(
              children: [
                Expanded(
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 8),
                    child: ElevatedButton(
                      onPressed: () {
                        Get.back();
                      },
                      child: Text("Cancel", style: mainMenuButtonTextStyle),
                      style: mainDialogButtonStyle,
                    ),
                  ),
                  flex: 2,
                ),
                Expanded(
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 8),
                    child: ElevatedButton(
                      onPressed: () {
                        Get.back();
                      },
                      child: Text("Ok", style: mainMenuButtonTextStyle),
                      style: mainDialogButtonStyle,
                    ),
                  ),
                  flex: 2,
                ),
              ],
            ),
            flex: 2,
          ),
        ],
      ),
    );
  }
}

class HelpDialog extends StatelessWidget {
  const HelpDialog({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return MainDialog(
      child: Column(
        children: [
          Expanded(
            child: Text(
              "Help",
              style: mainDialogTextStyle,
            ),
            flex: 2,
          ),
          Expanded(
            child: SingleChildScrollView(
                child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 6),
              child: Text(helpText["en"]!),
            )),
            flex: 6,
          ),
          Expanded(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                SizedBox(
                  width: Get.width * 0.5,
                  child: ElevatedButton(
                    onPressed: () {
                      Get.back();
                    },
                    child: const Text(
                      "Ok",
                      style: mainMenuButtonTextStyle,
                    ),
                    style: mainDialogButtonStyle,
                  ),
                ),
              ],
            ),
            flex: 2,
          ),
        ],
      ),
    );
  }
}
 */