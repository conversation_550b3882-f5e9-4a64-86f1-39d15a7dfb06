import 'dart:ui' as UI;
import 'dart:math' as math;
// import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:flutter/material.dart' show Color, Colors;
import 'package:nanoid/nanoid.dart' as nanoid;

// part 'SimObjects.freezed.dart';
// part 'SimObjects.g.dart';

/* 
  Transparent color for filterColor causes a problem with color picker widget
  resetting to white unless the opacity has changed 
*/
// const defaultFilterColor = Colors.transparent;
const defaultFilterColor = Color(0x00FF0000);

class SimObject {
  String id = "";
  double x;
  double y;
  double width = 1.0;
  double height = 1.0;
  double scale = 1.0;
  double widthScale = 1.0; // also named "pinch"
  double heightScale = 1.0; // also named "flatten"
  Color filterColor = defaultFilterColor;
  double blur = 0.0;
  bool mirrorX = false;
  bool mirrorY = false;
  double rotation = 0.0; // in radians (pi * 2 = 360 degrees)
  double opacity = 1.0;
  List<String> maskIds = List<String>.empty(growable: true);
  int priority = 0;
  double fadeInWhen = 0;
  double fadeInDuration = 0;
  bool fadeOut = false;
  double fadeOutWhen = 0;
  double fadeOutDuration = 0;
  // int timingstart = 0;
  bool playing = true;
  bool movable = false;
  String trigger = "scenario";
  bool triggerOnce = false;
  bool clickToToggle = false;
  bool hideOnStart = false;
  // NOTE: in-app only
  bool hidden = false;

  SimObject({
    this.id = "",
    required this.x,
    required this.y,
    this.width = 1.0,
    this.height = 1.0,
    this.scale = 1.0,
    this.widthScale = 1.0,
    this.heightScale = 1.0,
    this.filterColor = Colors.transparent,
    this.blur = 0.0,
    this.mirrorX = false,
    this.mirrorY = false,
    this.rotation = 0.0,
    this.opacity = 1.0,
    this.priority = 0,
    this.fadeInWhen = 0,
    this.fadeInDuration = 0,
    double? pinch,
    this.fadeOut = false,
    this.fadeOutWhen = 0,
    this.fadeOutDuration = 0,
    // this.timingstart = 0,
    this.playing = true,
    this.movable = false,
    this.trigger = "scenario",
    this.triggerOnce = false,
    this.hidden = false,
    this.clickToToggle = false,
    this.hideOnStart = false,
  }) {
    if (id.isEmpty) {
      id = nanoid.nanoid(8);
    }
  }

  copy({bool clearMasks = false, bool showHidden = false}) {
    final _copy = SimObject(x: x, y: y, scale: scale);
    _copy.blur = blur;
    _copy.filterColor = filterColor;
    _copy.width = width;
    _copy.height = height;
    _copy.scale = scale;
    _copy.widthScale = widthScale;
    _copy.heightScale = heightScale;
    _copy.mirrorX = mirrorX;
    _copy.mirrorY = mirrorY;
    _copy.rotation = rotation;
    _copy.width = width;
    _copy.opacity = opacity;
    _copy.maskIds = clearMasks ? [] : maskIds.toList();
    // _copy.maskIds = [];
    _copy.priority = priority;
    _copy.fadeInWhen = fadeInWhen;
    _copy.fadeInDuration = fadeInDuration;
    _copy.fadeOut = fadeOut;
    _copy.fadeOutWhen = fadeOutWhen;
    _copy.fadeOutDuration = fadeOutDuration;
    // _copy.timingstart = timingstart;
    _copy.movable = movable;
    _copy.trigger = trigger;
    _copy.triggerOnce = triggerOnce;
    _copy.clickToToggle = clickToToggle;
    _copy.hideOnStart = hideOnStart;
    _copy.hidden = showHidden ? false : hidden;
    return _copy;
  }

  reset() {
    scale = 1.0;
    widthScale = 1.0;
    heightScale = 1.0;
    filterColor = defaultFilterColor;
    blur = 0.0;
    mirrorX = false;
    mirrorY = false;
    rotation = 0.0;
    opacity = 1.0;
    maskIds = [];
    fadeOut = false;
    fadeOutWhen = 0;
    fadeOutDuration = 0;
    // timingstart = 0;
    playing = true;
    movable = false;
    trigger = "scenario";
    clickToToggle = false;
    hideOnStart = false;
    hidden = false;
  }

  // factory SimObject.fromJson(Map<String, Object?> json) => _$SimObjectFromJson(json);

  bool equalTo(SimObject other) {
    if (maskIds.length != other.maskIds.length) {
      return false;
    }
    for (int i = 0; i < maskIds.length; i++) {
      if (maskIds[i] != other.maskIds[i]) {
        return false;
      }
    }
    if (id != other.id ||
        x != other.x ||
        y != other.y ||
        width != other.width ||
        height != other.height ||
        scale != other.scale ||
        widthScale != other.widthScale ||
        heightScale != other.heightScale ||
        filterColor != other.filterColor ||
        blur != other.blur ||
        mirrorX != other.mirrorX ||
        mirrorY != other.mirrorY ||
        rotation != other.rotation ||
        opacity != other.opacity ||
        priority != other.priority ||
        fadeInWhen != other.fadeInWhen ||
        fadeInDuration != other.fadeInDuration ||
        fadeOut != other.fadeOut ||
        fadeOutWhen != other.fadeOutWhen ||
        fadeOutDuration != other.fadeOutDuration ||
        playing != other.playing ||
        movable != other.movable ||
        trigger != other.trigger ||
        triggerOnce != other.triggerOnce ||
        clickToToggle != other.clickToToggle ||
        hideOnStart != other.hideOnStart ||
        hidden != other.hidden) {
      return false;
    }

    return true;
  }

  @override
  String toString() {
    return "SimObject: {id: $id, x: $x, y: $y, width: $width, height: $height, scale: $scale, widthScale: $widthScale, heightScale: $heightScale, filterColor: $filterColor, blur: $blur, mirrorX: $mirrorX, mirrorY: $mirrorY, rotation: $rotation, opacity: $opacity, maskIds: $maskIds, priority: $priority, fadeInWhen: $fadeInWhen, fadeInDuration: $fadeInDuration, fadeOut: $fadeOut, fadeOutWhen: $fadeOutWhen, fadeOutDuration: $fadeOutDuration, trigger: $trigger, playing: $playing, hidden: $hidden}";
  }
}

class SimSprite extends SimObject {
  UI.Image img;
  List<SpriteFrame> frames;
  String name;
  String assetName;
  Map<String, dynamic>? aseprite;
  int framerate = 20;
  double speed = 1;
  double scaleFactor = 1;

  SimSprite({
    required this.img,
    required this.frames,
    required this.assetName,
    this.name = "",
    this.aseprite,
    this.framerate = 20,
    this.speed = 1.0,
    this.scaleFactor = 1.0,
    String id = "",
    required double x,
    required double y,
    double width = 1.0,
    double height = 1.0,
    double scale = 1.0,
    double widthScale = 1.0,
    double heightScale = 1.0,
    Color filterColor = defaultFilterColor,
    double blur = 0.0,
    bool mirrorX = false,
    bool mirrorY = false,
    double rotation = 0.0,
    double opacity = 1.0,
    // Sim Object properties
    double fadeInWhen = 0.0,
    double fadeInDuration = 0.0,
    double? pinch,
    bool fadeOut = false,
    double fadeOutWhen = 0.0,
    double fadeOutDuration = 0.0,
    String trigger = "scenario",
    // int timingstart = 0,
  }) : super(
          id: id,
          x: x,
          y: y,
          scale: scale,
          widthScale: widthScale,
          heightScale: heightScale,
          filterColor: filterColor,
          blur: blur,
          mirrorX: mirrorX,
          mirrorY: mirrorY,
          rotation: rotation,
          opacity: opacity,
          fadeInWhen: fadeInWhen,
          fadeInDuration: fadeInDuration,
          pinch: pinch,
          fadeOut: fadeOut,
          fadeOutWhen: fadeOutWhen,
          fadeOutDuration: fadeOutDuration,
          trigger: trigger,
          // timingstart: timingstart,
        ) {
    // for (var f in frames) {
    //   if (f.width > width) {
    //     width = f.width;
    //   }
    // }
    // for (var f in frames) {
    //   if (f.height > height) {
    //     height = f.height;
    //   }
    // }
    if (width != 1.0) {
      this.width = width;
    }
    if (height != 1.0) {
      this.height = height;
    }
    if (name.isEmpty) {
      name = "Sprite-${nanoid.nanoid(8)}";
    }
    if (id.isEmpty) {
      id = nanoid.nanoid(8);
    }
  }

  @override
  copy({bool clearMasks = false, bool showHidden = false}) {
    final _copy = SimSprite(
      img: img,
      frames: frames,
      assetName: assetName,
      x: x,
      y: y,
      scale: scale,
      framerate: framerate,
      speed: speed,
      name: name,
      scaleFactor: scaleFactor,
    );
    _copy.blur = blur;
    _copy.filterColor = filterColor;
    _copy.width = width;
    _copy.height = height;
    _copy.scale = scale;
    _copy.widthScale = widthScale;
    _copy.heightScale = heightScale;
    _copy.mirrorX = mirrorX;
    _copy.mirrorY = mirrorY;
    _copy.rotation = rotation;
    _copy.width = width;
    _copy.opacity = opacity;
    _copy.maskIds = clearMasks ? [] : maskIds.toList();
    // _copy.maskIds = [];
    _copy.priority = priority;
    _copy.fadeInWhen = fadeInWhen;
    _copy.fadeInDuration = fadeInDuration;
    _copy.fadeOut = fadeOut;
    _copy.fadeOutWhen = fadeOutWhen;
    _copy.fadeOutDuration = fadeOutDuration;
    // _copy.timingstart = timingstart;
    _copy.movable = movable;
    _copy.trigger = trigger;
    _copy.triggerOnce = triggerOnce;
    _copy.hidden = showHidden ? false : hidden;
    _copy.clickToToggle = clickToToggle;
    _copy.hideOnStart = hideOnStart;
    _copy.assetName = assetName;
    return _copy;
  }

  @override
  bool equalTo(SimObject other) {
    if (other is! SimSprite) {
      return false;
    }
    if (super.equalTo(other) == false) {
      return false;
    }
    if (assetName != other.assetName) {
      return false;
    }
    if (frames.length != other.frames.length) {
      return false;
    }
    for (int i = 0; i < frames.length; i++) {
      if (frames[i].x != other.frames[i].x ||
          frames[i].y != other.frames[i].y ||
          frames[i].width != other.frames[i].width ||
          frames[i].height != other.frames[i].height ||
          frames[i].rotated != other.frames[i].rotated) {
        return false;
      }
    }
    if (framerate != other.framerate || speed != other.speed) {
      return false;
    }
    return true;
  }

  @override
  String toString() {
    // return "SimSprite({ id: $id, assetName: $assetName, x: $x, y: $y, scale: $scale, widthScale: $widthScale, heightScale: $heightScale, rotation: $rotation, filterColor: ${filterColor.toString()}})";
    return "SimSprite({ " + super.toString() + " assetName: $assetName, frames: $frames, framerate: $framerate, speed: $speed})";
  }
}

class SpriteFrame {
  double x;
  double y;
  double width;
  double height;
  bool rotated;

  SpriteFrame({
    required this.x,
    required this.y,
    required this.width,
    required this.height,
    required this.rotated,
  });

  bool equalTo(Object other) {
    if (other is! SpriteFrame) {
      return false;
    }
    return x == other.x && y == other.y && width == other.width && height == other.height && rotated == other.rotated;
  }

  @override
  String toString() {
    return "SpriteFrame({ x: $x, y: $y, width: $width, height: $height, rotated: $rotated})";
  }
}

class SimImage extends SimObject {
  UI.Image img;
  String path;
  String to;
  int timeout = 0;
  double clickToScale = 1.0;
  String? syncVariable;
  // bool clickToToggle = false;
  // bool hideOnStart = false;

  SimImage({
    required this.img,
    required this.path,
    // this.clickToToggle = false,
    // this.hideOnStart = false,
    this.to = "",
    this.timeout = 0,
    this.clickToScale = 1.0,
    this.syncVariable,
    String id = "",
    required double x,
    required double y,
    double width = 1.0,
    double height = 1.0,
    double scale = 1.0,
    double widthScale = 1.0,
    double heightScale = 1.0,
    Color filterColor = defaultFilterColor,
    double blur = 0.0,
    bool mirrorX = false,
    bool mirrorY = false,
    double rotation = 0.0,
    double opacity = 1.0,
  }) : super(
          id: id,
          x: x,
          y: y,
          scale: scale,
          width: width,
          height: height,
          widthScale: widthScale,
          heightScale: heightScale,
          filterColor: filterColor,
          blur: blur,
          mirrorX: mirrorX,
          mirrorY: mirrorY,
          rotation: rotation,
          opacity: opacity,
        ) {
    if (id == "") {
      id = nanoid.nanoid(8);
    }
  }

  @override
  copy({bool clearMasks = false, bool showHidden = false}) {
    final _copy = SimImage(
      id: id,
      img: img,
      path: path,
      x: x,
      y: y,
      scale: scale,
      // clickToToggle: clickToToggle,
      // hideOnStart: hideOnStart,
      to: to,
      clickToScale: clickToScale,
      syncVariable: syncVariable,
    );
    _copy.blur = blur;
    _copy.filterColor = filterColor;
    _copy.width = width;
    _copy.height = height;
    _copy.scale = scale;
    _copy.widthScale = widthScale;
    _copy.heightScale = heightScale;
    _copy.mirrorX = mirrorX;
    _copy.mirrorY = mirrorY;
    _copy.rotation = rotation;
    _copy.width = width;
    _copy.opacity = opacity;
    _copy.maskIds = clearMasks ? [] : maskIds.toList();
    // _copy.maskIds = [];
    _copy.priority = priority;
    _copy.fadeInWhen = fadeInWhen;
    _copy.fadeInDuration = fadeInDuration;
    _copy.fadeOut = fadeOut;
    _copy.fadeOutWhen = fadeOutWhen;
    _copy.fadeOutDuration = fadeOutDuration;
    // _copy.timingstart = timingstart;
    _copy.movable = movable;
    _copy.trigger = trigger;
    _copy.triggerOnce = triggerOnce;
    _copy.hidden = showHidden ? false : hidden;
    _copy.clickToToggle = clickToToggle;
    _copy.hideOnStart = hideOnStart;
    return _copy;
  }

  @override
  bool equalTo(SimObject other) {
    if (other is! SimImage) {
      return false;
    }
    if (super.equalTo(other) == false) {
      return false;
    }
    if (path != other.path ||
        to != other.to ||
        timeout != other.timeout ||
        clickToScale != other.clickToScale ||
        syncVariable != other.syncVariable) {
      return false;
    }
    return true;
  }

  @override
  String toString() {
    return "{${super.toString()}, path: $path, to: $to, timeout: $timeout, clickToScale: $clickToScale, syncVariable: $syncVariable}";
  }
}

class SimText extends SimObject {
  String text;
  late Color backgroundColor;

  SimText({
    this.text = "Enter text here",
    this.backgroundColor = Colors.transparent,
    String id = "",
    required double x,
    required double y,
    double width = 1.0,
    double height = 1.0,
    double scale = 1.0,
    double widthScale = 1.0,
    double heightScale = 1.0,
    Color filterColor = Colors.white,
    double blur = 0.0,
    bool mirrorX = false,
    bool mirrorY = false,
    double rotation = 0.0,
    double opacity = 1.0,
    // Sim Object properties
    double fadeInWhen = 0.0,
    double fadeInDuration = 0.0,
    double? pinch,
    bool fadeOut = false,
    double fadeOutWhen = 0.0,
    double fadeOutDuration = 0.0,
    // int timingstart = 0,
  }) : super(
          id: id,
          x: x,
          y: y,
          scale: scale,
          widthScale: widthScale,
          heightScale: heightScale,
          filterColor: filterColor,
          blur: blur,
          mirrorX: mirrorX,
          mirrorY: mirrorY,
          rotation: rotation,
          opacity: opacity,
          fadeInWhen: fadeInWhen,
          fadeInDuration: fadeInDuration,
          pinch: pinch,
          fadeOut: fadeOut,
          fadeOutWhen: fadeOutWhen,
          fadeOutDuration: fadeOutDuration,
          // timingstart: timingstart,
        );

  @override
  copy({bool clearMasks = false, bool showHidden = false}) {
    final _copy = SimText(text: text, x: x, y: y, scale: scale);
    _copy.blur = blur;
    _copy.filterColor = filterColor;
    _copy.width = width;
    _copy.height = height;
    _copy.scale = scale;
    _copy.widthScale = widthScale;
    _copy.heightScale = heightScale;
    _copy.mirrorX = mirrorX;
    _copy.mirrorY = mirrorY;
    _copy.rotation = rotation;
    _copy.width = width;
    _copy.opacity = opacity;
    _copy.maskIds = clearMasks ? [] : maskIds.toList();
    // _copy.maskIds = [];
    _copy.priority = priority;
    _copy.fadeInWhen = fadeInWhen;
    _copy.fadeInDuration = fadeInDuration;
    _copy.fadeOut = fadeOut;
    _copy.fadeOutWhen = fadeOutWhen;
    _copy.fadeOutDuration = fadeOutDuration;
    // _copy.timingstart = timingstart;
    _copy.movable = movable;
    _copy.trigger = trigger;
    _copy.triggerOnce = triggerOnce;
    _copy.hidden = showHidden ? false : hidden;
    _copy.clickToToggle = clickToToggle;
    _copy.hideOnStart = hideOnStart;
    return _copy;
  }

  @override
  reset() {
    super.reset();
    filterColor = Colors.white;
  }

  @override
  bool equalTo(SimObject other) {
    if (other is! SimText) {
      return false;
    }
    if (super.equalTo(other) == false) {
      return false;
    }
    if (text != other.text || backgroundColor != other.backgroundColor) {
      return false;
    }
    return true;
  }

  @override
  String toString() {
    // return "SimText({ id: $id, text: $text, x: $x, y: $y, scale: $scale, widthScale: $widthScale, heightScale: $heightScale, rotation: $rotation, filterColor: ${filterColor.toString()}})";
    return "SimText({ " + super.toString() + " text: $text, backgroundColor: $backgroundColor })";
  }
}

class SimShape extends SimObject {
  String shape;

  SimShape({
    required this.shape,
    String id = "",
    required double x,
    required double y,
    double width = 1.0,
    double height = 1.0,
    double scale = 1.0,
    double widthScale = 1.0,
    double heightScale = 1.0,
    Color filterColor = Colors.white,
    double blur = 0.0,
    bool mirrorX = false,
    bool mirrorY = false,
    double rotation = 0.0,
    double opacity = 1.0,
    // Sim Object properties
    double fadeInWhen = 0.0,
    double fadeInDuration = 0.0,
    double? pinch,
    bool fadeOut = false,
    double fadeOutWhen = 0.0,
    double fadeOutDuration = 0.0,
    // int timingstart = 0,
  }) : super(
          id: id,
          x: x,
          y: y,
          scale: scale,
          widthScale: widthScale,
          heightScale: heightScale,
          filterColor: filterColor,
          blur: blur,
          mirrorX: mirrorX,
          mirrorY: mirrorY,
          rotation: rotation,
          opacity: opacity,
          fadeInWhen: fadeInWhen,
          fadeInDuration: fadeInDuration,
          pinch: pinch,
          fadeOut: fadeOut,
          fadeOutWhen: fadeOutWhen,
          fadeOutDuration: fadeOutDuration,
          // timingstart: timingstart,
        );

  @override
  copy({bool clearMasks = false, bool showHidden = false}) {
    final _copy = SimShape(shape: shape, x: x, y: y, scale: scale);
    _copy.blur = blur;
    _copy.filterColor = filterColor;
    _copy.width = width;
    _copy.height = height;
    _copy.scale = scale;
    _copy.widthScale = widthScale;
    _copy.heightScale = heightScale;
    _copy.mirrorX = mirrorX;
    _copy.mirrorY = mirrorY;
    _copy.rotation = rotation;
    _copy.width = width;
    _copy.opacity = opacity;
    _copy.maskIds = clearMasks ? [] : maskIds.toList();
    // _copy.maskIds = [];
    _copy.priority = priority;
    _copy.fadeInWhen = fadeInWhen;
    _copy.fadeInDuration = fadeInDuration;
    _copy.fadeOut = fadeOut;
    _copy.fadeOutWhen = fadeOutWhen;
    _copy.fadeOutDuration = fadeOutDuration;
    // _copy.timingstart = timingstart;
    _copy.movable = movable;
    _copy.trigger = trigger;
    _copy.triggerOnce = triggerOnce;
    _copy.hidden = showHidden ? false : hidden;
    _copy.clickToToggle = clickToToggle;
    _copy.hideOnStart = hideOnStart;
    return _copy;
  }

  @override
  bool equalTo(SimObject other) {
    if (other is! SimShape) {
      return false;
    }
    if (super.equalTo(other) == false) {
      return false;
    }
    if (shape != other.shape) {
      return false;
    }
    return true;
  }

  @override
  String toString() {
    return "SimShape({ " + super.toString() + " shape: $shape })";
  }
}

class SimSound extends SimObject {
  String path;
  bool loop = false;
  // buffer
  // Uint8List? buffer;

  SimSound({
    required this.path,
    this.loop = false,
    // this.buffer,
    String id = "",
    required double x,
    required double y,
    double width = 1.0,
    double height = 1.0,
    double scale = 1.0,
    double widthScale = 1.0,
    double heightScale = 1.0,
    Color filterColor = defaultFilterColor,
    double blur = 0.0,
    bool mirrorX = false,
    bool mirrorY = false,
    double rotation = 0.0,
    double opacity = 1.0,
  }) : super(
          id: id,
          x: x,
          y: y,
          scale: scale,
          widthScale: widthScale,
          heightScale: heightScale,
          filterColor: filterColor,
          blur: blur,
          mirrorX: mirrorX,
          mirrorY: mirrorY,
          rotation: rotation,
          opacity: opacity,
        );

  @override
  copy({bool clearMasks = false, bool showHidden = false}) {
    final _copy = SimSound(path: path, loop: loop, x: x, y: y, scale: scale);
    _copy.blur = blur;
    _copy.filterColor = filterColor;
    _copy.width = width;
    _copy.height = height;
    _copy.scale = scale;
    _copy.widthScale = widthScale;
    _copy.heightScale = heightScale;
    _copy.mirrorX = mirrorX;
    _copy.mirrorY = mirrorY;
    _copy.rotation = rotation;
    _copy.width = width;
    _copy.opacity = opacity;
    _copy.maskIds = clearMasks ? [] : maskIds.toList();
    // _copy.maskIds = [];
    _copy.priority = priority;
    _copy.fadeInWhen = fadeInWhen;
    _copy.fadeInDuration = fadeInDuration;
    _copy.fadeOut = fadeOut;
    _copy.fadeOutWhen = fadeOutWhen;
    _copy.fadeOutDuration = fadeOutDuration;
    // _copy.timingstart = timingstart;
    _copy.movable = movable;
    _copy.trigger = trigger;
    _copy.triggerOnce = triggerOnce;
    _copy.hidden = showHidden ? false : hidden;
    _copy.clickToToggle = clickToToggle;
    _copy.hideOnStart = hideOnStart;
    return _copy;
  }

  @override
  reset() {
    super.reset();
    filterColor = Colors.white;
  }

  @override
  bool equalTo(SimObject other) {
    if (other is! SimSound) {
      return false;
    }
    if (super.equalTo(other) == false) {
      return false;
    }
    if (path != other.path || loop != other.loop) {
      return false;
    }
    return true;
  }

  @override
  String toString() {
    return "SimSound({ " + super.toString() + " path: $path })";
  }
}

class SimLocationJumper extends SimShape {
  String to;
  int delay;
  bool clickable;
  String? name;
  SimLocationJumper({
    required this.to,
    this.delay = 0,
    this.clickable = true,
    this.name,
    required String shape,
    String id = "",
    required double x,
    required double y,
    double width = 1.0,
    double height = 1.0,
    double scale = 1.0,
    double widthScale = 1.0,
    double heightScale = 1.0,
    Color filterColor = defaultFilterColor,
    double blur = 0.0,
    bool mirrorX = false,
    bool mirrorY = false,
    double rotation = 0.0,
    double opacity = 1.0,
  }) : super(
          shape: shape,
          id: id,
          x: x,
          y: y,
          scale: scale,
          widthScale: widthScale,
          heightScale: heightScale,
          filterColor: filterColor,
          blur: blur,
          mirrorX: mirrorX,
          mirrorY: mirrorY,
          rotation: rotation,
          opacity: opacity,
        );

  @override
  copy({bool clearMasks = false, bool showHidden = false}) {
    final _copy = SimLocationJumper(
      to: to,
      clickable: clickable,
      name: name,
      delay: delay,
      shape: shape,
      x: x,
      y: y,
      scale: scale,
    );
    _copy.blur = blur;
    _copy.filterColor = filterColor;
    _copy.width = width;
    _copy.height = height;
    _copy.scale = scale;
    _copy.widthScale = widthScale;
    _copy.heightScale = heightScale;
    _copy.mirrorX = mirrorX;
    _copy.mirrorY = mirrorY;
    _copy.rotation = rotation;
    _copy.width = width;
    _copy.opacity = opacity;
    _copy.maskIds = clearMasks ? [] : maskIds.toList();
    // _copy.maskIds = [];
    _copy.priority = priority;
    _copy.fadeInWhen = fadeInWhen;
    _copy.fadeInDuration = fadeInDuration;
    _copy.fadeOut = fadeOut;
    _copy.fadeOutWhen = fadeOutWhen;
    _copy.fadeOutDuration = fadeOutDuration;
    // _copy.timingstart = timingstart;
    _copy.movable = movable;
    _copy.trigger = trigger;
    _copy.triggerOnce = triggerOnce;
    _copy.hidden = showHidden ? false : hidden;
    _copy.clickToToggle = clickToToggle;
    _copy.hideOnStart = hideOnStart;
    return _copy;
  }

  static SimLocationJumper fromShape(SimShape shape, String to, {bool clickable = true, int delay = 0}) {
    final instance = SimLocationJumper(
      to: to,
      clickable: clickable,
      delay: delay,
      shape: shape.shape,
      x: shape.x,
      y: shape.y,
      scale: shape.scale,
    );
    instance.blur = shape.blur;
    instance.filterColor = shape.filterColor;
    instance.width = shape.width;
    instance.height = shape.height;
    instance.scale = shape.scale;
    instance.widthScale = shape.widthScale;
    instance.heightScale = shape.heightScale;
    instance.mirrorX = shape.mirrorX;
    instance.mirrorY = shape.mirrorY;
    instance.rotation = shape.rotation;
    instance.width = shape.width;
    instance.opacity = shape.opacity;
    instance.maskIds = shape.maskIds.toList();
    instance.priority = shape.priority;
    instance.fadeInWhen = shape.fadeInWhen;
    instance.fadeInDuration = shape.fadeInDuration;
    instance.fadeOut = shape.fadeOut;
    instance.fadeOutWhen = shape.fadeOutWhen;
    instance.fadeOutDuration = shape.fadeOutDuration;
    // instance.timingstart = shape.timingstart;
    instance.trigger = shape.trigger;
    instance.movable = shape.movable;
    instance.trigger = shape.trigger;
    return instance;
  }

  @override
  reset() {
    super.reset();
    filterColor = Colors.white;
  }

  @override
  bool equalTo(SimObject other) {
    if (other is! SimLocationJumper) {
      return false;
    }
    if (super.equalTo(other) == false) {
      return false;
    }
    if (to != other.to || delay != other.delay || clickable != other.clickable) {
      return false;
    }
    return true;
  }

  @override
  String toString() {
    return "SimLocationJumper({ " + super.toString() + " to: $to, delay: $delay, clickable: $clickable })";
  }
}

class SimLabel extends SimObject {
  String name;
  String type;
  Map<String, dynamic> variables;

  SimLabel({
    required this.type,
    required this.variables,
    this.name = "",
    String id = "",
    required double x,
    required double y,
    double width = 1.0,
    double height = 1.0,
    double scale = 1.0,
    double widthScale = 1.0,
    double heightScale = 1.0,
    Color filterColor = defaultFilterColor,
    double blur = 0.0,
    bool mirrorX = false,
    bool mirrorY = false,
    double rotation = 0.0,
    double opacity = 1.0,
  }) : super(
          id: id,
          x: x,
          y: y,
          scale: scale,
          widthScale: widthScale,
          heightScale: heightScale,
          filterColor: filterColor,
          blur: blur,
          mirrorX: mirrorX,
          mirrorY: mirrorY,
          rotation: rotation,
          opacity: opacity,
        );

  @override
  copy({bool clearMasks = false, bool showHidden = false}) {
    final _copy = SimLabel(type: type, variables: variables, name: name, x: x, y: y, scale: scale);
    _copy.blur = blur;
    _copy.filterColor = filterColor;
    _copy.width = width;
    _copy.height = height;
    _copy.scale = scale;
    _copy.widthScale = widthScale;
    _copy.heightScale = heightScale;
    _copy.mirrorX = mirrorX;
    _copy.mirrorY = mirrorY;
    _copy.rotation = rotation;
    _copy.width = width;
    _copy.opacity = opacity;
    _copy.maskIds = clearMasks ? [] : maskIds.toList();
    // _copy.maskIds = [];
    _copy.priority = priority;
    _copy.fadeInWhen = fadeInWhen;
    _copy.fadeInDuration = fadeInDuration;
    _copy.fadeOut = fadeOut;
    _copy.fadeOutWhen = fadeOutWhen;
    _copy.fadeOutDuration = fadeOutDuration;
    // _copy.timingstart = timingstart;
    _copy.movable = movable;
    _copy.trigger = trigger;
    _copy.triggerOnce = triggerOnce;
    _copy.hidden = showHidden ? false : hidden;
    _copy.clickToToggle = clickToToggle;
    _copy.hideOnStart = hideOnStart;
    return _copy;
  }

  @override
  bool equalTo(SimObject other) {
    if (other is! SimLabel) {
      return false;
    }
    if (super.equalTo(other) == false) {
      return false;
    }
    if (type != other.type || variables != other.variables) {
      return false;
    }
    return true;
  }

  @override
  String toString() {
    return "SimLabel({ " + super.toString() + ", name: $name, type: $type, variables: $variables })";
  }
}

class SimPerson extends SimObject {
  String name;
  String type;
  int posture;
  String? syncVariable;

  SimPerson({
    required this.type,
    required this.posture,
    this.name = "",
    this.syncVariable,
    String id = "",
    required double x,
    required double y,
    double width = 1.0,
    double height = 1.0,
    double scale = 1.0,
    double widthScale = 1.0,
    double heightScale = 1.0,
    Color filterColor = defaultFilterColor,
    double blur = 0.0,
    bool mirrorX = false,
    bool mirrorY = false,
    double rotation = 0.0,
    double opacity = 1.0,
    bool hideOnStart = false,
  }) : super(
          id: id,
          x: x,
          y: y,
          scale: scale,
          widthScale: widthScale,
          heightScale: heightScale,
          filterColor: filterColor,
          blur: blur,
          mirrorX: mirrorX,
          mirrorY: mirrorY,
          rotation: rotation,
          opacity: opacity,
          hideOnStart: hideOnStart,
        );

  @override
  copy({bool clearMasks = false, bool showHidden = false}) {
    final _copy = SimPerson(type: type, posture: posture, name: name, syncVariable: syncVariable, x: x, y: y, scale: scale);
    _copy.blur = blur;
    _copy.filterColor = filterColor;
    _copy.width = width;
    _copy.height = height;
    _copy.scale = scale;
    _copy.widthScale = widthScale;
    _copy.heightScale = heightScale;
    _copy.mirrorX = mirrorX;
    _copy.mirrorY = mirrorY;
    _copy.rotation = rotation;
    _copy.width = width;
    _copy.opacity = opacity;
    _copy.maskIds = clearMasks ? [] : maskIds.toList();
    // _copy.maskIds = [];
    _copy.priority = priority;
    _copy.fadeInWhen = fadeInWhen;
    _copy.fadeInDuration = fadeInDuration;
    _copy.fadeOut = fadeOut;
    _copy.fadeOutWhen = fadeOutWhen;
    _copy.fadeOutDuration = fadeOutDuration;
    // _copy.timingstart = timingstart;
    _copy.movable = movable;
    _copy.trigger = trigger;
    _copy.triggerOnce = triggerOnce;
    _copy.hidden = showHidden ? false : hidden;
    _copy.clickToToggle = clickToToggle;
    _copy.hideOnStart = hideOnStart;
    return _copy;
  }

  @override
  reset() {
    super.reset();
    syncVariable = null;
  }

  @override
  bool equalTo(SimObject other) {
    if (other is! SimPerson) {
      return false;
    }
    if (super.equalTo(other) == false) {
      return false;
    }
    if (name != other.name || type != other.type || posture != other.posture || syncVariable != other.syncVariable) {
      return false;
    }
    return true;
  }

  @override
  String toString() {
    return "SimPerson({ " + super.toString() + ", name: $name, type: $type, posture: $posture, syncVariable: $syncVariable })";
  }
}

class SimContainer extends SimObject {
  String name;
  String type;
  int view;

  SimContainer({
    required this.type,
    required this.view,
    this.name = "",
    String id = "",
    required double x,
    required double y,
    double width = 1.0,
    double height = 1.0,
    double scale = 1.0,
    double widthScale = 1.0,
    double heightScale = 1.0,
    Color filterColor = defaultFilterColor,
    double blur = 0.0,
    bool mirrorX = false,
    bool mirrorY = false,
    double rotation = 0.0,
    double opacity = 1.0,
  }) : super(
          id: id,
          x: x,
          y: y,
          scale: scale,
          widthScale: widthScale,
          heightScale: heightScale,
          filterColor: filterColor,
          blur: blur,
          mirrorX: mirrorX,
          mirrorY: mirrorY,
          rotation: rotation,
          opacity: opacity,
        );

  @override
  copy({bool clearMasks = false, bool showHidden = false}) {
    final _copy = SimContainer(type: type, view: view, name: name, x: x, y: y, scale: scale);
    _copy.blur = blur;
    _copy.filterColor = filterColor;
    _copy.width = width;
    _copy.height = height;
    _copy.scale = scale;
    _copy.widthScale = widthScale;
    _copy.heightScale = heightScale;
    _copy.mirrorX = mirrorX;
    _copy.mirrorY = mirrorY;
    _copy.rotation = rotation;
    _copy.width = width;
    _copy.opacity = opacity;
    _copy.maskIds = clearMasks ? [] : maskIds.toList();
    // _copy.maskIds = [];
    _copy.priority = priority;
    _copy.fadeInWhen = fadeInWhen;
    _copy.fadeInDuration = fadeInDuration;
    _copy.fadeOut = fadeOut;
    _copy.fadeOutWhen = fadeOutWhen;
    _copy.fadeOutDuration = fadeOutDuration;
    // _copy.timingstart = timingstart;
    _copy.movable = movable;
    _copy.trigger = trigger;
    _copy.triggerOnce = triggerOnce;
    _copy.hidden = showHidden ? false : hidden;
    _copy.clickToToggle = clickToToggle;
    _copy.hideOnStart = hideOnStart;
    return _copy;
  }

  @override
  bool equalTo(SimObject other) {
    if (other is! SimContainer) {
      return false;
    }
    if (super.equalTo(other) == false) {
      return false;
    }
    if (name != other.name || type != other.type || view != other.view) {
      return false;
    }
    return true;
  }

  @override
  String toString() {
    return "SimContainer({ " + super.toString() + ", name: $name, type: $type, view: $view })";
  }
}

enum SimTimerType {
  countdown,
  exercise,
  location,
  timeOfDay,
  state,
}

enum SimTimerFormat {
  minuteSeconds,
  hourMinuteSeconds,
}

class SimTimer extends SimObject {
  SimTimerType type;
  SimTimerFormat format;
  int seconds;

  // int startingHour;
  // int startingMinute;
  int startingSecond;

  SimTimer({
    required this.type,
    required this.format,
    required this.seconds,
    // this.startingHour = 0,
    // this.startingMinute = 0,
    this.startingSecond = 0,
    String id = "",
    required double x,
    required double y,
    double width = 1.0,
    double height = 1.0,
    double scale = 1.0,
    double widthScale = 1.0,
    double heightScale = 1.0,
    Color filterColor = Colors.white,
    double blur = 0.0,
    bool mirrorX = false,
    bool mirrorY = false,
    double rotation = 0.0,
    double opacity = 1.0,
  }) : super(
          id: id,
          x: x,
          y: y,
          scale: scale,
          widthScale: widthScale,
          heightScale: heightScale,
          filterColor: filterColor,
          blur: blur,
          mirrorX: mirrorX,
          mirrorY: mirrorY,
          rotation: rotation,
          opacity: opacity,
        );

  @override
  copy({bool clearMasks = false, bool showHidden = false}) {
    final _copy = SimTimer(
      type: type,
      format: format,
      seconds: seconds,
      // startingHour: startingHour,
      // startingMinute: startingMinute,
      startingSecond: startingSecond,
      x: x,
      y: y,
      scale: scale,
    );
    _copy.blur = blur;
    _copy.filterColor = filterColor;
    _copy.width = width;
    _copy.height = height;
    _copy.scale = scale;
    _copy.widthScale = widthScale;
    _copy.heightScale = heightScale;
    _copy.mirrorX = mirrorX;
    _copy.mirrorY = mirrorY;
    _copy.rotation = rotation;
    _copy.width = width;
    _copy.opacity = opacity;
    _copy.maskIds = clearMasks ? [] : maskIds.toList();
    // _copy.maskIds = [];
    _copy.priority = priority;
    _copy.fadeInWhen = fadeInWhen;
    _copy.fadeInDuration = fadeInDuration;
    _copy.fadeOut = fadeOut;
    _copy.fadeOutWhen = fadeOutWhen;
    _copy.fadeOutDuration = fadeOutDuration;
    // _copy.timingstart = timingstart;
    _copy.movable = movable;
    _copy.trigger = trigger;
    _copy.triggerOnce = triggerOnce;
    _copy.hidden = showHidden ? false : hidden;
    _copy.clickToToggle = clickToToggle;
    _copy.hideOnStart = hideOnStart;
    return _copy;
  }

  @override
  bool equalTo(SimObject other) {
    if (other is! SimTimer) {
      return false;
    }
    if (super.equalTo(other) == false) {
      return false;
    }
    if (type != other.type || format != other.format || seconds != other.seconds || startingSecond != other.startingSecond) {
      return false;
    }
    return true;
  }

  @override
  String toString() {
    return "SimTimer({ " + super.toString() + ", type: $type, format: $format, seconds: $seconds, startingSecond: $startingSecond })";
    // " type: $type, format: $format, seconds: $seconds, startingHour: $startingHour, startingMinute: $startingMinute, startingSecond: $startingSecond })";
  }
}

double generateSimObjectDrift() {
  final _random = math.Random();
  final _drift = _random.nextDouble() * 0.05;
  return _drift;
}
