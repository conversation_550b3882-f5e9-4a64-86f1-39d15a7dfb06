import 'package:flutter/material.dart';
import 'package:nanoid/nanoid.dart';
import 'dart:math' as math;

class Mask {
  String id;
  String name;
  List<Coordinate> coordinates;
  MaskType type;
  String color;
  String locationId;

  Mask({this.id = "", this.name = "", this.coordinates = const [], required this.type, this.color = "", required this.locationId}) {
    if (id.isEmpty) {
      id = nanoid(12);
    }
    if (name.isEmpty) {
      name = "Mask-${nanoid(4)}";
    }
    if (color.isEmpty) {
      color = Color((math.Random(DateTime.now().millisecondsSinceEpoch).nextDouble() * 0xFFFFFF).toInt())
          .withOpacity(0.4)
          .toString()
          .replaceAll("Color(0x", "")
          .replaceAll(")", "");
    }
    if (coordinates.isEmpty) {
      coordinates = List<Coordinate>.empty(growable: true);
    }
  }

  Mask copy({bool clone = false}) {
    return Mask(type: type, color: color, coordinates: coordinates, id: clone ? id : nanoid(12), name: name, locationId: locationId);
  }

  bool needsParsing() {
    for (int i = 0; i < coordinates.length; i++) {
      final coor = coordinates[i];
      if (coor.x > 1 || coor.y > 1 || coor.x < -1 || coor.y < -1) {
        return false;
      }
    }
    return true;
  }

  Mask scale(double width, double height) {
    coordinates = coordinates.map((coor) => Coordinate(coor.x * width, coor.y * height)).toList();
    return this;
  }

  bool equalTo(Mask other) {
    if (id != other.id) {
      return false;
    }
    if (name != other.name) {
      return false;
    }
    if (coordinates.length != other.coordinates.length) {
      return false;
    }
    for (int i = 0; i < coordinates.length; i++) {
      if (coordinates[i].x != other.coordinates[i].x || coordinates[i].y != other.coordinates[i].y) {
        return false;
      }
    }
    if (type != other.type) {
      return false;
    }
    if (color != other.color) {
      return false;
    }
    if (locationId != other.locationId) {
      return false;
    }
    return true;
  }

  @override
  String toString() {
    return "{id: $id, name: $name, coordinates: $coordinates, type: $type, color: $color, locationId: $locationId}";
  }
}

enum MaskType { showWithin, showOutside }

class Coordinate {
  double x;
  double y;

  Coordinate(this.x, this.y);

  @override
  String toString() {
    return "{x: $x, y: $y}";
  }
}
