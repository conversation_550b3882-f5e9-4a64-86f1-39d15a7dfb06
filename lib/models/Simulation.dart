import 'package:flutter/material.dart';
import 'package:simsushare_player/models/Mask.dart';
import 'package:simsushare_player/models/SimObjects.dart';
import 'package:nanoid/nanoid.dart' as nanoid;

class Scenario {
  String id;
  String name;
  final List<SimulationLocation> locations;
  String categoryId;
  String currentState;
  late List<SimulationState> states;
  late List<Mask> masks;
  double width;
  double height;
  String directoryPath;
  late List<SimulationNavigation> navigations;
  double? navClusterX;
  double? navClusterY;
  String? initialLocationId;
  String? initialStateId;
  // Functional properties
  bool inPlayMode = false;

  Scenario({
    required this.name,
    required this.locations,
    this.currentState = "Initial",
    masks,
    this.id = "",
    this.directoryPath = "",
    this.width = 0,
    this.height = 0,
    this.categoryId = "",
    navigations,
    this.states = const [],
    this.navClusterX,
    this.navClusterY,
    this.initialLocationId,
    this.initialStateId,
    this.inPlayMode = false,
  }) : navigations = navigations ?? List<SimulationNavigation>.empty(growable: true) {
    if (states.isEmpty) {
      states = [SimulationState(id: "state0", name: currentState)];
    }
    if (id == "") {
      id = "__" + nanoid.nanoid(10);
    }
    this.masks = masks ?? List<Mask>.empty(growable: true);
    if (navigations == null) {
      this.navigations = List<SimulationNavigation>.empty(growable: true);
    }
    // initialLocationId ??= locations.first.id;
    // initialStateId ??= states.first.id;
  }

  Scenario copy({bool clone = false}) {
    print("Simulation Copy");
    final newStates = states.map((e) => e.copy()).toList();
    final stateOldNewMapping = <String, String>{};
    for (var i = 0; i < states.length; i++) {
      stateOldNewMapping[states[i].id] = newStates[i].id;
    }

    final newMasks = masks.map((e) => e.copy()).toList();
    final maskOldNewMapping = <String, String>{};
    for (var i = 0; i < masks.length; i++) {
      maskOldNewMapping[masks[i].id] = newMasks[i].id;
    }

    // Copying location IDs without relying on clone
    final newLocations = locations.map((e) => e.copy(id: e.id)).toList();
    final locationOldNewMapping = <String, String>{};
    for (var i = 0; i < locations.length; i++) {
      locationOldNewMapping[locations[i].id] = newLocations[i].id;
    }

    // sync vars handling
    final oldPeople = locations.expand((element) => element.people).toList();
    final newPeople = newLocations.expand((element) => element.people).toList();
    final oldPeopleIds = oldPeople.map((e) => e.id).toList();
    final newPeopleIds = newPeople.map((e) => e.id).toList();
    for (var i = 0; i < oldPeople.length; i++) {
      final oldPerson = oldPeople[i];
      if (oldPerson.syncVariable != null) {
        final match = oldPeopleIds.indexOf(oldPerson.syncVariable!);
        if (match != -1) {
          newPeople[i].syncVariable = newPeopleIds[match];
        }
      }
    }
    final oldImages = locations.expand((element) => element.images).toList();
    final newImages = newLocations.expand((element) => element.images).toList();
    for (var i = 0; i < oldImages.length; i++) {
      final img = oldImages[i];
      if (img.syncVariable == null) continue;
      if (img.syncVariable!.contains("___")) {
        final parts = img.syncVariable!.split("___");
        final locIndex = locations.indexWhere((loc) => loc.id == parts[1]);
        if (locIndex != -1) {
          parts[1] = newLocations[locIndex].id;
          oldImages[i].syncVariable = parts.join("___");
        }
      } else {
        final match = oldPeopleIds.indexOf(img.syncVariable!);
        if (match != -1) {
          print("Replacing sync variable ${img.syncVariable} with ${newPeopleIds[match]}");
          // oldImages[i].syncVariable = newPeopleIds[match];
          newImages[i].syncVariable = newPeopleIds[match];
        } else {
          print("Sync variable ${img.syncVariable} not found in people old $oldPeopleIds while new people are $newPeopleIds");
        }
      }
    }

    for (var loc in newLocations) {
      loc.state = stateOldNewMapping[loc.state]!;
      for (var element in loc.sprites) {
        element.maskIds = element.maskIds.map((e) => maskOldNewMapping[e]).where((e) => e != null).map((e) => e!).toList();
      }
      for (var element in loc.images) {
        element.maskIds = element.maskIds.map((e) => maskOldNewMapping[e]).where((e) => e != null).map((e) => e!).toList();
      }
      for (var element in loc.shapes) {
        element.maskIds = element.maskIds.map((e) => maskOldNewMapping[e]).where((e) => e != null).map((e) => e!).toList();
      }
      for (var element in loc.jumpers) {
        element.maskIds = element.maskIds.map((e) => maskOldNewMapping[e]).where((e) => e != null).map((e) => e!).toList();
      }
      for (var element in loc.texts) {
        element.maskIds = element.maskIds.map((e) => maskOldNewMapping[e]).where((e) => e != null).map((e) => e!).toList();
      }
      for (var element in loc.labels) {
        element.maskIds = element.maskIds.map((e) => maskOldNewMapping[e]).where((e) => e != null).map((e) => e!).toList();
      }
      for (var element in loc.containers) {
        element.maskIds = element.maskIds.map((e) => maskOldNewMapping[e]).where((e) => e != null).map((e) => e!).toList();
      }
      for (var element in loc.people) {
        element.maskIds = element.maskIds.map((e) => maskOldNewMapping[e]).where((e) => e != null).map((e) => e!).toList();
      }
      for (var element in loc.timers) {
        element.maskIds = element.maskIds.map((e) => maskOldNewMapping[e]).where((e) => e != null).map((e) => e!).toList();
      }
    }
    final newNavigations = navigations.map((e) => e.copy()).toList();
    for (var nav in newNavigations) {
      nav.from = locationOldNewMapping[nav.from]!;
      nav.to = locationOldNewMapping[nav.to]!;
    }

    return Scenario(
      id: clone ? id : nanoid.nanoid(10),
      name: name,
      // locations: locations.map((e) => e.copy()).toList(),
      locations: newLocations,
      categoryId: categoryId,
      currentState: currentState,
      // states: states.map((e) => e.copy()).toList(),
      states: newStates,
      // masks: masks.map((e) => e.copy(clone: clone)).toList(),
      masks: newMasks,
      width: width,
      height: height,
      directoryPath: directoryPath,
      // navigations: navigations,
      navigations: newNavigations,
      navClusterX: navClusterX,
      navClusterY: navClusterY,
      initialLocationId: initialLocationId,
      initialStateId: initialStateId,
      inPlayMode: inPlayMode,
    );
  }

  /* @override
  get hashCode => Object.hash(id, name, locations, currentState, states, masks, width, height, directoryPath, categoryId, navigations, navClusterX,
      navClusterY, initialLocationId, initialStateId, inPlayMode);

  @override
  bool operator ==(Object other) {
    if (other is Scenario) {
      return id == other.id &&
          name == other.name &&
          locations == other.locations &&
          currentState == other.currentState &&
          states == other.states &&
          masks == other.masks &&
          width == other.width &&
          height == other.height &&
          directoryPath == other.directoryPath &&
          categoryId == other.categoryId &&
          navigations == other.navigations &&
          navClusterX == other.navClusterX &&
          navClusterY == other.navClusterY &&
          initialLocationId == other.initialLocationId &&
          initialStateId == other.initialStateId &&
          inPlayMode == other.inPlayMode;
    }
    return false;
  } */

  bool equalTo(Scenario other) {
    if (id != other.id ||
        name != other.name ||
        locations.length != other.locations.length ||
        currentState != other.currentState ||
        states.length != other.states.length ||
        masks.length != other.masks.length ||
        width != other.width ||
        height != other.height ||
        directoryPath != other.directoryPath ||
        categoryId != other.categoryId ||
        navigations.length != other.navigations.length ||
        navClusterX != other.navClusterX ||
        navClusterY != other.navClusterY ||
        initialLocationId != other.initialLocationId ||
        initialStateId != other.initialStateId ||
        inPlayMode != other.inPlayMode) {
      return false;
    }

    if (locations.length != other.locations.length) return false;
    for (var i = 0; i < locations.length; i++) {
      if (!locations[i].equalTo(other.locations[i])) return false;
    }

    if (states.length != other.states.length) return false;
    for (var i = 0; i < states.length; i++) {
      if (states[i].equalTo(other.states[i])) return false;
    }

    if (masks.length != other.masks.length) return false;
    for (var i = 0; i < masks.length; i++) {
      if (!masks[i].equalTo(other.masks[i])) return false;
    }

    for (var i = 0; i < navigations.length; i++) {
      if (navigations[i].direction != other.navigations[i].direction) return false;
      if (navigations[i].from != other.navigations[i].from) return false;
      if (navigations[i].to != other.navigations[i].to) return false;
    }

    return true;
  }

  @override
  String toString() {
    return "{name: $name, currentState: $currentState, states: $states, locations: $locations, masks: $masks, directoryPath: $directoryPath, categoryId: $categoryId, navigations: $navigations, inPlayMode: $inPlayMode}";
  }
}

class SimulationLocation {
  /// used in web to still get the image file name if [image] var is not a file path
  String? imageFileName;
  String image;
  double imageScale;
  Offset imageOffset;
  double imageBrightness;
  String color;
  String name;
  String state;
  String id;
  int parentIndex;
  bool disabled;
  final List<SimSprite> sprites;
  final List<SimImage> images;
  final List<SimText> texts;
  final List<SimShape> shapes;
  final List<SimSound> sounds;
  final List<SimLocationJumper> jumpers;
  final List<SimLabel> labels;
  final List<SimContainer> containers;
  final List<SimPerson> people;
  final List<SimTimer> timers;
  double imageRotation;

  SimulationLocation({
    required this.name,
    required this.sprites,
    this.id = "",
    // this.images = const <SimImage>[],
    // this.texts = const <SimText>[],
    this.image = "",
    this.imageScale = 1.0,
    this.imageOffset = Offset.zero,
    this.imageBrightness = 0.0,
    this.color = "",
    this.imageRotation = 0.0,
    this.state = "",
    this.parentIndex = -1,
    this.disabled = false,
  })  : images = List<SimImage>.empty(growable: true),
        texts = List<SimText>.empty(growable: true),
        shapes = List<SimShape>.empty(growable: true),
        sounds = List<SimSound>.empty(growable: true),
        jumpers = List<SimLocationJumper>.empty(growable: true),
        labels = List<SimLabel>.empty(growable: true),
        containers = List<SimContainer>.empty(growable: true),
        people = List<SimPerson>.empty(growable: true),
        timers = List<SimTimer>.empty(growable: true) {
    if (id == "") {
      id = nanoid.nanoid(5);
    }
  }

  SimulationLocation copy({String? id, bool ignoreObjects = false}) {
    final c = SimulationLocation(
      id: id ?? nanoid.nanoid(),
      name: name,
      sprites: List<SimSprite>.empty(growable: true),
      image: image,
      imageScale: imageScale,
      imageOffset: imageOffset,
      imageBrightness: imageBrightness,
      color: color,
      imageRotation: imageRotation,
      state: state,
      parentIndex: parentIndex,
      disabled: disabled,
    );
    if (!ignoreObjects) {
      c.sprites.addAll(sprites.map((e) => e.copy()));
      c.images.addAll(images.map((e) => e.copy()));
      c.texts.addAll(texts.map((e) => e.copy()));
      c.shapes.addAll(shapes.map((e) => e.copy()));
      c.jumpers.addAll(jumpers.map((e) => e.copy()));
      c.sounds.addAll(sounds.map((e) => e.copy()));
      c.labels.addAll(labels.map((e) => e.copy()));
      c.containers.addAll(containers.map((e) => e.copy()));
      c.people.addAll(people.map((e) => e.copy()));
      c.timers.addAll(timers.map((e) => e.copy()));
    }
    return c;
  }

  migrateMasks({required Map<String, String> oldToNewMaskMapping}) {
    for (var obj in sprites) {
      obj.maskIds = obj.maskIds.map((maskId) => oldToNewMaskMapping[maskId] ?? maskId).toList();
    }
    for (var obj in images) {
      obj.maskIds = obj.maskIds.map((maskId) => oldToNewMaskMapping[maskId] ?? maskId).toList();
    }
    for (var obj in texts) {
      obj.maskIds = obj.maskIds.map((maskId) => oldToNewMaskMapping[maskId] ?? maskId).toList();
    }
    for (var obj in shapes) {
      obj.maskIds = obj.maskIds.map((maskId) => oldToNewMaskMapping[maskId] ?? maskId).toList();
    }
    for (var obj in jumpers) {
      obj.maskIds = obj.maskIds.map((maskId) => oldToNewMaskMapping[maskId] ?? maskId).toList();
    }
    for (var obj in sounds) {
      obj.maskIds = obj.maskIds.map((maskId) => oldToNewMaskMapping[maskId] ?? maskId).toList();
    }
    for (var obj in labels) {
      obj.maskIds = obj.maskIds.map((maskId) => oldToNewMaskMapping[maskId] ?? maskId).toList();
    }
    for (var obj in containers) {
      obj.maskIds = obj.maskIds.map((maskId) => oldToNewMaskMapping[maskId] ?? maskId).toList();
    }
    for (var obj in people) {
      obj.maskIds = obj.maskIds.map((maskId) => oldToNewMaskMapping[maskId] ?? maskId).toList();
    }
    for (var obj in timers) {
      obj.maskIds = obj.maskIds.map((maskId) => oldToNewMaskMapping[maskId] ?? maskId).toList();
    }
  }

  bool equalTo(SimulationLocation other) {
    if (id != other.id ||
        name != other.name ||
        image != other.image ||
        imageScale != other.imageScale ||
        imageOffset != other.imageOffset ||
        imageBrightness != other.imageBrightness ||
        color != other.color ||
        imageRotation != other.imageRotation ||
        state != other.state ||
        parentIndex != other.parentIndex ||
        disabled != other.disabled) {
      return false;
    }

    if (sprites.length != other.sprites.length) return false;
    for (var i = 0; i < sprites.length; i++) {
      if (!sprites[i].equalTo(other.sprites[i])) return false;
    }

    if (images.length != other.images.length) return false;
    for (var i = 0; i < images.length; i++) {
      if (!images[i].equalTo(other.images[i])) return false;
    }

    if (texts.length != other.texts.length) return false;
    for (var i = 0; i < texts.length; i++) {
      if (!texts[i].equalTo(other.texts[i])) return false;
    }

    if (shapes.length != other.shapes.length) return false;
    for (var i = 0; i < shapes.length; i++) {
      if (!shapes[i].equalTo(other.shapes[i])) return false;
    }

    if (sounds.length != other.sounds.length) return false;
    for (var i = 0; i < sounds.length; i++) {
      if (!sounds[i].equalTo(other.sounds[i])) return false;
    }

    if (jumpers.length != other.jumpers.length) return false;
    for (var i = 0; i < jumpers.length; i++) {
      if (!jumpers[i].equalTo(other.jumpers[i])) return false;
    }

    if (labels.length != other.labels.length) return false;
    for (var i = 0; i < labels.length; i++) {
      if (!labels[i].equalTo(other.labels[i])) return false;
    }

    if (containers.length != other.containers.length) return false;
    for (var i = 0; i < containers.length; i++) {
      if (!containers[i].equalTo(other.containers[i])) return false;
    }

    if (people.length != other.people.length) return false;
    for (var i = 0; i < people.length; i++) {
      if (!people[i].equalTo(other.people[i])) return false;
    }

    if (timers.length != other.timers.length) return false;
    for (var i = 0; i < timers.length; i++) {
      if (!timers[i].equalTo(other.timers[i])) return false;
    }

    return true;
  }

  @override
  String toString() {
    return "{id: $id, name: $name, state: $state, image: $image, sprites: Sprite[${sprites.length}], imageRotation: $imageRotation}";
  }
}

class SimulationState {
  String id;
  String name;

  SimulationState({
    required this.id,
    required this.name,
  });

  SimulationState copy() {
    return SimulationState(id: nanoid.nanoid(), name: name);
  }

  bool equalTo(SimulationState other) {
    return id == other.id && name == other.name;
  }

  @override
  String toString() {
    return "{id: $id, name: $name}";
  }
}

class SimulationNavigation {
  String direction;
  String from;
  String to;

  SimulationNavigation({
    required this.direction,
    required this.from,
    required this.to,
  });

  SimulationNavigation copy() {
    return SimulationNavigation(direction: direction, from: from, to: to);
  }

  @override
  String toString() {
    return "{direction: $direction, from: $from, to: $to}";
  }
}
