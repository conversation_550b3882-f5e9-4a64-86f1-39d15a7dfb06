// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'CloudScenario.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

CloudScenario _$CloudScenarioFromJson(Map<String, dynamic> json) =>
    CloudScenario(
      id: json['_id'] as String,
      archiveURL: json['archiveURL'] as String? ?? "",
      categories: (json['categories'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      downloadURL: json['downloadURL'] as String? ?? "",
      filesURL: json['filesURL'] as String? ?? "",
      identifier: json['id'] as String? ?? "",
      isPublic: json['is_public'] as bool? ?? false,
      locations: json['locations'] as List<dynamic>? ?? const [],
      variables: json['variables'] as List<dynamic>? ?? const [],
      locationsStates: json['locationsStates'] as List<dynamic>? ?? const [],
      store: json['store'] as String? ?? "",
      title: json['title'] as String? ?? "",
      uniqueId: json['uniqueId'] as String? ?? "",
      updateURL: json['updateURL'] as String? ?? "",
    );

Map<String, dynamic> _$CloudScenarioToJson(CloudScenario instance) =>
    <String, dynamic>{
      '_id': instance.id,
      'archiveURL': instance.archiveURL,
      'categories': instance.categories,
      'downloadURL': instance.downloadURL,
      'filesURL': instance.filesURL,
      'id': instance.identifier,
      'is_public': instance.isPublic,
      'locations': instance.locations,
      'variables': instance.variables,
      'locationsStates': instance.locationsStates,
      'store': instance.store,
      'title': instance.title,
      'uniqueId': instance.uniqueId,
      'updateURL': instance.updateURL,
    };
