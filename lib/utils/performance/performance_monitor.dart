import 'dart:async';
import 'dart:collection';

/// Performance monitoring and profiling system
class PerformanceMonitor {
  static final PerformanceMonitor _instance = PerformanceMonitor._internal();
  factory PerformanceMonitor() => _instance;
  PerformanceMonitor._internal();

  final Map<String, Stopwatch> _activeTimers = {};
  final Map<String, List<int>> _operationTimes = {};
  final Map<String, int> _operationCounts = {};
  final Queue<PerformanceEvent> _recentEvents = Queue();

  static const int maxRecentEvents = 1000;
  static const int maxHistoryPerOperation = 100;

  /// Start timing an operation
  void startTimer(String operationName) {
    final stopwatch = Stopwatch()..start();
    _activeTimers[operationName] = stopwatch;
  }

  /// Stop timing an operation and record the result
  int stopTimer(String operationName) {
    final stopwatch = _activeTimers.remove(operationName);
    if (stopwatch == null) {
      throw ArgumentError("No active timer found for operation: $operationName");
    }

    stopwatch.stop();
    final elapsedMs = stopwatch.elapsedMilliseconds;

    _recordOperation(operationName, elapsedMs);
    return elapsedMs;
  }

  /// Time a synchronous operation
  T timeOperation<T>(String operationName, T Function() operation) {
    startTimer(operationName);
    try {
      final result = operation();
      stopTimer(operationName);
      return result;
    } catch (e) {
      stopTimer(operationName);
      rethrow;
    }
  }

  /// Time an asynchronous operation
  Future<T> timeAsyncOperation<T>(String operationName, Future<T> Function() operation) async {
    startTimer(operationName);
    try {
      final result = await operation();
      stopTimer(operationName);
      return result;
    } catch (e) {
      stopTimer(operationName);
      rethrow;
    }
  }

  /// Record a custom metric
  void recordMetric(String metricName, int value) {
    _recordOperation(metricName, value);
  }

  /// Get performance statistics for an operation
  PerformanceStats? getStats(String operationName) {
    final times = _operationTimes[operationName];
    final count = _operationCounts[operationName];

    if (times == null || count == null || times.isEmpty) {
      return null;
    }

    final sortedTimes = List<int>.from(times)..sort();
    final total = times.fold(0, (sum, time) => sum + time);
    final average = total / times.length;

    return PerformanceStats(
      operationName: operationName,
      totalOperations: count,
      averageTimeMs: average,
      minTimeMs: sortedTimes.first,
      maxTimeMs: sortedTimes.last,
      medianTimeMs: _calculateMedian(sortedTimes),
      p95TimeMs: _calculatePercentile(sortedTimes, 0.95),
      p99TimeMs: _calculatePercentile(sortedTimes, 0.99),
      totalTimeMs: total.toDouble(),
    );
  }

  /// Get all performance statistics
  Map<String, PerformanceStats> getAllStats() {
    final stats = <String, PerformanceStats>{};
    for (final operationName in _operationTimes.keys) {
      final stat = getStats(operationName);
      if (stat != null) {
        stats[operationName] = stat;
      }
    }
    return stats;
  }

  /// Get recent performance events
  List<PerformanceEvent> getRecentEvents({int? limit}) {
    final events = _recentEvents.toList();
    if (limit != null && events.length > limit) {
      return events.take(limit).toList();
    }
    return events;
  }

  /// Get performance summary
  PerformanceSummary getSummary() {
    final allStats = getAllStats();
    final totalOperations = allStats.values.fold(0, (sum, stat) => sum + stat.totalOperations);
    final totalTime = allStats.values.fold(0.0, (sum, stat) => sum + stat.totalTimeMs);

    final slowestOperations = allStats.values.toList()..sort((a, b) => b.averageTimeMs.compareTo(a.averageTimeMs));

    final mostFrequentOperations = allStats.values.toList()..sort((a, b) => b.totalOperations.compareTo(a.totalOperations));

    return PerformanceSummary(
      totalOperations: totalOperations,
      totalTimeMs: totalTime,
      averageOperationTimeMs: totalOperations > 0 ? totalTime / totalOperations : 0,
      uniqueOperations: allStats.length,
      slowestOperations: slowestOperations.take(5).toList(),
      mostFrequentOperations: mostFrequentOperations.take(5).toList(),
      cacheHitRate: _calculateCacheHitRate(),
    );
  }

  /// Clear all performance data
  void clear() {
    _activeTimers.clear();
    _operationTimes.clear();
    _operationCounts.clear();
    _recentEvents.clear();
  }

  /// Export performance data as JSON
  Map<String, dynamic> exportData() {
    return {
      'timestamp': DateTime.now().toIso8601String(),
      'summary': getSummary().toJson(),
      'operations': getAllStats().map((key, value) => MapEntry(key, value.toJson())),
      'recentEvents': _recentEvents.map((e) => e.toJson()).toList(),
    };
  }

  /// Record an operation result
  void _recordOperation(String operationName, int timeMs) {
    // Update operation times
    _operationTimes.putIfAbsent(operationName, () => <int>[]);
    final times = _operationTimes[operationName]!;
    times.add(timeMs);

    // Limit history size
    if (times.length > maxHistoryPerOperation) {
      times.removeAt(0);
    }

    // Update operation counts
    _operationCounts[operationName] = (_operationCounts[operationName] ?? 0) + 1;

    // Add to recent events
    final event = PerformanceEvent(
      operationName: operationName,
      timeMs: timeMs,
      timestamp: DateTime.now(),
    );
    _recentEvents.addLast(event);

    // Limit recent events size
    if (_recentEvents.length > maxRecentEvents) {
      _recentEvents.removeFirst();
    }
  }

  /// Calculate median from sorted list
  double _calculateMedian(List<int> sortedTimes) {
    final length = sortedTimes.length;
    if (length % 2 == 0) {
      return (sortedTimes[length ~/ 2 - 1] + sortedTimes[length ~/ 2]) / 2.0;
    } else {
      return sortedTimes[length ~/ 2].toDouble();
    }
  }

  /// Calculate percentile from sorted list
  double _calculatePercentile(List<int> sortedTimes, double percentile) {
    final index = (sortedTimes.length * percentile).floor();
    return sortedTimes[index.clamp(0, sortedTimes.length - 1)].toDouble();
  }

  /// Calculate cache hit rate (placeholder - would integrate with actual cache)
  double _calculateCacheHitRate() {
    // This would integrate with AssetCache to get actual hit rate
    return 0.0;
  }
}

/// Performance statistics for a specific operation
class PerformanceStats {
  final String operationName;
  final int totalOperations;
  final double averageTimeMs;
  final int minTimeMs;
  final int maxTimeMs;
  final double medianTimeMs;
  final double p95TimeMs;
  final double p99TimeMs;
  final double totalTimeMs;

  PerformanceStats({
    required this.operationName,
    required this.totalOperations,
    required this.averageTimeMs,
    required this.minTimeMs,
    required this.maxTimeMs,
    required this.medianTimeMs,
    required this.p95TimeMs,
    required this.p99TimeMs,
    required this.totalTimeMs,
  });

  Map<String, dynamic> toJson() => {
        'operationName': operationName,
        'totalOperations': totalOperations,
        'averageTimeMs': averageTimeMs,
        'minTimeMs': minTimeMs,
        'maxTimeMs': maxTimeMs,
        'medianTimeMs': medianTimeMs,
        'p95TimeMs': p95TimeMs,
        'p99TimeMs': p99TimeMs,
        'totalTimeMs': totalTimeMs,
      };
}

/// Individual performance event
class PerformanceEvent {
  final String operationName;
  final int timeMs;
  final DateTime timestamp;

  PerformanceEvent({
    required this.operationName,
    required this.timeMs,
    required this.timestamp,
  });

  Map<String, dynamic> toJson() => {
        'operationName': operationName,
        'timeMs': timeMs,
        'timestamp': timestamp.toIso8601String(),
      };
}

/// Overall performance summary
class PerformanceSummary {
  final int totalOperations;
  final double totalTimeMs;
  final double averageOperationTimeMs;
  final int uniqueOperations;
  final List<PerformanceStats> slowestOperations;
  final List<PerformanceStats> mostFrequentOperations;
  final double cacheHitRate;

  PerformanceSummary({
    required this.totalOperations,
    required this.totalTimeMs,
    required this.averageOperationTimeMs,
    required this.uniqueOperations,
    required this.slowestOperations,
    required this.mostFrequentOperations,
    required this.cacheHitRate,
  });

  Map<String, dynamic> toJson() => {
        'totalOperations': totalOperations,
        'totalTimeMs': totalTimeMs,
        'averageOperationTimeMs': averageOperationTimeMs,
        'uniqueOperations': uniqueOperations,
        'slowestOperations': slowestOperations.map((s) => s.toJson()).toList(),
        'mostFrequentOperations': mostFrequentOperations.map((s) => s.toJson()).toList(),
        'cacheHitRate': cacheHitRate,
      };
}
