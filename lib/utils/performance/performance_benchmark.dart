import 'dart:io';
import 'dart:convert';
import 'package:simsushare_player/utils/performance/performance_monitor.dart';
import 'package:simsushare_player/utils/performance/asset_cache.dart';
import 'package:simsushare_player/utils/performance/lazy_loader.dart';
import 'package:simsushare_player/utils/parsers.dart';
import 'package:simsushare_player/models/Simulation.dart';

/// Performance benchmarking system for parsing operations
class PerformanceBenchmark {
  static final PerformanceBenchmark _instance = PerformanceBenchmark._internal();
  factory PerformanceBenchmark() => _instance;
  PerformanceBenchmark._internal();

  final PerformanceMonitor _monitor = PerformanceMonitor();
  final AssetCache _cache = AssetCache();
  final LazyLoader _loader = LazyLoader();
  
  final List<BenchmarkResult> _results = [];

  /// Run comprehensive performance benchmark
  Future<BenchmarkSuite> runFullBenchmark({
    required List<String> testFiles,
    int iterations = 3,
  }) async {
    print("🚀 Starting Performance Benchmark Suite");
    print("Test files: ${testFiles.length}");
    print("Iterations per test: $iterations");
    
    final suite = BenchmarkSuite(
      startTime: DateTime.now(),
      testFiles: testFiles,
      iterations: iterations,
    );

    // Clear caches before starting
    _clearAllCaches();

    for (final testFile in testFiles) {
      print("\n📊 Benchmarking: $testFile");
      
      final fileResults = await _benchmarkFile(testFile, iterations);
      suite.results.addAll(fileResults);
      
      // Print intermediate results
      _printFileResults(testFile, fileResults);
    }

    suite.endTime = DateTime.now();
    suite.totalDuration = suite.endTime!.difference(suite.startTime);

    // Generate comprehensive report
    _generateBenchmarkReport(suite);
    
    return suite;
  }

  /// Benchmark a single file with multiple iterations
  Future<List<BenchmarkResult>> _benchmarkFile(String filePath, int iterations) async {
    final results = <BenchmarkResult>[];
    
    for (int i = 0; i < iterations; i++) {
      print("  Iteration ${i + 1}/$iterations");
      
      // Clear caches between iterations for consistent results
      if (i > 0) _clearAllCaches();
      
      final result = await _runSingleBenchmark(filePath, i + 1);
      results.add(result);
      
      // Brief pause between iterations
      await Future.delayed(const Duration(milliseconds: 100));
    }
    
    return results;
  }

  /// Run a single benchmark iteration
  Future<BenchmarkResult> _runSingleBenchmark(String filePath, int iteration) async {
    final file = File(filePath);
    final dir = file.parent;
    
    final result = BenchmarkResult(
      filePath: filePath,
      iteration: iteration,
      startTime: DateTime.now(),
    );

    try {
      // Measure file reading
      final fileContent = await _monitor.timeAsyncOperation('file_read', () async {
        return await file.readAsString();
      });
      
      // Measure parsing
      final scenario = await _monitor.timeAsyncOperation('parse_complete', () async {
        return await parseSimDef(fileContent, dir);
      });
      
      result.endTime = DateTime.now();
      result.success = true;
      result.scenario = scenario;
      
      // Collect performance metrics
      result.performanceStats = _monitor.getAllStats();
      result.cacheStats = _cache.statistics;
      result.loadingStats = _loader.getLoadingStats();
      
      // Calculate derived metrics
      result.totalElements = _countTotalElements(scenario);
      result.memoryUsage = _estimateMemoryUsage(scenario);
      
    } catch (e) {
      result.endTime = DateTime.now();
      result.success = false;
      result.error = e.toString();
    }
    
    return result;
  }

  /// Count total elements in scenario
  int _countTotalElements(Scenario scenario) {
    return scenario.locations.fold(0, (total, location) {
      return total +
          location.sprites.length +
          location.images.length +
          location.shapes.length +
          location.texts.length +
          location.jumpers.length +
          location.labels.length +
          location.containers.length +
          location.people.length +
          location.timers.length +
          location.sounds.length;
    });
  }

  /// Estimate memory usage
  int _estimateMemoryUsage(Scenario scenario) {
    // Rough estimation based on element counts and types
    int usage = 0;
    
    for (final location in scenario.locations) {
      usage += location.sprites.length * 50000; // ~50KB per sprite
      usage += location.images.length * 100000; // ~100KB per image
      usage += location.shapes.length * 1000; // ~1KB per shape
      usage += location.texts.length * 500; // ~500B per text
      usage += location.jumpers.length * 200; // ~200B per jumper
      usage += location.labels.length * 100; // ~100B per label
      usage += location.containers.length * 300; // ~300B per container
      usage += location.people.length * 2000; // ~2KB per person
      usage += location.timers.length * 100; // ~100B per timer
      usage += location.sounds.length * 200000; // ~200KB per sound
    }
    
    return usage;
  }

  /// Clear all caches
  void _clearAllCaches() {
    _monitor.clear();
    _cache.clearAll();
    _loader.clear();
  }

  /// Print results for a single file
  void _printFileResults(String filePath, List<BenchmarkResult> results) {
    final successful = results.where((r) => r.success).toList();
    if (successful.isEmpty) {
      print("  ❌ All iterations failed");
      return;
    }

    final avgDuration = successful
        .map((r) => r.duration.inMilliseconds)
        .reduce((a, b) => a + b) / successful.length;
    
    final avgElements = successful
        .map((r) => r.totalElements)
        .reduce((a, b) => a + b) / successful.length;

    print("  ✅ Average duration: ${avgDuration.toStringAsFixed(1)}ms");
    print("  📊 Average elements: ${avgElements.toStringAsFixed(0)}");
    print("  🎯 Success rate: ${(successful.length / results.length * 100).toStringAsFixed(1)}%");
  }

  /// Generate comprehensive benchmark report
  void _generateBenchmarkReport(BenchmarkSuite suite) {
    print("\n" + "="*60);
    print("🏆 PERFORMANCE BENCHMARK REPORT");
    print("="*60);
    
    final successful = suite.results.where((r) => r.success).toList();
    
    if (successful.isEmpty) {
      print("❌ No successful benchmark runs");
      return;
    }

    // Overall statistics
    final totalDuration = successful
        .map((r) => r.duration.inMilliseconds)
        .reduce((a, b) => a + b);
    final avgDuration = totalDuration / successful.length;
    
    final totalElements = successful
        .map((r) => r.totalElements)
        .reduce((a, b) => a + b);
    final avgElements = totalElements / successful.length;

    print("📈 OVERALL PERFORMANCE:");
    print("  Total runs: ${suite.results.length}");
    print("  Successful: ${successful.length}");
    print("  Success rate: ${(successful.length / suite.results.length * 100).toStringAsFixed(1)}%");
    print("  Average duration: ${avgDuration.toStringAsFixed(1)}ms");
    print("  Average elements: ${avgElements.toStringAsFixed(0)}");
    print("  Elements per second: ${(avgElements / (avgDuration / 1000)).toStringAsFixed(0)}");

    // Performance breakdown
    _printPerformanceBreakdown(successful);
    
    // Cache performance
    _printCachePerformance(successful);
    
    // Memory usage
    _printMemoryUsage(successful);
    
    // Save detailed report
    _saveDetailedReport(suite);
  }

  /// Print performance breakdown
  void _printPerformanceBreakdown(List<BenchmarkResult> results) {
    print("\n⚡ PERFORMANCE BREAKDOWN:");
    
    final operationTimes = <String, List<double>>{};
    
    for (final result in results) {
      for (final entry in result.performanceStats.entries) {
        operationTimes.putIfAbsent(entry.key, () => []);
        operationTimes[entry.key]!.add(entry.value.averageTimeMs);
      }
    }
    
    final sortedOperations = operationTimes.entries.toList()
      ..sort((a, b) => b.value.reduce((x, y) => x + y).compareTo(a.value.reduce((x, y) => x + y)));
    
    for (final entry in sortedOperations.take(10)) {
      final avgTime = entry.value.reduce((a, b) => a + b) / entry.value.length;
      print("  ${entry.key}: ${avgTime.toStringAsFixed(1)}ms");
    }
  }

  /// Print cache performance
  void _printCachePerformance(List<BenchmarkResult> results) {
    print("\n💾 CACHE PERFORMANCE:");
    
    final avgHitRate = results
        .map((r) => r.cacheStats['hitRate'] as double? ?? 0.0)
        .reduce((a, b) => a + b) / results.length;
    
    final avgMemoryUsage = results
        .map((r) => r.cacheStats['memoryUsage'] as int? ?? 0)
        .reduce((a, b) => a + b) / results.length;
    
    print("  Hit rate: ${(avgHitRate * 100).toStringAsFixed(1)}%");
    print("  Memory usage: ${(avgMemoryUsage / 1024 / 1024).toStringAsFixed(1)}MB");
  }

  /// Print memory usage
  void _printMemoryUsage(List<BenchmarkResult> results) {
    print("\n🧠 MEMORY USAGE:");
    
    final avgMemory = results
        .map((r) => r.memoryUsage)
        .reduce((a, b) => a + b) / results.length;
    
    print("  Estimated usage: ${(avgMemory / 1024 / 1024).toStringAsFixed(1)}MB");
  }

  /// Save detailed report to file
  void _saveDetailedReport(BenchmarkSuite suite) {
    final reportFile = File('performance_report_${DateTime.now().millisecondsSinceEpoch}.json');
    final reportData = suite.toJson();
    
    reportFile.writeAsStringSync(jsonEncode(reportData));
    print("\n📄 Detailed report saved: ${reportFile.path}");
  }
}

/// Benchmark suite containing all results
class BenchmarkSuite {
  final DateTime startTime;
  final List<String> testFiles;
  final int iterations;
  final List<BenchmarkResult> results = [];
  
  DateTime? endTime;
  Duration? totalDuration;

  BenchmarkSuite({
    required this.startTime,
    required this.testFiles,
    required this.iterations,
  });

  Map<String, dynamic> toJson() => {
    'startTime': startTime.toIso8601String(),
    'endTime': endTime?.toIso8601String(),
    'totalDuration': totalDuration?.inMilliseconds,
    'testFiles': testFiles,
    'iterations': iterations,
    'results': results.map((r) => r.toJson()).toList(),
  };
}

/// Individual benchmark result
class BenchmarkResult {
  final String filePath;
  final int iteration;
  final DateTime startTime;
  
  DateTime? endTime;
  bool success = false;
  String? error;
  Scenario? scenario;
  Map<String, dynamic> performanceStats = {};
  Map<String, dynamic> cacheStats = {};
  Map<String, dynamic> loadingStats = {};
  int totalElements = 0;
  int memoryUsage = 0;

  BenchmarkResult({
    required this.filePath,
    required this.iteration,
    required this.startTime,
  });

  Duration get duration => endTime!.difference(startTime);

  Map<String, dynamic> toJson() => {
    'filePath': filePath,
    'iteration': iteration,
    'startTime': startTime.toIso8601String(),
    'endTime': endTime?.toIso8601String(),
    'duration': duration.inMilliseconds,
    'success': success,
    'error': error,
    'totalElements': totalElements,
    'memoryUsage': memoryUsage,
    'performanceStats': performanceStats,
    'cacheStats': cacheStats,
    'loadingStats': loadingStats,
  };
}
