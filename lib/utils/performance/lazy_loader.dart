import 'dart:async';
import 'dart:ui' as ui;
import 'package:simsushare_player/utils/performance/asset_cache.dart';
import 'package:simsushare_player/utils/performance/performance_monitor.dart';

/// Load priority levels
enum LoadPriority {
  immediate(0), // Load immediately, bypass queue
  high(1), // Load as soon as possible
  normal(2), // Standard loading priority
  low(3), // Load when resources are available
  background(4); // Load in background when idle

  const LoadPriority(this.value);
  final int value;
}

/// Lazy loading system for assets with priority-based loading
class LazyLoader {
  static final LazyLoader _instance = LazyLoader._internal();
  factory LazyLoader() => _instance;
  LazyLoader._internal();

  final AssetCache _cache = AssetCache();
  final PerformanceMonitor _monitor = PerformanceMonitor();

  final Map<String, Future<dynamic>> _pendingLoads = {};
  final Map<String, LoadPriority> _loadPriorities = {};
  final Set<String> _preloadedAssets = {};

  // Configuration
  static const int maxConcurrentLoads = 3;
  int _currentLoads = 0;
  final Queue<_LoadRequest> _loadQueue = Queue();

  /// Load sprite data with lazy loading and priority
  Future<Map<String, dynamic>> loadSpriteData(
    String assetName, {
    LoadPriority priority = LoadPriority.normal,
  }) async {
    return _monitor.timeAsyncOperation('lazy_load_sprite_data', () async {
      final cacheKey = 'sprite_data_$assetName';

      // Check if already loading
      if (_pendingLoads.containsKey(cacheKey)) {
        return await _pendingLoads[cacheKey] as Map<String, dynamic>;
      }

      // Check if already preloaded
      if (_preloadedAssets.contains(assetName)) {
        return await _cache.loadSpriteData(assetName);
      }

      // Create load request
      final request = _LoadRequest(
        key: cacheKey,
        assetName: assetName,
        priority: priority,
        loadFunction: () => _cache.loadSpriteData(assetName),
      );

      return await _enqueueLoad(request);
    });
  }

  /// Load image with lazy loading
  Future<ui.Image> loadImage(
    String assetName, {
    LoadPriority priority = LoadPriority.normal,
  }) async {
    return _monitor.timeAsyncOperation('lazy_load_image', () async {
      final cacheKey = 'image_$assetName';

      if (_pendingLoads.containsKey(cacheKey)) {
        return await _pendingLoads[cacheKey] as ui.Image;
      }

      final request = _LoadRequest(
        key: cacheKey,
        assetName: assetName,
        priority: priority,
        loadFunction: () => _cache.loadSpriteImage(assetName),
      );

      return await _enqueueLoad(request);
    });
  }

  /// Preload assets for better performance
  Future<void> preloadAssets(
    List<String> assetNames, {
    LoadPriority priority = LoadPriority.background,
  }) async {
    await _monitor.timeAsyncOperation('preload_assets', () async {
      final futures = assetNames.map((assetName) async {
        try {
          await loadSpriteData(assetName, priority: priority);
          _preloadedAssets.add(assetName);
        } catch (e) {
          print("Warning: Failed to preload asset $assetName: $e");
        }
      });

      await Future.wait(futures);
    });
  }

  /// Preload assets based on usage patterns
  Future<void> smartPreload(Map<String, double> assetUsageFrequency) async {
    // Sort assets by usage frequency
    final sortedAssets = assetUsageFrequency.entries.toList()..sort((a, b) => b.value.compareTo(a.value));

    // Preload most frequently used assets first
    final highPriorityAssets = sortedAssets.take(10).map((e) => e.key).toList();

    final normalPriorityAssets = sortedAssets.skip(10).take(20).map((e) => e.key).toList();

    // Load high priority assets first
    await preloadAssets(highPriorityAssets, priority: LoadPriority.high);

    // Then load normal priority assets
    await preloadAssets(normalPriorityAssets, priority: LoadPriority.normal);
  }

  /// Get loading statistics
  Map<String, dynamic> getLoadingStats() {
    return {
      'currentLoads': _currentLoads,
      'queuedLoads': _loadQueue.length,
      'preloadedAssets': _preloadedAssets.length,
      'pendingLoads': _pendingLoads.length,
      'cacheStats': _cache.statistics,
    };
  }

  /// Clear all loading state
  void clear() {
    _pendingLoads.clear();
    _loadPriorities.clear();
    _preloadedAssets.clear();
    _loadQueue.clear();
    _currentLoads = 0;
  }

  /// Enqueue a load request
  Future<T> _enqueueLoad<T>(_LoadRequest request) async {
    // For immediate priority, bypass queue
    if (request.priority == LoadPriority.immediate) {
      return await _executeLoad<T>(request);
    }

    // Add to queue if we're at capacity
    if (_currentLoads >= maxConcurrentLoads) {
      _loadQueue.add(request);
      final completer = Completer<T>();
      request.completer = completer;
      return completer.future;
    }

    // Execute immediately if we have capacity
    return await _executeLoad<T>(request);
  }

  /// Execute a load request
  Future<T> _executeLoad<T>(_LoadRequest request) async {
    _currentLoads++;
    _pendingLoads[request.key] = request.loadFunction();

    try {
      final result = await _pendingLoads[request.key] as T;

      // Complete any queued requests for the same asset
      if (request.completer != null) {
        request.completer!.complete(result);
      }

      return result;
    } catch (e) {
      if (request.completer != null) {
        request.completer!.completeError(e);
      }
      rethrow;
    } finally {
      _pendingLoads.remove(request.key);
      _currentLoads--;

      // Process next item in queue
      _processQueue();
    }
  }

  /// Process the next item in the load queue
  void _processQueue() {
    if (_loadQueue.isEmpty || _currentLoads >= maxConcurrentLoads) {
      return;
    }

    // Sort queue by priority
    final sortedQueue = _loadQueue.toList()..sort((a, b) => a.priority.value.compareTo(b.priority.value));

    if (sortedQueue.isNotEmpty) {
      final nextRequest = sortedQueue.first;
      _loadQueue.remove(nextRequest);

      // Execute the request
      _executeLoad(nextRequest).catchError((e) {
        print("Error processing queued load: $e");
      });
    }
  }
}

/// Internal load request structure
class _LoadRequest {
  final String key;
  final String assetName;
  final LoadPriority priority;
  final Future<dynamic> Function() loadFunction;
  Completer<dynamic>? completer;

  _LoadRequest({
    required this.key,
    required this.assetName,
    required this.priority,
    required this.loadFunction,
  });
}

/// Queue implementation for load requests
class Queue<T> {
  final List<T> _items = [];

  void add(T item) => _items.add(item);
  T removeFirst() => _items.removeAt(0);
  void remove(T item) => _items.remove(item);
  bool get isEmpty => _items.isEmpty;
  bool get isNotEmpty => _items.isNotEmpty;
  int get length => _items.length;
  List<T> toList() => List<T>.from(_items);
  void clear() => _items.clear();
}
