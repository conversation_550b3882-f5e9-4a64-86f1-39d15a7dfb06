import 'dart:ui' as ui;
import 'dart:typed_data';
import 'dart:convert';
import 'package:flutter/services.dart';
import 'package:flutter/material.dart';
import 'package:simsushare_player/models/SimObjects.dart';

/// High-performance asset cache with memory management
class AssetCache {
  static final AssetCache _instance = AssetCache._internal();
  factory AssetCache() => _instance;
  AssetCache._internal();

  // Cache storage
  final Map<String, ui.Image> _imageCache = {};
  final Map<String, Map<String, dynamic>> _spriteMetadataCache = {};
  final Map<String, List<SpriteFrame>> _spriteFramesCache = {};
  final Map<String, Uint8List> _rawDataCache = {};

  // Cache statistics
  int _cacheHits = 0;
  int _cacheMisses = 0;
  int _totalMemoryUsage = 0;

  // Configuration
  static const int maxCacheSize = 100 * 1024 * 1024; // 100MB
  static const int maxImageCacheEntries = 50;
  static const int maxMetadataCacheEntries = 100;

  /// Get cache statistics
  Map<String, dynamic> get statistics => {
        'hits': _cacheHits,
        'misses': _cacheMisses,
        'hitRate': _cacheHits / (_cacheHits + _cacheMisses),
        'memoryUsage': _totalMemoryUsage,
        'imageCacheSize': _imageCache.length,
        'metadataCacheSize': _spriteMetadataCache.length,
      };

  /// Load sprite image with caching
  Future<ui.Image> loadSpriteImage(String assetName) async {
    final cacheKey = 'sprite_image_$assetName';

    if (_imageCache.containsKey(cacheKey)) {
      _cacheHits++;
      return _imageCache[cacheKey]!;
    }

    _cacheMisses++;

    try {
      final imageData = await rootBundle.load("assets/sprites/$assetName-frames-high.png");
      final img = await decodeImageFromList(imageData.buffer.asUint8List());

      // Cache management
      if (_imageCache.length >= maxImageCacheEntries) {
        _evictOldestImage();
      }

      _imageCache[cacheKey] = img;
      _updateMemoryUsage(imageData.lengthInBytes);

      return img;
    } catch (e) {
      throw ArgumentError("Failed to load sprite image for $assetName: $e");
    }
  }

  /// Load sprite metadata with caching
  Future<Map<String, dynamic>> loadSpriteMetadata(String assetName) async {
    final cacheKey = 'sprite_meta_$assetName';

    if (_spriteMetadataCache.containsKey(cacheKey)) {
      _cacheHits++;
      return _spriteMetadataCache[cacheKey]!;
    }

    _cacheMisses++;

    try {
      final metaJson = await rootBundle.loadString("assets/sprites/$assetName-frames-high.json");
      final meta = jsonDecode(metaJson) as Map<String, dynamic>;

      // Cache management
      if (_spriteMetadataCache.length >= maxMetadataCacheEntries) {
        _evictOldestMetadata();
      }

      _spriteMetadataCache[cacheKey] = meta;
      _updateMemoryUsage(metaJson.length * 2); // Approximate memory usage

      return meta;
    } catch (e) {
      throw ArgumentError("Failed to load sprite metadata for $assetName: $e");
    }
  }

  /// Load sprite frames with caching
  Future<List<SpriteFrame>> loadSpriteFrames(String assetName) async {
    final cacheKey = 'sprite_frames_$assetName';

    if (_spriteFramesCache.containsKey(cacheKey)) {
      _cacheHits++;
      return _spriteFramesCache[cacheKey]!;
    }

    _cacheMisses++;

    final meta = await loadSpriteMetadata(assetName);
    final frames = (meta["frames"] as Map<String, dynamic>).values.map((e) {
      return SpriteFrame(
        x: (e["frame"]["x"] as int).toDouble(),
        y: (e["frame"]["y"] as int).toDouble(),
        width: (e["frame"]["w"] as int).toDouble(),
        height: (e["frame"]["h"] as int).toDouble(),
        rotated: e["rotated"] as bool,
      );
    }).toList();

    _spriteFramesCache[cacheKey] = frames;
    _updateMemoryUsage(frames.length * 40); // Approximate memory per frame

    return frames;
  }

  /// Load complete sprite data (optimized single call)
  Future<Map<String, dynamic>> loadSpriteData(String assetName) async {
    final futures = await Future.wait([
      loadSpriteImage(assetName),
      loadSpriteMetadata(assetName),
      loadSpriteFrames(assetName),
    ]);

    return {
      'image': futures[0],
      'meta': futures[1],
      'frames': futures[2],
    };
  }

  /// Load raw file data with caching
  Future<Uint8List> loadRawData(String path) async {
    if (_rawDataCache.containsKey(path)) {
      _cacheHits++;
      return _rawDataCache[path]!;
    }

    _cacheMisses++;

    final data = await rootBundle.load(path);
    final bytes = data.buffer.asUint8List();

    _rawDataCache[path] = bytes;
    _updateMemoryUsage(bytes.length);

    return bytes;
  }

  /// Preload commonly used assets
  Future<void> preloadCommonAssets(List<String> assetNames) async {
    final futures = assetNames.map((assetName) async {
      try {
        await loadSpriteData(assetName);
      } catch (e) {
        // Log error but don't fail the entire preload
        print("Warning: Failed to preload asset $assetName: $e");
      }
    });

    await Future.wait(futures);
  }

  /// Clear specific cache type
  void clearImageCache() {
    _imageCache.clear();
    _recalculateMemoryUsage();
  }

  void clearMetadataCache() {
    _spriteMetadataCache.clear();
    _spriteFramesCache.clear();
    _recalculateMemoryUsage();
  }

  void clearRawDataCache() {
    _rawDataCache.clear();
    _recalculateMemoryUsage();
  }

  /// Clear all caches
  void clearAll() {
    _imageCache.clear();
    _spriteMetadataCache.clear();
    _spriteFramesCache.clear();
    _rawDataCache.clear();
    _totalMemoryUsage = 0;
    _cacheHits = 0;
    _cacheMisses = 0;
  }

  /// Evict oldest image from cache
  void _evictOldestImage() {
    if (_imageCache.isNotEmpty) {
      final oldestKey = _imageCache.keys.first;
      _imageCache.remove(oldestKey);
    }
  }

  /// Evict oldest metadata from cache
  void _evictOldestMetadata() {
    if (_spriteMetadataCache.isNotEmpty) {
      final oldestKey = _spriteMetadataCache.keys.first;
      _spriteMetadataCache.remove(oldestKey);
    }
  }

  /// Update memory usage tracking
  void _updateMemoryUsage(int bytes) {
    _totalMemoryUsage += bytes;

    // If we exceed max cache size, trigger cleanup
    if (_totalMemoryUsage > maxCacheSize) {
      _performMemoryCleanup();
    }
  }

  /// Recalculate total memory usage
  void _recalculateMemoryUsage() {
    _totalMemoryUsage = 0;

    // Approximate memory usage calculation
    _totalMemoryUsage += _imageCache.length * 1024 * 1024; // ~1MB per image
    _totalMemoryUsage += _spriteMetadataCache.length * 10 * 1024; // ~10KB per metadata
    _totalMemoryUsage += _rawDataCache.values.fold(0, (sum, data) => sum + data.length);
  }

  /// Perform memory cleanup when cache is full
  void _performMemoryCleanup() {
    // Remove half of the cached items (LRU-style)
    final imagesToRemove = _imageCache.length ~/ 2;
    final metadataToRemove = _spriteMetadataCache.length ~/ 2;

    for (int i = 0; i < imagesToRemove; i++) {
      _evictOldestImage();
    }

    for (int i = 0; i < metadataToRemove; i++) {
      _evictOldestMetadata();
    }

    _recalculateMemoryUsage();
  }
}
