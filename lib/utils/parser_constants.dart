/// Constants and enums used across parser files
/// This file centralizes all magic numbers, default values, and enums
/// to improve maintainability and reduce duplication.

import 'package:flutter/material.dart';

/// Default values used in parsing
class ParserDefaults {
  static const double defaultScale = 1.0;
  static const double defaultRotation = 0.0;
  static const double defaultOpacity = 1.0;
  static const double defaultFadeInWhen = 0.0;
  static const double defaultFadeInDuration = 0.0;
  static const double defaultFadeOutWhen = 0.0;
  static const double defaultFadeOutDuration = 0.0;
  static const double defaultBlur = 0.0;
  static const int defaultPriority = 1;
  static const int defaultFramerate = 20;
  static const bool defaultMirrorX = false;
  static const bool defaultMirrorY = false;
  static const bool defaultFadeOut = false;
  static const bool defaultTriggerOnce = false;
  static const bool defaultMovable = false;
  static const bool defaultClickToToggle = false;
  static const bool defaultHideOnStart = true;
  
  /// Default offset for navigation cluster positioning
  static const double navClusterOffset = 20.0;
  
  /// Default background color for simulations
  static const String defaultBackgroundColor = "0x222222";
}

/// Timing trigger types for simulation elements
enum TimingTrigger {
  scenario,
  location,
  state,
}

/// Extension to convert timing trigger to/from string values
extension TimingTriggerExtension on TimingTrigger {
  String get value {
    switch (this) {
      case TimingTrigger.scenario:
        return "0";
      case TimingTrigger.location:
        return "1";
      case TimingTrigger.state:
        return "2";
    }
  }
  
  static TimingTrigger fromString(String value) {
    switch (value) {
      case "1":
        return TimingTrigger.location;
      case "2":
        return TimingTrigger.state;
      default:
        return TimingTrigger.scenario;
    }
  }
  
  String get name {
    switch (this) {
      case TimingTrigger.scenario:
        return "scenario";
      case TimingTrigger.location:
        return "location";
      case TimingTrigger.state:
        return "state";
    }
  }
}

/// Element types that can be parsed from XML
enum ElementType {
  csPic,
  csMask,
  csShape,
  locJumper,
  csText,
  csTimer,
  audioClip,
  container,
  person,
  label,
  sprite,
}

/// Extension to convert element type to/from string values
extension ElementTypeExtension on ElementType {
  String get xmlName {
    switch (this) {
      case ElementType.csPic:
        return "CSPic";
      case ElementType.csMask:
        return "CSMask";
      case ElementType.csShape:
        return "CSShape";
      case ElementType.locJumper:
        return "LocJumper";
      case ElementType.csText:
        return "CSText";
      case ElementType.csTimer:
        return "CSTimer";
      case ElementType.audioClip:
        return "AudioClip";
      case ElementType.container:
        return "Container";
      case ElementType.person:
        return "Person";
      case ElementType.label:
        return "Label";
      case ElementType.sprite:
        return "Sprite";
    }
  }
  
  static ElementType? fromString(String value) {
    switch (value) {
      case "CSPic":
        return ElementType.csPic;
      case "CSMask":
        return ElementType.csMask;
      case "CSShape":
        return ElementType.csShape;
      case "LocJumper":
        return ElementType.locJumper;
      case "CSText":
        return ElementType.csText;
      case "CSTimer":
        return ElementType.csTimer;
      case "AudioClip":
        return ElementType.audioClip;
      default:
        return null; // Will be handled by specialized parsers
    }
  }
}

/// Mask type enumeration
enum MaskType {
  showWithin,
  showOutside,
}

/// Shape types for location jumpers and shapes
enum ShapeType {
  rectangle,
  circle,
  arrow,
  triangle,
}

/// Extension for shape type conversion
extension ShapeTypeExtension on ShapeType {
  String get name {
    switch (this) {
      case ShapeType.rectangle:
        return "rectangle";
      case ShapeType.circle:
        return "circle";
      case ShapeType.arrow:
        return "arrow";
      case ShapeType.triangle:
        return "triangle";
    }
  }
  
  static ShapeType fromString(String value) {
    switch (value.toLowerCase()) {
      case "circle":
        return ShapeType.circle;
      case "arrow":
        return ShapeType.arrow;
      case "triangle":
        return ShapeType.triangle;
      default:
        return ShapeType.rectangle;
    }
  }
}

/// Common error messages
class ParserErrors {
  static const String invalidMaskDefinition = "Invalid mask definition format";
  static const String missingElementId = "Element ID is required but missing";
  static const String missingFileName = "File name is required but missing";
  static const String invalidColorValue = "Invalid color value format";
  static const String invalidTimingTrigger = "Invalid timing trigger value";
  static const String elementNotFound = "Referenced element not found";
  static const String invalidShapeNumber = "Invalid shape number";
  static const String xmlParsingError = "Error parsing XML document";
  static const String fileNotFound = "Required file not found";
}

/// Validation constants
class ValidationConstants {
  static const int maxElementNameLength = 100;
  static const int maxTextLength = 1000;
  static const double minScale = 0.1;
  static const double maxScale = 10.0;
  static const double minOpacity = 0.0;
  static const double maxOpacity = 1.0;
  static const int minPriority = 0;
  static const int maxPriority = 100;
}
