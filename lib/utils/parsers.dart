import 'dart:convert';
import 'dart:io';
import 'dart:ui' as ui;
import 'package:flutter/foundation.dart';
import 'package:flutter_colorpicker/flutter_colorpicker.dart';
import 'package:nanoid/nanoid.dart';
import 'package:path/path.dart' as path;
import 'package:collection/collection.dart';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:simsushare_player/flame/Containers/parser.dart';
import 'package:simsushare_player/flame/Labels/parser.dart';
import 'package:simsushare_player/flame/People/parser.dart';
import 'package:simsushare_player/pages/NewScenarioWithPlayer/helpers/helper_methods.dart';
import 'package:simsushare_player/utils/constants.dart';
import 'package:simsushare_player/utils/parser_utilities.dart';
import 'package:simsushare_player/utils/parser_constants.dart' as parser_constants;
import 'package:simsushare_player/utils/parser_base.dart';
import 'package:simsushare_player/utils/element_parsers/mask_parser.dart';
import 'package:xml/xml.dart' as xml;
import 'package:simsushare_player/models/SimObjects.dart';
import 'package:simsushare_player/models/Mask.dart';
import 'package:simsushare_player/models/Simulation.dart';
import 'package:get/get.dart';

// Parse a mask definition into a Mask object
Mask parseMask(String id, String definition, String locationId) {
  final maskParser = MaskParser();
  final defSplit = ParserUtils.parseDefinition(definition);

  if (defSplit.length < 3) {
    throw ArgumentError(parser_constants.ParserErrors.invalidMaskDefinition);
  }

  final showOutside = ParserUtils.parseBool(defSplit[1]);
  final maskType = showOutside ? MaskType.showOutside : MaskType.showWithin;

  final coordinates = ParserUtils.parseCoordinates(defSplit.last);

  return Mask(
    type: maskType,
    coordinates: coordinates,
    id: id,
    name: id,
    locationId: locationId,
  );
}

// Retrieve the sprite ids from a mask definition
List<String> parseMaskSprites(String definition) {
  final maskParser = MaskParser();
  return maskParser.parseMaskSprites(definition);
}

// Retrieve the sim object ids from a mask definition
List<String> parseSimObjectsInMask(String definition) {
  final maskParser = MaskParser();
  return maskParser.parseSimObjectsInMask(definition);
}

String parseTimingTrigger(String trigger) {
  return ParserUtils.parseTimingTrigger(trigger);
}

/// Parse simulation metadata from XML document
SimulationMetadata _parseSimulationMetadata(xml.XmlDocument simdef) {
  final simElement = simdef.getElement("sim");
  if (simElement == null) {
    throw ArgumentError(parser_constants.ParserErrors.xmlParsingError);
  }

  final width = ParserUtils.parseDouble(
    simElement.getAttribute("width"),
    fallback: Get.width.toDouble(),
  );
  final height = ParserUtils.parseDouble(
    simElement.getAttribute("height"),
    fallback: Get.height.toDouble(),
  );

  return SimulationMetadata(
    id: simElement.getAttribute("id") ?? "",
    name: simElement.getAttribute("title") ?? "",
    width: width,
    height: height,
    backgroundColor: simElement.getAttribute("backgroundColor") ?? parser_constants.ParserDefaults.defaultBackgroundColor,
    navClusterX: ParserUtils.calculateNavClusterPosition(
      simElement.getAttribute("NavClusterXPct"),
      width,
      false,
    ),
    navClusterY: ParserUtils.calculateNavClusterPosition(
      simElement.getAttribute("NavClusterYPct"),
      height,
      true,
    ),
  );
}

// Parse simdef into scenario
Future<Scenario> parseSimDef(String simdefXml, Directory dir) async {
  print("Parsing sim def: $simdefXml");
  final simdef = xml.XmlDocument.parse(simdefXml);
  final metadata = _parseSimulationMetadata(simdef);
  final simMasks = <Mask>[];
  print("Sim width: ${metadata.width} x height: ${metadata.height}");
  final simDefNavs = simdef.findAllElements("nav").toList();
  final navs = simDefNavs
      .map((e) => SimulationNavigation(
            direction: e.getAttribute("dir")!,
            from: e.getAttribute("fromID")!,
            to: e.getAttribute("toID")!,
          ))
      .toList();
  final simDefSimFrames = simdef.findAllElements("simFrame").toList();
  // print("Sim frames =========> $simDefSimFrames");
  final stateLocationMapping = <String, String>{};
  for (final simFrame in simDefSimFrames) {
    final state = simFrame.getAttribute("states");
    final location = simFrame.getAttribute("locs");
    if (state != null && location != null) {
      stateLocationMapping[location] = state;
    }
  }
  final simDefStates = simdef.findAllElements("state").toList();
  final states = simDefStates.map((e) => SimulationState(id: e.getAttribute("id")!, name: e.getAttribute("name")!)).toList();
  final simDefLocations = simdef.findAllElements("location").toList();
  final allFileNames = dir.listSync().map((e) => e.path.split("/").last).toList();
  final locations = await Future.wait(simDefLocations.map((location) async {
    final locId = location.getAttribute("id");
    final locName = location.getAttribute("name");
    final brightness = location.getAttribute("brightness");
    final loc = SimulationLocation(
      id: locId ?? "",
      name: locName ?? "",
      color: location.getAttribute("color") ?? "",
      state: stateLocationMapping[locId] ?? states.first.id, // NOTE: might consider using the default state from the variables element
      sprites: [],
      imageBrightness: double.tryParse(brightness ?? "0") ?? 0,
    );
    final elements = location.findElements("element");
    // print("Location Elements: ${elements.map((e) => e.getAttribute("type")).toList()}");
    await Future.forEach(elements, (xml.XmlElement element) async {
      final elementType = element.getAttribute("type");
      switch (elementType) {
        case "CSPic":
          if (element.getAttribute("background") == "true") {
            final fileName = element.getAttribute("file");
            if (fileName == null || fileName.isEmpty) {
              print("File name value not set on a background picture");
              return;
            }
            // loc.image = base64Encode(await File(dir.path + "/" + fileName).readAsBytes());
            loc.image = File(dir.path + "/" + fileName).path;
            // NOTE: scaleX and scaleY should be the same until offset can be calculated from it
            loc.imageScale = double.tryParse(element.getAttribute("scaleX") ?? "1") ?? 1;
            // NOTE: fetching image offset is based on a new attribute added to the simdef file
            final imgOffset = (element.getAttribute("offset") ?? "0x0").split("x");
            loc.imageOffset = Offset(
              double.tryParse(imgOffset[0]) ?? 0,
              double.tryParse(imgOffset[1]) ?? 0,
            );
            // print("Location.Image: ${loc.image}");
          } else {
            final imageName = element.getAttribute("file") /*  ?? element.getAttribute("id") */;
            if (imageName == null) break;
            final fullFileName = allFileNames.firstWhereOrNull((element) => element.startsWith(imageName));
            if (fullFileName == null) break;
            // final imageFile = File(dir.path + "/" + imageName /*  + path.extension(fullFileName) */);
            final imageFile = File(path.join(dir.path, imageName));
            ui.Image img;
            if (kIsWeb) {
              img = await decodeImageFromList(await imageFile.readAsBytes());
            } else {
              img = await decodeImageFromList(await File(imageFile.path).readAsBytes());
            }
            final x = (double.tryParse(element.getAttribute("x") ?? "0") ?? 0) /* / metadata.width */;
            final y = (double.tryParse(element.getAttribute("y") ?? "0") ?? 0) /* / metadata.height */;
            // in,0,0,${hsl.lightness.toPrecision(3)},${hsl.hue},${hsl.saturation},${hsl.alpha.toPrecision(3)},${pic.fadeInWhen},${pic.fadeInDuration},${pic.movable},${pic.mirrorX},${pic.mirrorY},${pic.widthScale},${pic.fadeOut},${pic.fadeOutWhen},${pic.fadeOutDuration},${pic.blur},${pic.trigger},${pic.clickToToggle},0,0";
            final details =
                (simdef.findAllElements("elementVal").firstWhereOrNull((el) => el.getAttribute("id") == element.getAttribute("id")) ?? element)
                    .innerText
                    .split(",");
            final sImg = SimImage(
              id: element.getAttribute("id") ?? "",
              img: img,
              path: imageFile.path,
              x: x,
              y: y,
              width: img.width.toDouble(),
              height: img.height.toDouble(),
              widthScale: double.tryParse(element.getAttribute("scaleX") ?? "1") ?? 1,
              heightScale: double.tryParse(element.getAttribute("scaleY") ?? "1") ?? 1,
              scale: details.length >= 12 ? double.tryParse(details[12]) ?? 1 : 1, // fetched from pinch
              rotation: double.tryParse(element.getAttribute("rotation") ?? "") ?? 0.0,
              opacity: details.length > 6 ? double.tryParse(details[6]) ?? 1 : 1,
              mirrorX: details.length > 10 ? details[10] == "true" : false,
              mirrorY: details.length > 11 ? details[11] == "true" : false,
              filterColor: details.length > 5 ? getColorFromValue(int.parse(details[4])).withOpacity(double.parse(details[5])) : Colors.transparent,
              blur: details.length >= 16 ? double.tryParse(details[16]) ?? 0 : 0,
              to: element.getAttribute("to") ?? "",
              syncVariable: element.getAttribute("syncVar"),
            )
              ..clickToToggle = details.length >= 18 ? details[18] == "true" : false
              ..hideOnStart = details.isNotEmpty ? details[0] == "in" : true
              ..trigger = details.length >= 17 ? parseTimingTrigger(details[17]) : "scenario"
              ..priority = int.parse(element.getAttribute("priority") ?? "1")
              ..triggerOnce = element.getAttribute("triggerOnce") == "true";
            elements.where((element) => element.getAttribute("type") == "CSMask").forEach((maskElement) {
              final linkedSprites = parseSimObjectsInMask(maskElement.innerText);
              if (linkedSprites.contains(element.getAttribute("id"))) {
                sImg.maskIds.add(maskElement.getAttribute("id")!);
              }
            });
            loc.images.add(sImg);
          }
          break;
        case "CSMask":
          // parseSpriteToMask(element.innerText);
          final maskElementId = element.getAttribute("id") ?? "";
          final mask = parseMask(maskElementId + (maskElementId.endsWith("-" + loc.id) ? "" : "-$locId"), element.innerText, loc.id);
          // mask name defaults to ID. Initialized in parseMask
          final maskElementName = element.getAttribute("name");
          if (maskElementName != null) {
            mask.name = maskElementName;
          }
          final spriteIds = parseMaskSprites(element.innerText);
          // NOTE: this might fail if mask is defined in the simdef before the sprite it is masking
          // print("All location sprites: ${loc.sprites.map((e) => e.id).toList()}");
          for (final spriteId in spriteIds) {
            final maskable = [...loc.sprites, ...loc.images, ...loc.shapes, ...loc.containers];
            final sprite = maskable.firstWhereOrNull((element) =>
                element.id == spriteId ||
                (element.id == spriteId + "-$locId") ||
                (spriteId.length >= (loc.id.length + 1) && element.id.startsWith(spriteId.substring(0, loc.id.length + 1))));
            // print("While parsing mask $maskElementId and looking up sprite $spriteId, found sprite: ${sprite?.id}");
            if (sprite != null) {
              sprite.maskIds.add(mask.id);
            }
          }
          final offsetX = double.tryParse(element.getAttribute("x") ?? "0") ?? 0;
          final offsetY = double.tryParse(element.getAttribute("y") ?? "0") ?? 0;
          mask.coordinates =
              mask.coordinates.map((coor) => Coordinate((coor.x + offsetX) / metadata.width, (coor.y + offsetY) / metadata.height)).toList();
          // mask.locationId = loc.id;
          print("================ CSMask: $mask");
          if (simMasks.firstWhereOrNull((element) => element.id == mask.id) == null) {
            simMasks.add(mask);
          }
          break;
        case "CSShape":
          final shapeDetails =
              simdef.findAllElements("elementVal").firstWhere((el) => el.getAttribute("id") == element.getAttribute("id")).innerText.split(",");
          final sShape = SimShape(
            id: element.getAttribute("id") ?? nanoid(10),
            x: (double.tryParse(element.getAttribute("x") ?? "") ?? 1) / metadata.width,
            y: (double.tryParse(element.getAttribute("y") ?? "") ?? 1) / metadata.height,
            widthScale: double.tryParse(element.getAttribute("scaleX") ?? "") ?? 1.0,
            heightScale: double.tryParse(element.getAttribute("scaleY") ?? "") ?? 1.0,
            rotation: double.tryParse(element.getAttribute("rotation") ?? "") ?? 0.0,
            shape: element.getAttribute("shape") ?? "rectangle",
            filterColor: shapeDetails.length > 6
                ? getColorFromValue(int.parse(shapeDetails[4])).withOpacity(double.parse(shapeDetails[5]))
                /* HSLColor.fromAHSL(
                    double.tryParse(shapeDetails.length > 6 ? shapeDetails[6] : "") ?? 1,
                    double.tryParse(shapeDetails.length > 4 ? shapeDetails[4] : "") ?? 0,
                    double.tryParse(shapeDetails.length > 5 ? shapeDetails[5] : "") ?? 0,
                    (double.tryParse(shapeDetails.length > 3 ? shapeDetails[3] : "") ?? 0),
                  ).toColor() */
                : Colors.transparent,
            fadeInWhen: shapeDetails.length > 8 ? double.tryParse(shapeDetails[8]) ?? 0 : 0,
            fadeInDuration: shapeDetails.length > 9 ? double.tryParse(shapeDetails[9]) ?? 0 : 0,
            mirrorX: shapeDetails.length > 10 ? (shapeDetails[10] == "true") : false,
            mirrorY: shapeDetails.length > 11 ? (shapeDetails[11] == "true") : false,
            pinch: shapeDetails.length > 12 ? double.tryParse(shapeDetails[12]) : null,
            fadeOut: shapeDetails.length > 13 ? (shapeDetails[13] == "true") : false,
            fadeOutWhen: shapeDetails.length > 14 ? double.tryParse(shapeDetails[14]) ?? 0 : 0,
            fadeOutDuration: shapeDetails.length > 15 ? double.tryParse(shapeDetails[15]) ?? 0 : 0,
            // timingstart: shapeDetails.length > 16 ? int.tryParse(shapeDetails[16]) ?? 0 : 0,
            blur: shapeDetails.length > 16 ? double.tryParse(shapeDetails[16]) ?? 0 : 0,
            opacity: shapeDetails.length > 6 ? double.tryParse(shapeDetails[6]) ?? 1 : 1,
          )
            ..priority = int.parse(element.getAttribute("priority") ?? "1")
            ..trigger = shapeDetails.length > 17 ? parseTimingTrigger(shapeDetails[17]) : "scenario"
            ..triggerOnce = element.getAttribute("triggerOnce") == "true";
          /* elements.where((element) => element.getAttribute("type") == "CSMask").forEach((maskElement) {
            final linkedSprites = parseSimObjectsInMask(maskElement.innerText);
            if (linkedSprites.contains(element.getAttribute("id"))) {
              sShape.maskIds.add(maskElement.getAttribute("id")!);
            }
          }); */
          loc.shapes.add(sShape);
          break;
        case "LocJumper":
          final jumperDetails = element.innerText.split(",");
          final sJumper = SimLocationJumper(
            id: element.getAttribute("id") ?? nanoid(10),
            name: element.getAttribute("name"),
            to: "",
            shape: "arrow",
            x: (double.tryParse(element.getAttribute("x") ?? "") ?? 1) / metadata.width,
            y: (double.tryParse(element.getAttribute("y") ?? "") ?? 1) / metadata.height,
            widthScale: double.tryParse(element.getAttribute("scaleX") ?? "") ?? 1.0,
            heightScale: double.tryParse(element.getAttribute("scaleY") ?? "") ?? 1.0,
            rotation: double.tryParse(element.getAttribute("rotation") ?? "") ?? 0.0,
          )
            // NOTE: shape will be set in `setLocationJumperFromText` and multiple values here are useless since they will be re-set
            ..filterColor = jumperDetails.length > 6
                ? HSLColor.fromAHSL(
                    double.tryParse(jumperDetails.length > 9 ? jumperDetails[9] : "") ?? 1,
                    double.tryParse(jumperDetails.length > 7 ? jumperDetails[7] : "") ?? 0,
                    double.tryParse(jumperDetails.length > 8 ? jumperDetails[8] : "") ?? 0,
                    (double.tryParse(jumperDetails.length > 6 ? jumperDetails[6] : "") ?? 0),
                  ).toColor()
                : Colors.transparent
            ..fadeInWhen = jumperDetails.length > 19 ? double.tryParse(jumperDetails[19]) ?? 0 : 0
            ..fadeInDuration = jumperDetails.length > 20 ? double.tryParse(jumperDetails[20]) ?? 0 : 0
            ..mirrorX = jumperDetails.length > 10 ? (jumperDetails[10] == "true") : false
            ..mirrorY = jumperDetails.length > 11 ? (jumperDetails[11] == "true") : false
            ..widthScale = jumperDetails.length > 12 ? double.tryParse(jumperDetails[12]) ?? 0.0 : 0.0
            ..fadeOut = jumperDetails.length > 13 ? (jumperDetails[13] == "true") : false
            ..fadeOutWhen = jumperDetails.length > 14 ? double.tryParse(jumperDetails[14]) ?? 0 : 0
            ..fadeOutDuration = jumperDetails.length > 15 ? double.tryParse(jumperDetails[15]) ?? 0 : 0
            ..blur = jumperDetails.length > 16 ? double.tryParse(jumperDetails[16]) ?? 0 : 0
            ..triggerOnce = element.getAttribute("triggerOnce") == "true";
          // TODO: if to value is set to -NEXT- then it should be replaced with the id of the next location
          setLocationJumperFromText(sJumper, jumperDetails);
          loc.jumpers.add(sJumper);
          break;
        case "CSText":
          final sInner = element.innerText.split(",");
          final sText = SimText(
            id: element.getAttribute("id") ?? nanoid(10),
            x: (double.tryParse(element.getAttribute("x") ?? "") ?? 1) / metadata.width,
            y: (double.tryParse(element.getAttribute("y") ?? "") ?? 1) / metadata.height,
            widthScale: double.tryParse(element.getAttribute("scaleX") ?? "") ?? 1.0,
            heightScale: double.tryParse(element.getAttribute("scaleY") ?? "") ?? 1.0,
            rotation: double.tryParse(element.getAttribute("rotation") ?? "") ?? 0.0,
            text: element.getAttribute("text") ?? "",
            backgroundColor: sInner.length >= 2 ? colorFromHex(sInner[1]) ?? Colors.transparent : Colors.transparent,
            fadeInWhen: sInner.length >= 3 ? double.parse(sInner[2]) : 0.0,
            fadeInDuration: sInner.length >= 4 ? double.parse(sInner[3]) : 0.0,
            fadeOut: sInner.length >= 5 && sInner[4] == "true",
            fadeOutWhen: sInner.length >= 6 ? double.parse(sInner[5]) : 0.0,
            fadeOutDuration: sInner.length >= 7 ? double.parse(sInner[6]) : 0.0,
            // timingstart: sInner.length >= 8 ? int.parse(sInner[7]) : 0,
            filterColor: Color(int.tryParse(element.getAttribute("color") ?? "") ?? Colors.black.value),
          )
            ..trigger = sInner.length >= 8 ? parseTimingTrigger(sInner[7]) : "scenario"
            ..priority = int.parse(element.getAttribute("priority") ?? "1")
            ..triggerOnce = element.getAttribute("triggerOnce") == "true";
          // print("sText: $sText");
          loc.texts.add(sText);
          break;
        case "CSTimer":
          // TODO: continue parsing the rest of the parameters
          final elementAttrVal =
              simdef.findAllElements("elementVal").firstWhereOrNull((elementVal) => elementVal.getAttribute("id") == element.getAttribute("id"));
          // final sInner = element.innerText.split(",");
          final sInner = elementAttrVal!.innerText.split(",");
          print("CSTIMER ====> S_INNER $sInner");
          final sTimer = SimTimer(
            id: element.getAttribute("id") ?? "",
            format: timerFormats[int.tryParse(sInner[0]) ?? 0],
            type: timerTypes[int.tryParse(sInner[1]) ?? 0],
            seconds: int.tryParse(sInner[2]) ?? 0,
            x: (double.tryParse(element.getAttribute("x") ?? "") ?? 1),
            y: (double.tryParse(element.getAttribute("y") ?? "") ?? 1),
          )..triggerOnce = element.getAttribute("triggerOnce") == "true";
          loc.timers.add(sTimer);
          break;
        case "AudioClip":
          final sAudio = SimSound(
            id: element.getAttribute("id") ?? "",
            path: path.join(dir.path, element.getAttribute("file")!), // throw an error if file is not set
            x: (double.tryParse(element.getAttribute("x") ?? "") ?? 1) /* / metadata.width */,
            y: (double.tryParse(element.getAttribute("y") ?? "") ?? 1) /* / metadata.height */,
            loop: element.getAttribute("loop") == "true",
            mirrorX: element.getAttribute("mirrorX") == "true",
            mirrorY: element.getAttribute("mirrorY") == "true",
            rotation: double.tryParse(element.getAttribute("rotation") ?? "") ?? 0.0,
            width: double.tryParse(element.getAttribute("scaleX") ?? "") ?? 0.0,
            height: double.tryParse(element.getAttribute("scaleY") ?? "") ?? 0.0,
          )
            ..loop = element.getAttribute("loop") == "true"
            ..priority = int.parse(element.getAttribute("priority") ?? "1")
            ..triggerOnce = element.getAttribute("triggerOnce") == "true";
          loc.sounds.add(sAudio);
          break;
        default: // Parse sprite/label/victim/container

          final elementAttrVal =
              simdef.findAllElements("elementVal").firstWhereOrNull((elementVal) => elementVal.getAttribute("id") == element.getAttribute("id"));

          // check if type equals to any container type and if so, parse as container
          final containerType = containerAssetsMapping.values.firstWhereOrNull((containerType) => elementType!.startsWith(containerType));
          if (containerType != null) {
            final container = parseContainer(
              containerType,
              elementAttrVal?.innerText ?? "",
              element.attributes.fold({}, (previousValue, element) => previousValue..addAll({element.name.toString(): element.value.toString()})),
            );
            elements.where((element) => element.getAttribute("type") == "CSMask").forEach((maskElement) {
              final linkedSprites = parseSimObjectsInMask(maskElement.innerText);
              if (linkedSprites.contains(element.getAttribute("id"))) {
                container.maskIds.add(maskElement.getAttribute("id")!);
              }
            });
            loc.containers.add(container);
            break;
          }
          // check if type starts with Victim and if so, parse as victim
          print("elementType: $elementType");
          final personType = assetToPeopleMapping.values.firstWhereOrNull((personType) => elementType! == "Victim$personType");
          if (personType != null) {
            final person = parsePerson(
              personType,
              elementAttrVal?.innerText ?? "",
              element.attributes.fold({}, (previousValue, element) => previousValue..addAll({element.name.toString(): element.value.toString()})),
            );
            loc.people.add(person);
            break;
          }
          // check if type starts with Label and if so, parse as label
          final labelType = labelToTypeMapping.values.firstWhereOrNull((labelType) => elementType!.endsWith(labelType));
          if (labelType != null) {
            final label = parseLabel(
              labelType,
              elementAttrVal?.innerText ?? "",
              element.attributes.fold({}, (previousValue, element) => previousValue..addAll({element.name.toString(): element.value.toString()})),
            );
            loc.labels.add(label);
            break;
          }

          final meta = jsonDecode(await rootBundle.loadString("assets/sprites/$elementType-frames-high.json"));
          if (elementAttrVal == null) {
            print("Sprite value not found for element: $element");
          }
          final spriteDetails = (elementAttrVal?.innerText ?? "").split(",");
          final frames = (meta["frames"] as Map<String, dynamic>).values.map(
            (e) {
              return SpriteFrame(
                x: (e["frame"]["x"] as int).toDouble(),
                y: (e["frame"]["y"] as int).toDouble(),
                width: (e["frame"]["w"] as int).toDouble(),
                height: (e["frame"]["h"] as int).toDouble(),
                rotated: e["rotated"] as bool,
              );
            },
          ).toList();
          final img = await decodeImageFromList((await rootBundle.load("assets/sprites/$elementType-frames-high.png")).buffer.asUint8List());
          // print("Parser Sim Image: ${img.width}x${img.height}");
          // decodeImageFromList();
          // final img = AssetImage("sprites/$elementType-frames-high.png").load;
          final spriteElementId = element.getAttribute("id") ?? "";
          // print("Completed details: $spriteDetails");
          // print("Sprite details: ${double.tryParse(spriteDetails.length > 6 ? spriteDetails[6] : "") ?? 0}, ${double.tryParse(spriteDetails.length > 4 ? spriteDetails[4] : "") ?? 0}, ${double.tryParse(spriteDetails.length > 5 ? spriteDetails[5] : "") ?? 0}, ${1 + ((double.tryParse(spriteDetails.length > 3 ? spriteDetails[3] : "") ?? 0) / 255)}");
          final spriteToAdd = SimSprite(
            id: spriteElementId + (spriteElementId.endsWith("-$locId") ? "" : "-$locId"),
            name: element.getAttribute("name") ?? spriteElementId,
            img: img,
            frames: frames,
            aseprite: meta as Map<String, dynamic>,
            width: double.parse(meta["meta"]["size"]["w"].toString()),
            height: double.parse(meta["meta"]["size"]["h"].toString()),
            scaleFactor: double.tryParse(meta["meta"]["scale"].toString()) ?? 1,
            scale: double.tryParse(element.getAttribute("scale") ?? "") ?? 1,
            x: (double.tryParse(element.getAttribute("x")!) ?? 1) / metadata.width,
            y: (double.tryParse(element.getAttribute("y")!) ?? 1) / metadata.height,
            widthScale: double.tryParse(element.getAttribute("scaleX") ?? "") ?? 1,
            heightScale: double.tryParse(element.getAttribute("scaleY") ?? "") ?? 1,
            assetName: elementType!,
            rotation: double.tryParse(element.getAttribute("rotation") ?? "") ?? 0.0,
            filterColor: spriteDetails.length >= 5
                ? getColorFromValue(int.parse(spriteDetails[4])).withOpacity(double.parse(spriteDetails[5]))
                : Colors.transparent,
            opacity: spriteDetails.length > 6 ? double.tryParse(spriteDetails[6]) ?? 1 : 1,
            // framerate: spriteDetails.length > 7 ? (double.tryParse(spriteDetails[7]) ?? 20).toInt() : 20,
            framerate: 20,
            speed: spriteDetails.length > 7 ? (double.tryParse(spriteDetails[7]) ?? 20) / 20 : 1,
            fadeInWhen: spriteDetails.length > 8 ? double.tryParse(spriteDetails[8]) ?? 0 : 0,
            fadeInDuration: spriteDetails.length > 9 ? double.tryParse(spriteDetails[9]) ?? 0 : 0,
            mirrorX: spriteDetails.length > 10 ? (spriteDetails[10] == "true") : false,
            mirrorY: spriteDetails.length > 11 ? (spriteDetails[11] == "true") : false,
            pinch: spriteDetails.length > 12 ? double.tryParse(spriteDetails[12]) : null,
            fadeOut: spriteDetails.length > 13 ? (spriteDetails[13] == "true") : false,
            fadeOutWhen: spriteDetails.length > 14 ? double.tryParse(spriteDetails[14]) ?? 0 : 0,
            fadeOutDuration: spriteDetails.length > 15 ? double.tryParse(spriteDetails[15]) ?? 0 : 0,
            // timingstart: spriteDetails.length > 16 ? int.tryParse(spriteDetails[16]) ?? 0 : 0,
          )
            ..trigger = spriteDetails.length > 16 ? parseTimingTrigger(spriteDetails[16]) : "scenario"
            ..priority = int.parse(element.getAttribute("priority") ?? "1")
            ..triggerOnce = element.getAttribute("triggerOnce") == "true";
          // print("Sprite to add: $spriteToAdd");
          elements.where((element) => element.getAttribute("type") == "CSMask").forEach((maskElement) {
            final linkedSprites = parseSimObjectsInMask(maskElement.innerText);
            if (linkedSprites.contains(element.getAttribute("id"))) {
              spriteToAdd.maskIds.add(maskElement.getAttribute("id")!);
            }
          });
          loc.sprites.add(spriteToAdd);
      }
    });
    return loc;
  }));
  final variables = simdef.findAllElements("variable").toList();
  return Scenario(
      name: metadata.name,
      id: metadata.id,
      width: metadata.width,
      height: metadata.height,
      locations: locations,
      currentState: states[0].id,
      states: states,
      initialLocationId: variables.firstWhereOrNull((element) => element.getAttribute("id") == "CURRENT_LOCATION")?.getAttribute("default"),
      initialStateId: variables.firstWhereOrNull((element) => element.getAttribute("id") == "CURRENT_SIM_STATE")?.getAttribute("default"),
      navClusterX: metadata.navClusterX,
      navClusterY: metadata.navClusterY
      // currentState: allStates[0]?.getAttribute("name") ?? "",
      )
    ..navigations.addAll(navs)
    ..masks.addAll(simMasks);
}

String generateSimDef(Scenario simValue, {bool cleanup = false}) {
  final builder = xml.XmlBuilder();
  print("Sim Masks: ${simValue.masks}");
  // builder.processing("sim",
  //     "id=${simValue.id} title=${simValue.name} width=${simValue.width} height=${simValue.height} backgroundColor=0x000000");
  // TODO: handle correctly setting background color value as well as width and height
  builder.element("sim", nest: () {
    builder.attribute("id", simValue.id);
    builder.attribute("title", simValue.name);
    builder.attribute("width", simValue.width.ceil());
    builder.attribute("height", simValue.height.ceil());
    builder.attribute("backgroundColor", "0x222222");
    builder.attribute("NavClusterXPct", simValue.navClusterX != null ? ((simValue.navClusterX! + 20) / simValue.width).toStringAsFixed(3) : null);
    builder.attribute(
        "NavClusterYPct", simValue.navClusterY != null ? (1 - ((simValue.navClusterY! + 20) / simValue.height)).toStringAsFixed(3) : null);
    builder.element("header", nest: () {
      builder.element("summary");
      builder.element("description");
      builder.element("attachments");
      builder.element("keywords");
      builder.element("categories", nest: () {
        builder.attribute("id", simValue.categoryId);
        /* builder.element("category", nest: () {
          builder.attribute("id", simValue.categoryId);
        }); */
      });
      builder.element("history");
    });
    builder.element("varTable", nest: () {
      builder.element("variable", nest: () {
        builder.attribute("id", "CURRENT_SIM_STATE");
        builder.attribute("type", "string");
        builder.attribute("default", simValue.initialStateId ?? simValue.states[0].id);
        builder.attribute("desc", "the current sim state");
      });
      builder.element("variable", nest: () {
        builder.attribute("id", "CURRENT_LOCATION");
        builder.attribute("type", "string");
        builder.attribute("default", simValue.initialLocationId ?? simValue.locations[0].id);
        builder.attribute("desc", "the current sim location");
      });
    });
    builder.element("environ", nest: () {
      builder.element("locations", nest: () {
        for (final loc in simValue.locations) {
          builder.element("location", nest: () {
            builder.attribute("id", loc.id);
            builder.attribute("name", loc.name);
            builder.attribute("brightness", loc.imageBrightness);
            if (loc.color.isNotEmpty) builder.attribute("color", loc.color);
            // Add background
            final locIndex = simValue.locations.indexOf(loc);
            builder.element("element", attributes: {
              "type": "CSPic",
              "id": "background",
              /* 
                NOTE: I have no idea why the commented code was written this way. probably due to something related to us saving things as base64 encoding in web
                      but this still doesn't answer why some of the logic below. I am just going to leave it here for reference 
              */
              // "file": loc.image.isNotEmpty ? "BK$locIndex${isBase64(loc.image) || loc.image.length < 200 ? path.extension(loc.image) : '.png'}" : "",
              "file": loc.image.isNotEmpty ? "BK$locIndex${path.extension(loc.image)}" : "",
              "background": "true",
              "x": (simValue.width / 2).ceil().toString(),
              "y": (simValue.height / 2).ceil().toString(),
              "scaleX": (loc.imageScale).toString(),
              "scaleY": (loc.imageScale).toString(),
              // NOTE: doesn't exist in original simdef but added until i figure out how ti maps to scaleX and scaleY
              "offset": "${loc.imageOffset.dx}x${loc.imageOffset.dy}",
            });
            // add pics
            for (final pic in loc.images) {
              builder.element("element", nest: () {
                builder.attribute("type", "CSPic");
                builder.attribute("id", pic.id);
                builder.attribute("x", (pic.x /* * simValue.width */).toString());
                builder.attribute("y", (pic.y /* * simValue.height */).toString());
                builder.attribute("file", File(pic.path).path.substring(File(pic.path).parent.path.length + 1));
                builder.attribute("scaleX", (pic.widthScale).toString());
                builder.attribute("scaleY", (pic.heightScale).toString());
                builder.attribute("rotation", pic.rotation.toPrecision(3).toString());
                builder.attribute("priority", pic.priority.toString());
                if (pic.to.isNotEmpty) builder.attribute("to", pic.to);
                if (pic.syncVariable != null) {
                  builder.attribute("syncVar", pic.syncVariable);
                }
                // final hsl = HSLColor.fromColor(pic.filterColor);
                // builder.text("out,0,0,${hsl.lightness * 512 - 255},");
                // builder.text(getPicInnerText(pic));
              });
            }
            // add sprites
            // final locMasks = <String>{};
            for (final sp in loc.sprites) {
              // for (final sm in sp.maskIds) {
              //   locMasks.add(sm.endsWith("-${loc.id}") ? sm : "$sm-${loc.id}");
              //   // locMasks.add(sm);
              // }
              builder.element("element", attributes: {
                "type": sp.assetName,
                "id": sp.id,
                "name": sp.name, // NOTE: this is a new attribute that doesn't exist in the old app
                "x": (sp.x * simValue.width).toPrecision(2).toString(),
                "y": (sp.y * simValue.height).toPrecision(2).toString(),
                "scale": sp.scale.toStringAsFixed(2),
                "scaleX": (sp.widthScale /* * sp.scale */).toString(),
                "scaleY": (sp.heightScale /* * sp.scale */).toString(),
                "rotation": sp.rotation.toPrecision(3).toString(),
                "priority": sp.priority.toString(),
                if (sp.movable) "movable": "true",
                if (sp.triggerOnce) "triggerOnce": "true",
              });
            }

            // add location jumper
            for (var jumper in loc.jumpers) {
              builder.element("element", nest: () {
                builder.attribute("type", "LocJumper");
                builder.attribute("id", jumper.id);
                builder.attribute("x", (jumper.x * simValue.width).toString());
                builder.attribute("y", (jumper.y * simValue.height).toString());
                builder.attribute("scaleY", jumper.heightScale.toString());
                // scaleX is not defined since widthScale is set in `getLocationJumperInnerText`
                if (jumper.triggerOnce) builder.attribute("triggerOnce", "true");
                if (jumper.name != null) builder.attribute("name", jumper.name);
                builder.text(getLocationJumperInnerText(jumper));
              });
            }

            // add texts
            for (var txt in loc.texts) {
              builder.element("element", nest: () {
                builder.attribute("type", "CSText");
                builder.attribute("id", txt.id);
                builder.attribute("x", (txt.x * simValue.width).toString());
                builder.attribute("y", (txt.y * simValue.height).toString());
                builder.attribute("scaleX", (txt.widthScale * txt.scale).toString());
                builder.attribute("scaleY", (txt.heightScale * txt.scale).toString());
                builder.attribute("rotation", txt.rotation.toPrecision(3).toString());
                builder.attribute("priority", txt.priority);
                builder.attribute("text", txt.text);
                builder.attribute("color", int.parse(txt.filterColor.toHexString(), radix: 16).toString());
                if (txt.triggerOnce) {
                  builder.attribute("triggerOnce", "true");
                }
                builder.text(getTextInnerText(txt));
              });
            }

            // add shapes
            for (final shape in loc.shapes) {
              builder.element("element", nest: () {
                builder.attribute("type", "CSShape");
                builder.attribute("id", shape.id);
                builder.attribute("x", (shape.x * simValue.width).toString());
                builder.attribute("y", (shape.y * simValue.height).toString());
                builder.attribute("scaleX", (shape.widthScale * shape.scale).toString());
                builder.attribute("scaleY", (shape.heightScale * shape.scale).toString());
                builder.attribute("rotation", shape.rotation.toPrecision(3).toString());
                builder.attribute("priority", shape.priority);
                builder.attribute("shape", shape.shape);
                if (shape.triggerOnce) {
                  builder.attribute("triggerOnce", "true");
                }
                builder.text(getShapeInnerText(shape));
              });
            }

            // add labels
            for (final label in loc.labels) {
              builder.element("element", nest: () {
                builder.attribute("type", "Label${labelToTypeMapping[label.type]}");
                builder.attribute("id", label.id);
                builder.attribute("x", (label.x).toString());
                builder.attribute("y", (label.y).toString());
                builder.attribute("scaleX", (label.widthScale * label.scale).toString());
                builder.attribute("scaleY", (label.heightScale * label.scale).toString());
                builder.attribute("rotation", label.rotation.toPrecision(3).toString());
                if (label.filterColor != Colors.transparent) builder.attribute("color", getColorValue(label.filterColor, withAlpha: true));
                builder.attribute("priority", label.priority);
                if (label.triggerOnce) {
                  builder.attribute("triggerOnce", "true");
                }
              });
            }

            // add containers
            for (final container in loc.containers) {
              /* 
                NOTE: width and height are saved with actual values. Might need to re-add it
                but if we do, the container parser must also be modified to handle such change
              */
              builder.element("element", nest: () {
                builder.attribute("type", container.type.replaceAll(" ", ""));
                builder.attribute("id", container.id);
                builder.attribute("x", (container.x /* * simValue.width */).toString());
                builder.attribute("y", (container.y /* * simValue.height */).toString());
                builder.attribute("scaleX", (container.widthScale).toString());
                builder.attribute("scaleY", (container.heightScale).toString());
                builder.attribute("rotation", container.rotation.toPrecision(3).toString());
                builder.attribute("priority", container.priority);
                if (container.movable) {
                  builder.attribute("movable", "true");
                }
                if (container.triggerOnce) {
                  builder.attribute("triggerOnce", "true");
                }
              });
            }

            // add people
            for (final person in loc.people) {
              builder.element("element", nest: () {
                builder.attribute("type", "Victim${person.type}");
                builder.attribute("id", person.id);
                builder.attribute("name", person.name);
                builder.attribute("x", (person.x).toString());
                builder.attribute("y", (person.y).toString());
                builder.attribute("scaleX", (person.widthScale * person.scale).toString());
                builder.attribute("scaleY", (person.heightScale * person.scale).toString());
                builder.attribute("rotation", person.rotation.toPrecision(3).toString());
                builder.attribute("priority", person.priority);
                builder.attribute("hideOnStart", person.hideOnStart);
                if (person.movable) {
                  builder.attribute("movable", "true");
                }
                if (person.triggerOnce) {
                  builder.attribute("triggerOnce", "true");
                }
                if (person.syncVariable != null) {
                  builder.element("sync", nest: () {
                    builder.attribute("varTableKey", person.syncVariable!);
                    builder.attribute("prop", "visible"); // NOTE: constant. maybe it has a different value in the old app in some cases
                  });
                }
              });
            }

            // add timers
            for (final timer in loc.timers) {
              builder.element("element", nest: () {
                builder.attribute("type", "CSTimer");
                builder.attribute("id", timer.id);
                builder.attribute("x", (timer.x).toString());
                builder.attribute("y", (timer.y).toString());
                builder.attribute("scaleX", (timer.widthScale * timer.scale).toString());
                builder.attribute("scaleY", (timer.heightScale * timer.scale).toString());
                builder.attribute("rotation", timer.rotation.toPrecision(3).toString());
                builder.attribute("priority", timer.priority);
                if (timer.triggerOnce) {
                  builder.attribute("triggerOnce", "true");
                }
              });
            }

            final List<String> soundNames = [];
            // add sounds
            for (final sound in loc.sounds) {
              /* if (File(sound.path).parent.path != File(sound.path).path) {
                print("Sound path: ${sound.path} has parent: ${File(sound.path).parent.path}");
                final originalPath = sound.path;
                final ext = path.extension(sound.path);
                sound.path = nanoid(20) + ext;
                // defer copying audio to sim directory
                getSimSaveDirectory(simValue).then((dir) {
                  print("Copying audio file from $originalPath to ${sound.path}");
                  // Using copy sync to try to take precedence over the next Future
                  File(originalPath).copySync("${dir.path}/${sound.path}");
                }).catchError((err) {
                  print("Error copying audio to sim directory: $err");
                });
              } */
              builder.element("element", nest: () {
                builder.attribute("type", "AudioClip");
                builder.attribute("id", sound.id);
                // builder.attribute("file", sound.path.split("/").last);
                // builder.attribute("file", File(sound.path).path.substring(File(sound.path).parent.path.length + 1));
                builder.attribute("file", path.basename(sound.path));
                builder.attribute("loop", sound.loop.toString());
                builder.attribute("priority", sound.priority.toString());
                builder.attribute("x", sound.x.toString());
                builder.attribute("y", sound.y.toString());
                builder.attribute("scaleX", sound.widthScale.toString());
                builder.attribute("scaleY", sound.heightScale.toString());
                builder.attribute("rotation", sound.rotation.toString());
                if (sound.triggerOnce) {
                  builder.attribute("triggerOnce", "true");
                }
              });
              soundNames.add(sound.path);
            }

            // NOTE: there is a good chance that this is never called
            if (cleanup) {
              // remove remnant sound files
              Future.delayed(const Duration(seconds: 1), () {
                getSimSaveDirectory(simValue).then((dir) {
                  final files = dir.listSync();
                  print("Files in sim directory: ${files.map((e) => e.path).toList().join("\n")}");
                  for (final file in files) {
                    if (file is File && [".m4a", ".mp3", ".wav"].contains(path.extension(file.path)) && !soundNames.contains(file.path)) {
                      print("Deleting remnant sound file: ${file.path}");
                      file.deleteSync();
                    }
                  }
                });
              });
            }

            // NOTE: it is important to save sprites before masks because masks rely on sprites during parsing
            // add masks
            final locMasks = simValue.masks.where((mask) => mask.locationId == loc.id).toList();
            print("Location: ${loc.name} has masks: ${locMasks.map((e) => e.id).toList()} and sprites: ${loc.sprites.map((e) => {
                  "id": e.id,
                  "maskIds": e.maskIds
                }).toList()}");
            for (final mask in locMasks) {
              // final mask = simValue.masks.firstWhere((element) => element.id == maskId);
              // for (final locMask in locMasks) {
              final maskSprites = <SimObject>[];
              final maskable = [...loc.sprites, ...loc.images, ...loc.shapes, ...loc.containers];
              maskSprites.addAll(maskable.where((element) => element.maskIds.contains(mask.id)));
              builder.element("element", nest: () {
                builder.attribute("type", "CSMask");
                // builder.attribute("id", maskId);
                builder.attribute("id", mask.id);
                builder.attribute("name", mask.name);
                // TODO: add mask x and y. correct positioning needs to be checked against the old app
                builder.attribute("x", "0");
                builder.attribute("y", "0");
                List<Coordinate> coords = mask.coordinates;
                if (mask.needsParsing()) {
                  coords = coords.map((c) => Coordinate(c.x * simValue.width, c.y * simValue.height)).toList();
                }
                builder.text(
                  // "true,${locMask.type == MaskType.showOutside},${maskSprites.length},${maskSprites.map((e) => e.id).join(",")},${locMask.coordinates.map((coor) => "${coor.x.toPrecision(2)}c${coor.y.toPrecision(2)}").join("x")}",
                  "true,${mask.type == MaskType.showOutside},${maskSprites.length},${maskSprites.map((e) => e.id).join(",")}${maskSprites.isNotEmpty ? "," : ""}${coords.map((coor) => "${(coor.x /* / simValue.width */).toPrecision(2)}c${(coor.y /* / simValue.height */).toPrecision(2)}").join("x")}",
                );
              });
            }
          });
        }
      });
      builder.element("navigations", nest: () {
        for (final nav in simValue.navigations) {
          builder.element("nav", nest: () {
            builder.attribute("dir", nav.direction);
            builder.attribute("fromID", nav.from);
            builder.attribute("toID", nav.to);
          });
        }
      });
    });
    builder.element("environstates", nest: () {
      builder.element("states", nest: () {
        for (final state in simValue.states) {
          builder.element("state", nest: () {
            builder.attribute("id", state.id);
            builder.attribute("name", state.name);
          });
        }
      });
    });
    builder.element("simFrames", nest: () {
      // TODO: add sim frame. need to check what this is
      for (final loc in simValue.locations) {
        builder.element("simFrame", nest: () {
          builder.attribute("states", loc.state);
          builder.attribute("locs", loc.id);
          for (final sprite in loc.sprites) {
            builder.element("elementVal", nest: () {
              builder.attribute("id", sprite.id);
              // fill inner text
              builder.text(getSpriteInnerText(sprite));
            });
          }
          for (final pic in loc.images) {
            builder.element("elementVal", nest: () {
              builder.attribute("id", pic.id);
              // fill inner text
              builder.text(getPicInnerText(pic));
            });
          }
          for (final text in loc.texts) {
            builder.element("elementVal", nest: () {
              builder.attribute("id", text.id);
              // fill inner text
              builder.text(getTextInnerText(text));
            });
          }
          for (final shape in loc.shapes) {
            builder.element("elementVal", nest: () {
              builder.attribute("id", shape.id);
              // fill inner text
              builder.text(getSimShapeInnerText(shape));
            });
          }
          for (final locJumper in loc.jumpers) {
            builder.element("elementVal", nest: () {
              builder.attribute("id", locJumper.id);
              // fill inner text
              builder.text(getLocationJumperInnerText(locJumper));
            });
          }
          for (final label in loc.labels) {
            builder.element("elementVal", nest: () {
              builder.attribute("id", label.id);
              // fill inner text
              builder.text(getLabelText(label));
            });
          }
          for (final container in loc.containers) {
            builder.element("elementVal", nest: () {
              builder.attribute("id", container.id);
              // fill inner text
              builder.text(getContainerText(container));
            });
          }
          for (final person in loc.people) {
            builder.element("elementVal", nest: () {
              builder.attribute("id", person.id);
              builder.attribute("name", person.name);
              if (person.filterColor != Colors.transparent) builder.attribute("color", getColorValue(person.filterColor, withAlpha: true));
              // fill inner text
              builder.text(getPersonText(person));
            });
          }
          for (final timer in loc.timers) {
            builder.element("elementVal", nest: () {
              builder.attribute("id", timer.id);
              // fill inner text
              builder.text(getTimerInnerText(timer));
            });
          }
        });
      }
    });
    builder.element("plugins", nest: () {
      if (simValue.masks.isNotEmpty) {
        builder.element("plugin", nest: () {
          builder.attribute("lib", "CSMask_v1_lib.swf");
          builder.attribute("assetIDs", "CSMask");
        });
      }
      if (simValue.navigations.isNotEmpty) {
        builder.element("plugin", nest: () {
          builder.attribute("lib", "LocJumper_v1_lib.swf");
          builder.attribute("assetIDs", "LocJumper");
        });
      }
      if (simValue.locations.firstWhereOrNull((loc) => loc.images.isNotEmpty) != null) {
        builder.element("plugin", nest: () {
          builder.attribute("lib", "CSPic_v1_lib.swf");
          builder.attribute("assetIDs", "CSPic");
        });
      }
      if (simValue.locations.firstWhereOrNull((loc) => loc.shapes.isNotEmpty) != null) {
        builder.element("plugin", nest: () {
          builder.attribute("lib", "CSShape_v1_lib.swf");
          builder.attribute("assetIDs", "CSShape");
        });
      }
      if (simValue.locations.firstWhereOrNull((loc) => loc.sounds.isNotEmpty) != null) {
        builder.element("plugin", nest: () {
          builder.attribute("lib", "AudioClip_v1_lib.swf");
          builder.attribute("assetIDs", "AudioClip");
        });
      }
      final allSprites = <String, bool>{};
      for (final loc in simValue.locations) {
        for (var sprite in loc.sprites) {
          allSprites[sprite.assetName] = true;
        }
      }
      for (final assetName in allSprites.keys) {
        builder.element("plugin", nest: () {
          builder.attribute("lib", "${assetName}_v1_lib.swf");
          builder.attribute("assetIDs", assetName);
        });
      }
      final allVictims = <String, bool>{};
      for (final loc in simValue.locations) {
        for (var person in loc.people) {
          allVictims[peopleToAssetMapping[person.type]!] = true;
        }
      }
      for (final assetName in allVictims.keys) {
        builder.element("plugin", nest: () {
          builder.attribute("lib", "${assetName}_v1_lib.swf");
          builder.attribute("assetIDs", assetName);
        });
      }
    });
    builder.element("triggeractions", nest: () {
      builder.element("actions");
      builder.element("triggers");
      builder.element("mappings");
    });
  });
  final doc = builder.buildDocument();
  return doc.toXmlString(pretty: true, indent: "  ");
}

String getTimingTriggerDefValue(SimObject simObj) {
  return ParserUtils.getTimingTriggerDefValue(simObj);
}

String getSpriteInnerText(SimSprite sprite) {
  String timingTrigger = getTimingTriggerDefValue(sprite);
  // out,0,0,brightness,RGB-color,color-opacity,opacity,framerate,speed,fadeInWhen,fadeInDuration,mirrorX,mirrorY,widthScale,fadeOut,fadeOutWhen,fadeOutDuration,timingTrigger
  // NOTE: brightness will always be set to zero because it might need an extra post-processing step to calculate the brightness to the RGB color
  return "out,0,0,1,${getColorValue(sprite.filterColor)},${sprite.filterColor.opacity},${sprite.opacity},${(sprite.framerate * sprite.speed).toPrecision(2)},${sprite.fadeInWhen},${sprite.fadeInDuration},${sprite.mirrorX},${sprite.mirrorY},${sprite.widthScale},${sprite.fadeOut},${sprite.fadeOutWhen},${sprite.fadeOutDuration},$timingTrigger";
}

String getLocationJumperInnerText(SimLocationJumper jumper) {
  String shapeNumber = "0";
  switch (jumper.shape) {
    case "arrow":
    case "arrow-1":
      shapeNumber = "0"; // NOTE: there are 4 variants of arrow from 1 to 4. We only have arrow-1
      break;
    case "arrow-2":
      shapeNumber = "1";
      break;
    case "arrow-3":
      shapeNumber = "2";
      break;
    case "arrow-4":
      shapeNumber = "3";
      break;
    case "rectangle":
      shapeNumber = "4";
      break;
    case "square":
      shapeNumber = "5";
      break;
    case "circle":
      shapeNumber = "6";
      break;
    case "triangle": // NOTE: this is not supported in old players
      shapeNumber = "7";
      break;
    case "rounded-rectangle": // NEW SHAPE
      shapeNumber = "8";
      break;
    default:
      throw "Invalid shape: ${jumper.shape}";
  }
  final hsl = HSLColor.fromColor(jumper.filterColor);
  /* starting ${jumper.fadeInWhen + jumper.fadeInDuration > 0}, these are new add-ons */
  /* ${jumper.fadeInWhen + jumper.fadeInDuration > 0} checks if either is larger than 0 */
  return "in,${jumper.to},$shapeNumber,${jumper.delay},${(jumper.delay > 0).toString()},${jumper.clickable.toString()},${hsl.lightness.toPrecision(3)},${hsl.hue},${hsl.saturation},${hsl.alpha.toPrecision(3)},${jumper.mirrorY.toString()},${jumper.mirrorX.toString()},${(jumper.widthScale)},${jumper.fadeOut},${jumper.fadeOutWhen},${jumper.fadeOutDuration},${jumper.blur},${getTimingTriggerDefValue(jumper)},${jumper.fadeInWhen + jumper.fadeInDuration > 0},${jumper.fadeInWhen},${jumper.fadeInDuration}";
}

String getShapeInnerText(SimShape shape) {
  final hsl = HSLColor.fromColor(shape.filterColor);
  return "out,0,0,${hsl.lightness.toPrecision(3)},${hsl.hue},${hsl.saturation},${hsl.alpha.toPrecision(3)},${shape.shape},${shape.fadeInWhen},${shape.fadeInDuration},${shape.mirrorX},${shape.mirrorY},${shape.widthScale},${shape.fadeOut},${shape.fadeOutWhen},${shape.fadeOutDuration},${shape.blur},${getTimingTriggerDefValue(shape)}";
}

String getPicInnerText(SimImage pic) {
  // final hsl = HSLColor.fromColor(pic.filterColor);
  // TODO: missing implementation for last 3 values
  // TODO: check if default value of "out" (index 0) is responsible for showing the image on start
  return "${pic.hideOnStart ? "in" : "out"},0,0,1,${getColorValue(pic.filterColor)},${pic.filterColor.opacity},${pic.opacity},${pic.fadeInWhen},${pic.fadeInDuration},${pic.movable},${pic.mirrorX},${pic.mirrorY},${pic.scale.toPrecision(3)},${pic.fadeOut},${pic.fadeOutWhen},${pic.fadeOutDuration},${pic.blur},${getTimingTriggerDefValue(pic)},${pic.clickToToggle},0,0";
}

String getSimShapeInnerText(SimShape shape) {
  // final hsl = HSLColor.fromColor(shape.filterColor);
  // return "out,0,0,${hsl.lightness.toPrecision(3)},${hsl.hue},${hsl.saturation},${hsl.alpha.toPrecision(3)},${shape.fadeInWhen},${shape.fadeInDuration},null,${shape.mirrorX},${shape.mirrorY},${shape.widthScale},${shape.fadeOut},${shape.fadeOutWhen},${shape.fadeOutDuration},${shape.blur},${shape.trigger},0,0,0";
  return "out,0,0,1,${getColorValue(shape.filterColor)},${shape.filterColor.opacity},${shape.opacity},${shape.fadeInWhen},${shape.fadeInDuration},null,${shape.mirrorX},${shape.mirrorY},${shape.widthScale},${shape.fadeOut},${shape.fadeOutWhen},${shape.fadeOutDuration},${shape.blur},${shape.trigger},0,0,0";
}

String getTextInnerText(SimText txt) {
  // return "${Uri.encodeFull(txt.text)},${txt.backgroundColor},${txt.fadeInWhen},${txt.fadeInDuration},${txt.fadeOut.toString()},${txt.fadeOutWhen},${txt.fadeOutDuration},${txt.trigger}";
  return "${Uri.encodeFull(txt.text)},${txt.backgroundColor.toHexString()},${txt.fadeInWhen},${txt.fadeInDuration},${txt.fadeOut.toString()},${txt.fadeOutWhen},${txt.fadeOutDuration},${getTimingTriggerDefValue(txt)}";
}

String getTimerInnerText(SimTimer timer) {
  // NOTE: missing time of day attribute at the end of the string
  return "${timerFormats.indexOf(timer.format)},${timerTypes.indexOf(timer.type)},${timer.startingSecond},${timer.filterColor.value},${timer.fadeInWhen},${timer.fadeInDuration},${timer.fadeOut.toString()},${timer.fadeOutWhen},${timer.fadeOutDuration},${timer.trigger}";
}

void setLocationJumperFromText(SimLocationJumper jumper, List<String> details) {
  String getShape(String shapeNumber) {
    switch (shapeNumber) {
      case "0":
        return "arrow-1";
      case "1":
        return "arrow-2";
      case "2":
        return "arrow-3";
      case "3":
        return "arrow-4";
      case "4":
        return "rectangle";
      case "5":
        return "square";
      case "6":
        return "circle";
      case "7":
        return "triangle";
      case "8": // NEW SHAPE
        return "rounded-rectangle";
    }
    // print("INVALID SHAPE NUMBER: $shapeNumber");
    throw "Invalid shape number: $shapeNumber";
    return "arrow-1";
  }

  if (details.length >= 2) {
    jumper.to = details[1];
  }
  if (details.length >= 3) {
    jumper.shape = getShape(details[2]);
  }
  // split this "in,${jumper.to},$shapeNumber,${jumper.delay},${(jumper.delay > 0).toString()},${jumper.clickable.toString()},${hsl.lightness.toPrecision(3)},${hsl.hue},${hsl.saturation},${hsl.alpha.toPrecision(3)},${jumper.mirrorY.toString()},${jumper.mirrorX.toString()},${(jumper.widthScale ?? 0)}";
  // and convert the details based on the index starting from index 7
  if (details.length > 3) {
    jumper.delay = int.parse(details[3]);
  }
  if (details.length > 5) {
    jumper.clickable = details[5] == "true";
  }
  if (details.length > 6) {
    jumper.filterColor =
        HSLColor.fromAHSL(double.parse(details[9]), double.parse(details[7]), double.parse(details[8]), double.parse(details[6])).toColor();
  }
  if (details.length >= 11) {
    jumper.mirrorY = details[10] == "true";
  }
  if (details.length >= 12) {
    jumper.mirrorX = details[11] == "true";
  }
  if (details.length >= 13) {
    jumper.widthScale = double.parse(details[12]);
  }
}

double getInitialSize() {
  return ParserUtils.getInitialSize();
}

int getColorValue(Color c, {bool withAlpha = false}) {
  return ColorUtils.getColorValue(c, withAlpha: withAlpha);
}

Color getColorFromValue(int value, {bool withAlpha = false}) {
  return ColorUtils.getColorFromValue(value, withAlpha: withAlpha);
}
