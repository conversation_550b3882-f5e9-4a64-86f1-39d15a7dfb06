import 'dart:io';
import 'package:flutter_colorpicker/flutter_colorpicker.dart';
import 'package:path/path.dart' as path;

import 'package:flutter/material.dart';
import 'package:simsushare_player/flame/Containers/parser.dart';
import 'package:simsushare_player/flame/Labels/parser.dart';
import 'package:simsushare_player/flame/People/parser.dart';
import 'package:simsushare_player/pages/NewScenarioWithPlayer/helpers/helper_methods.dart';
import 'package:simsushare_player/utils/constants.dart';
import 'package:simsushare_player/utils/parser_utilities.dart';
import 'package:simsushare_player/utils/parser_constants.dart' as parser_constants;
import 'package:simsushare_player/utils/parser_base.dart';
import 'package:simsushare_player/utils/element_parsers/mask_parser.dart';
import 'package:simsushare_player/utils/element_parsers/element_parser_factory.dart';
import 'package:xml/xml.dart' as xml;
import 'package:simsushare_player/models/SimObjects.dart';
import 'package:simsushare_player/models/Mask.dart';
import 'package:simsushare_player/models/Simulation.dart';
import 'package:get/get.dart';

// Parse a mask definition into a Mask object
Mask parseMask(String id, String definition, String locationId) {
  final defSplit = ParserUtils.parseDefinition(definition);

  if (defSplit.length < 3) {
    throw ArgumentError(parser_constants.ParserErrors.invalidMaskDefinition);
  }

  final showOutside = ParserUtils.parseBool(defSplit[1]);
  final maskType = showOutside ? MaskType.showOutside : MaskType.showWithin;

  final coordinates = ParserUtils.parseCoordinates(defSplit.last);

  return Mask(
    type: maskType,
    coordinates: coordinates,
    id: id,
    name: id,
    locationId: locationId,
  );
}

// Retrieve the sprite ids from a mask definition
List<String> parseMaskSprites(String definition) {
  final maskParser = MaskParser();
  return maskParser.parseMaskSprites(definition);
}

// Retrieve the sim object ids from a mask definition
List<String> parseSimObjectsInMask(String definition) {
  final maskParser = MaskParser();
  return maskParser.parseSimObjectsInMask(definition);
}

String parseTimingTrigger(String trigger) {
  return ParserUtils.parseTimingTrigger(trigger);
}

/// Parse navigation elements from XML document
List<SimulationNavigation> _parseNavigations(xml.XmlDocument simdef) {
  final simDefNavs = simdef.findAllElements("nav").toList();
  return simDefNavs
      .map((e) => SimulationNavigation(
            direction: e.getAttribute("dir") ?? "",
            from: e.getAttribute("fromID") ?? "",
            to: e.getAttribute("toID") ?? "",
          ))
      .toList();
}

/// Parse state-location mapping from simFrame elements
Map<String, String> _parseStateLocationMapping(xml.XmlDocument simdef) {
  final simDefSimFrames = simdef.findAllElements("simFrame").toList();
  final stateLocationMapping = <String, String>{};

  for (final simFrame in simDefSimFrames) {
    final state = simFrame.getAttribute("states");
    final location = simFrame.getAttribute("locs");
    if (state != null && location != null) {
      stateLocationMapping[location] = state;
    }
  }

  return stateLocationMapping;
}

/// Parse simulation states from XML document
List<SimulationState> _parseStates(xml.XmlDocument simdef) {
  final simDefStates = simdef.findAllElements("state").toList();
  return simDefStates
      .map((e) => SimulationState(
            id: e.getAttribute("id") ?? "",
            name: e.getAttribute("name") ?? "",
          ))
      .toList();
}

/// Parse simulation variables from XML document
Map<String, String> _parseVariables(xml.XmlDocument simdef) {
  final variables = simdef.findAllElements("variable").toList();
  final variableMap = <String, String>{};

  for (final variable in variables) {
    final id = variable.getAttribute("id");
    final defaultValue = variable.getAttribute("default");
    if (id != null && defaultValue != null) {
      variableMap[id] = defaultValue;
    }
  }

  return variableMap;
}

/// Parse all locations from XML document
Future<List<SimulationLocation>> _parseLocations(
  xml.XmlDocument simdef,
  Directory dir,
  List<String> allFileNames,
  SimulationMetadata metadata,
  Map<String, String> stateLocationMapping,
  List<SimulationState> states,
  List<Mask> simMasks,
) async {
  final simDefLocations = simdef.findAllElements("location").toList();

  return await Future.wait(simDefLocations.map((location) async {
    final locId = location.getAttribute("id");
    final locName = location.getAttribute("name");
    final brightness = location.getAttribute("brightness");

    final loc = SimulationLocation(
      id: locId ?? "",
      name: locName ?? "",
      color: location.getAttribute("color") ?? "",
      state: stateLocationMapping[locId] ?? states.first.id,
      sprites: [],
      imageBrightness: double.tryParse(brightness ?? "0") ?? 0,
    );

    final elements = location.findElements("element");
    await _parseLocationElements(elements, loc, simdef, dir, allFileNames, metadata, simMasks);

    return loc;
  }));
}

/// Parse all elements within a location
Future<void> _parseLocationElements(
  Iterable<xml.XmlElement> elements,
  SimulationLocation location,
  xml.XmlDocument simdef,
  Directory dir,
  List<String> allFileNames,
  SimulationMetadata metadata,
  List<Mask> simMasks,
) async {
  final context = {
    "locationId": location.id,
    "directory": dir,
    "allFileNames": allFileNames,
    "metadata": metadata,
    "simMasks": simMasks,
  };

  for (final element in elements) {
    try {
      final parsedElement = await ElementParserFactory.parseElement(element, simdef, context);
      if (parsedElement != null) {
        _addElementToLocation(parsedElement, location);
      }
    } catch (e) {
      print("Error parsing element ${element.getAttribute("id")}: $e");
    }
  }
}

/// Add parsed element to appropriate location collection
void _addElementToLocation(SimObject element, SimulationLocation location) {
  if (element is SimImage) {
    location.images.add(element);
  } else if (element is SimShape) {
    location.shapes.add(element);
  } else if (element is SimLocationJumper) {
    location.jumpers.add(element);
  } else if (element is SimText) {
    location.texts.add(element);
  } else if (element is SimTimer) {
    location.timers.add(element);
  } else if (element is SimSound) {
    location.sounds.add(element);
  } else if (element is SimContainer) {
    location.containers.add(element);
  } else if (element is SimPerson) {
    location.people.add(element);
  } else if (element is SimLabel) {
    location.labels.add(element);
  } else if (element is SimSprite) {
    location.sprites.add(element);
  }
}

/// Parse simulation metadata from XML document
SimulationMetadata _parseSimulationMetadata(xml.XmlDocument simdef) {
  final simElement = simdef.getElement("sim");
  if (simElement == null) {
    throw ArgumentError(parser_constants.ParserErrors.xmlParsingError);
  }

  final width = ParserUtils.parseDouble(
    simElement.getAttribute("width"),
    fallback: Get.width.toDouble(),
  );
  final height = ParserUtils.parseDouble(
    simElement.getAttribute("height"),
    fallback: Get.height.toDouble(),
  );

  return SimulationMetadata(
    id: simElement.getAttribute("id") ?? "",
    name: simElement.getAttribute("title") ?? "",
    width: width,
    height: height,
    backgroundColor: simElement.getAttribute("backgroundColor") ?? parser_constants.ParserDefaults.defaultBackgroundColor,
    navClusterX: ParserUtils.calculateNavClusterPosition(
      simElement.getAttribute("NavClusterXPct"),
      width,
      false,
    ),
    navClusterY: ParserUtils.calculateNavClusterPosition(
      simElement.getAttribute("NavClusterYPct"),
      height,
      true,
    ),
  );
}

/// Main orchestration function for parsing SimDef XML into Scenario
Future<Scenario> parseSimDef(String simdefXml, Directory dir) async {
  print("Parsing sim def: $simdefXml");

  // Initialize element parser factory
  ElementParserFactory.initialize();

  final simdef = xml.XmlDocument.parse(simdefXml);
  return await _orchestrateSimDefParsing(simdef, dir);
}

/// Orchestrate the complete parsing process
Future<Scenario> _orchestrateSimDefParsing(xml.XmlDocument simdef, Directory dir) async {
  // Parse core simulation metadata
  final metadata = _parseSimulationMetadata(simdef);
  print("Sim width: ${metadata.width} x height: ${metadata.height}");

  // Parse simulation structure components
  final navs = _parseNavigations(simdef);
  final stateLocationMapping = _parseStateLocationMapping(simdef);
  final states = _parseStates(simdef);
  final variables = _parseVariables(simdef);

  // Prepare context for location parsing
  final allFileNames = _extractFileNames(dir);
  final simMasks = <Mask>[];

  // Parse all locations with their elements
  final locations = await _parseLocations(simdef, dir, allFileNames, metadata, stateLocationMapping, states, simMasks);

  // Build and return the complete scenario
  return _buildScenario(metadata, locations, states, navs, variables, simMasks);
}

/// Extract file names from directory for element parsing
List<String> _extractFileNames(Directory dir) {
  return dir.listSync().map((e) => e.path.split("/").last).toList();
}

/// Build the final Scenario object from parsed components
Scenario _buildScenario(
  SimulationMetadata metadata,
  List<SimulationLocation> locations,
  List<SimulationState> states,
  List<SimulationNavigation> navs,
  Map<String, String> variables,
  List<Mask> simMasks,
) {
  return Scenario(
    name: metadata.name,
    id: metadata.id,
    width: metadata.width,
    height: metadata.height,
    locations: locations,
    currentState: states.isNotEmpty ? states[0].id : "",
    states: states,
    initialLocationId: variables["CURRENT_LOCATION"],
    initialStateId: variables["CURRENT_SIM_STATE"],
    navClusterX: metadata.navClusterX,
    navClusterY: metadata.navClusterY,
  )
    ..navigations.addAll(navs)
    ..masks.addAll(simMasks);
}

/// Generate SimDef XML from Scenario object
String generateSimDef(Scenario simValue, {bool cleanup = false}) {
  print("Sim Masks: ${simValue.masks}");
  final builder = xml.XmlBuilder();

  builder.element("sim", nest: () {
    _generateSimAttributes(builder, simValue);
    _generateSimHeader(builder, simValue);
    _generateVariableTable(builder, simValue);
    _generateEnvironment(builder, simValue, cleanup);
    _generateEnvironmentStates(builder, simValue);
    _generateSimFrames(builder, simValue);
    _generatePlugins(builder, simValue);
    _generateTriggerActions(builder);
  });

  final doc = builder.buildDocument();
  return doc.toXmlString(pretty: true, indent: "  ");
}

/// Generate sim element attributes
void _generateSimAttributes(xml.XmlBuilder builder, Scenario simValue) {
  builder.attribute("id", simValue.id);
  builder.attribute("title", simValue.name);
  builder.attribute("width", simValue.width.ceil());
  builder.attribute("height", simValue.height.ceil());
  builder.attribute("backgroundColor", "0x222222");

  if (simValue.navClusterX != null) {
    builder.attribute("NavClusterXPct",
        ((simValue.navClusterX! + 20) / simValue.width).toStringAsFixed(3));
  }

  if (simValue.navClusterY != null) {
    builder.attribute("NavClusterYPct",
        (1 - ((simValue.navClusterY! + 20) / simValue.height)).toStringAsFixed(3));
  }
}

/// Generate sim header section
void _generateSimHeader(xml.XmlBuilder builder, Scenario simValue) {
  builder.element("header", nest: () {
    builder.element("summary");
    builder.element("description");
    builder.element("attachments");
    builder.element("keywords");
    builder.element("categories", nest: () {
      builder.attribute("id", simValue.categoryId);
    });
    builder.element("history");
  });
}

/// Generate variable table section
void _generateVariableTable(xml.XmlBuilder builder, Scenario simValue) {
  builder.element("varTable", nest: () {
    builder.element("variable", nest: () {
      builder.attribute("id", "CURRENT_SIM_STATE");
      builder.attribute("type", "string");
      builder.attribute("default", simValue.initialStateId ?? simValue.states[0].id);
      builder.attribute("desc", "the current sim state");
    });
    builder.element("variable", nest: () {
      builder.attribute("id", "CURRENT_LOCATION");
      builder.attribute("type", "string");
      builder.attribute("default", simValue.initialLocationId ?? simValue.locations[0].id);
      builder.attribute("desc", "the current sim location");
    });
  });
}

/// Generate environment section with locations and navigations
void _generateEnvironment(xml.XmlBuilder builder, Scenario simValue, bool cleanup) {
  builder.element("environ", nest: () {
    _generateLocations(builder, simValue, cleanup);
    _generateNavigations(builder, simValue);
  });
}

/// Generate all locations with their elements
void _generateLocations(xml.XmlBuilder builder, Scenario simValue, bool cleanup) {
  builder.element("locations", nest: () {
    for (final loc in simValue.locations) {
      _generateSingleLocation(builder, simValue, loc, cleanup);
    }
  });
}

/// Generate a single location with all its elements
void _generateSingleLocation(xml.XmlBuilder builder, Scenario simValue, SimulationLocation loc, bool cleanup) {
  builder.element("location", nest: () {
    builder.attribute("id", loc.id);
    builder.attribute("name", loc.name);
    builder.attribute("brightness", loc.imageBrightness);
    if (loc.color.isNotEmpty) builder.attribute("color", loc.color);

    // Generate location elements
    _generateLocationBackground(builder, simValue, loc);
    _generateLocationImages(builder, loc);
    _generateLocationSprites(builder, simValue, loc);
    _generateLocationJumpers(builder, simValue, loc);
    _generateLocationTexts(builder, simValue, loc);
    _generateLocationShapes(builder, simValue, loc);
    _generateLocationLabels(builder, loc);
    _generateLocationContainers(builder, loc);
    _generateLocationPeople(builder, loc);
    _generateLocationTimers(builder, loc);
    _generateLocationSounds(builder, simValue, loc, cleanup);
    _generateLocationMasks(builder, simValue, loc);
  });
}

/// Generate location background element
void _generateLocationBackground(xml.XmlBuilder builder, Scenario simValue, SimulationLocation loc) {
  final locIndex = simValue.locations.indexOf(loc);
  builder.element("element", attributes: {
    "type": "CSPic",
    "id": "background",
    "file": loc.image.isNotEmpty ? "BK$locIndex${path.extension(loc.image)}" : "",
    "background": "true",
    "x": (simValue.width / 2).ceil().toString(),
    "y": (simValue.height / 2).ceil().toString(),
    "scaleX": (loc.imageScale).toString(),
    "scaleY": (loc.imageScale).toString(),
    "offset": "${loc.imageOffset.dx}x${loc.imageOffset.dy}",
  });
}

/// Generate location images
void _generateLocationImages(xml.XmlBuilder builder, SimulationLocation loc) {
  for (final pic in loc.images) {
    builder.element("element", nest: () {
      builder.attribute("type", "CSPic");
      builder.attribute("id", pic.id);
      builder.attribute("x", (pic.x).toString());
      builder.attribute("y", (pic.y).toString());
      builder.attribute("file", File(pic.path).path.substring(File(pic.path).parent.path.length + 1));
      builder.attribute("scaleX", (pic.widthScale).toString());
      builder.attribute("scaleY", (pic.heightScale).toString());
      builder.attribute("rotation", pic.rotation.toPrecision(3).toString());
      builder.attribute("priority", pic.priority.toString());
      if (pic.to.isNotEmpty) builder.attribute("to", pic.to);
      if (pic.syncVariable != null) {
        builder.attribute("syncVar", pic.syncVariable);
      }
    });
  }
}

/// Generate location sprites
void _generateLocationSprites(xml.XmlBuilder builder, Scenario simValue, SimulationLocation loc) {
  for (final sp in loc.sprites) {
              // for (final sm in sp.maskIds) {
              //   locMasks.add(sm.endsWith("-${loc.id}") ? sm : "$sm-${loc.id}");
              //   // locMasks.add(sm);
              // }
              builder.element("element", attributes: {
                "type": sp.assetName,
                "id": sp.id,
                "name": sp.name, // NOTE: this is a new attribute that doesn't exist in the old app
                "x": (sp.x * simValue.width).toPrecision(2).toString(),
                "y": (sp.y * simValue.height).toPrecision(2).toString(),
                "scale": sp.scale.toStringAsFixed(2),
                "scaleX": (sp.widthScale /* * sp.scale */).toString(),
                "scaleY": (sp.heightScale /* * sp.scale */).toString(),
                "rotation": sp.rotation.toPrecision(3).toString(),
                "priority": sp.priority.toString(),
                if (sp.movable) "movable": "true",
                if (sp.triggerOnce) "triggerOnce": "true",
              });
            }

            // add location jumper
            for (var jumper in loc.jumpers) {
              builder.element("element", nest: () {
                builder.attribute("type", "LocJumper");
                builder.attribute("id", jumper.id);
                builder.attribute("x", (jumper.x * simValue.width).toString());
                builder.attribute("y", (jumper.y * simValue.height).toString());
                builder.attribute("scaleY", jumper.heightScale.toString());
                // scaleX is not defined since widthScale is set in `getLocationJumperInnerText`
                if (jumper.triggerOnce) builder.attribute("triggerOnce", "true");
                if (jumper.name != null) builder.attribute("name", jumper.name);
                builder.text(getLocationJumperInnerText(jumper));
              });
            }

            // add texts
            for (var txt in loc.texts) {
              builder.element("element", nest: () {
                builder.attribute("type", "CSText");
                builder.attribute("id", txt.id);
                builder.attribute("x", (txt.x * simValue.width).toString());
                builder.attribute("y", (txt.y * simValue.height).toString());
                builder.attribute("scaleX", (txt.widthScale * txt.scale).toString());
                builder.attribute("scaleY", (txt.heightScale * txt.scale).toString());
                builder.attribute("rotation", txt.rotation.toPrecision(3).toString());
                builder.attribute("priority", txt.priority);
                builder.attribute("text", txt.text);
                builder.attribute("color", int.parse(txt.filterColor.toHexString(), radix: 16).toString());
                if (txt.triggerOnce) {
                  builder.attribute("triggerOnce", "true");
                }
                builder.text(getTextInnerText(txt));
              });
            }

            // add shapes
            for (final shape in loc.shapes) {
              builder.element("element", nest: () {
                builder.attribute("type", "CSShape");
                builder.attribute("id", shape.id);
                builder.attribute("x", (shape.x * simValue.width).toString());
                builder.attribute("y", (shape.y * simValue.height).toString());
                builder.attribute("scaleX", (shape.widthScale * shape.scale).toString());
                builder.attribute("scaleY", (shape.heightScale * shape.scale).toString());
                builder.attribute("rotation", shape.rotation.toPrecision(3).toString());
                builder.attribute("priority", shape.priority);
                builder.attribute("shape", shape.shape);
                if (shape.triggerOnce) {
                  builder.attribute("triggerOnce", "true");
                }
                builder.text(getShapeInnerText(shape));
              });
            }

            // add labels
            for (final label in loc.labels) {
              builder.element("element", nest: () {
                builder.attribute("type", "Label${labelToTypeMapping[label.type]}");
                builder.attribute("id", label.id);
                builder.attribute("x", (label.x).toString());
                builder.attribute("y", (label.y).toString());
                builder.attribute("scaleX", (label.widthScale * label.scale).toString());
                builder.attribute("scaleY", (label.heightScale * label.scale).toString());
                builder.attribute("rotation", label.rotation.toPrecision(3).toString());
                if (label.filterColor != Colors.transparent) builder.attribute("color", getColorValue(label.filterColor, withAlpha: true));
                builder.attribute("priority", label.priority);
                if (label.triggerOnce) {
                  builder.attribute("triggerOnce", "true");
                }
              });
            }

            // add containers
            for (final container in loc.containers) {
              /* 
                NOTE: width and height are saved with actual values. Might need to re-add it
                but if we do, the container parser must also be modified to handle such change
              */
              builder.element("element", nest: () {
                builder.attribute("type", container.type.replaceAll(" ", ""));
                builder.attribute("id", container.id);
                builder.attribute("x", (container.x /* * simValue.width */).toString());
                builder.attribute("y", (container.y /* * simValue.height */).toString());
                builder.attribute("scaleX", (container.widthScale).toString());
                builder.attribute("scaleY", (container.heightScale).toString());
                builder.attribute("rotation", container.rotation.toPrecision(3).toString());
                builder.attribute("priority", container.priority);
                if (container.movable) {
                  builder.attribute("movable", "true");
                }
                if (container.triggerOnce) {
                  builder.attribute("triggerOnce", "true");
                }
              });
            }

            // add people
            for (final person in loc.people) {
              builder.element("element", nest: () {
                builder.attribute("type", "Victim${person.type}");
                builder.attribute("id", person.id);
                builder.attribute("name", person.name);
                builder.attribute("x", (person.x).toString());
                builder.attribute("y", (person.y).toString());
                builder.attribute("scaleX", (person.widthScale * person.scale).toString());
                builder.attribute("scaleY", (person.heightScale * person.scale).toString());
                builder.attribute("rotation", person.rotation.toPrecision(3).toString());
                builder.attribute("priority", person.priority);
                builder.attribute("hideOnStart", person.hideOnStart);
                if (person.movable) {
                  builder.attribute("movable", "true");
                }
                if (person.triggerOnce) {
                  builder.attribute("triggerOnce", "true");
                }
                if (person.syncVariable != null) {
                  builder.element("sync", nest: () {
                    builder.attribute("varTableKey", person.syncVariable!);
                    builder.attribute("prop", "visible"); // NOTE: constant. maybe it has a different value in the old app in some cases
                  });
                }
              });
            }

            // add timers
            for (final timer in loc.timers) {
              builder.element("element", nest: () {
                builder.attribute("type", "CSTimer");
                builder.attribute("id", timer.id);
                builder.attribute("x", (timer.x).toString());
                builder.attribute("y", (timer.y).toString());
                builder.attribute("scaleX", (timer.widthScale * timer.scale).toString());
                builder.attribute("scaleY", (timer.heightScale * timer.scale).toString());
                builder.attribute("rotation", timer.rotation.toPrecision(3).toString());
                builder.attribute("priority", timer.priority);
                if (timer.triggerOnce) {
                  builder.attribute("triggerOnce", "true");
                }
              });
            }

            final List<String> soundNames = [];
            // add sounds
            for (final sound in loc.sounds) {
              /* if (File(sound.path).parent.path != File(sound.path).path) {
                print("Sound path: ${sound.path} has parent: ${File(sound.path).parent.path}");
                final originalPath = sound.path;
                final ext = path.extension(sound.path);
                sound.path = nanoid(20) + ext;
                // defer copying audio to sim directory
                getSimSaveDirectory(simValue).then((dir) {
                  print("Copying audio file from $originalPath to ${sound.path}");
                  // Using copy sync to try to take precedence over the next Future
                  File(originalPath).copySync("${dir.path}/${sound.path}");
                }).catchError((err) {
                  print("Error copying audio to sim directory: $err");
                });
              } */
              builder.element("element", nest: () {
                builder.attribute("type", "AudioClip");
                builder.attribute("id", sound.id);
                // builder.attribute("file", sound.path.split("/").last);
                // builder.attribute("file", File(sound.path).path.substring(File(sound.path).parent.path.length + 1));
                builder.attribute("file", path.basename(sound.path));
                builder.attribute("loop", sound.loop.toString());
                builder.attribute("priority", sound.priority.toString());
                builder.attribute("x", sound.x.toString());
                builder.attribute("y", sound.y.toString());
                builder.attribute("scaleX", sound.widthScale.toString());
                builder.attribute("scaleY", sound.heightScale.toString());
                builder.attribute("rotation", sound.rotation.toString());
                if (sound.triggerOnce) {
                  builder.attribute("triggerOnce", "true");
                }
              });
              soundNames.add(sound.path);
            }

            // NOTE: there is a good chance that this is never called
            if (cleanup) {
              // remove remnant sound files
              Future.delayed(const Duration(seconds: 1), () {
                getSimSaveDirectory(simValue).then((dir) {
                  final files = dir.listSync();
                  print("Files in sim directory: ${files.map((e) => e.path).toList().join("\n")}");
                  for (final file in files) {
                    if (file is File && [".m4a", ".mp3", ".wav"].contains(path.extension(file.path)) && !soundNames.contains(file.path)) {
                      print("Deleting remnant sound file: ${file.path}");
                      file.deleteSync();
                    }
                  }
                });
              });
            }

            // NOTE: it is important to save sprites before masks because masks rely on sprites during parsing
            // add masks
            final locMasks = simValue.masks.where((mask) => mask.locationId == loc.id).toList();
            print("Location: ${loc.name} has masks: ${locMasks.map((e) => e.id).toList()} and sprites: ${loc.sprites.map((e) => {
                  "id": e.id,
                  "maskIds": e.maskIds
                }).toList()}");
            for (final mask in locMasks) {
              // final mask = simValue.masks.firstWhere((element) => element.id == maskId);
              // for (final locMask in locMasks) {
              final maskSprites = <SimObject>[];
              final maskable = [...loc.sprites, ...loc.images, ...loc.shapes, ...loc.containers];
              maskSprites.addAll(maskable.where((element) => element.maskIds.contains(mask.id)));
              builder.element("element", nest: () {
                builder.attribute("type", "CSMask");
                // builder.attribute("id", maskId);
                builder.attribute("id", mask.id);
                builder.attribute("name", mask.name);
                // TODO: add mask x and y. correct positioning needs to be checked against the old app
                builder.attribute("x", "0");
                builder.attribute("y", "0");
                List<Coordinate> coords = mask.coordinates;
                if (mask.needsParsing()) {
                  coords = coords.map((c) => Coordinate(c.x * simValue.width, c.y * simValue.height)).toList();
                }
                builder.text(
                  // "true,${locMask.type == MaskType.showOutside},${maskSprites.length},${maskSprites.map((e) => e.id).join(",")},${locMask.coordinates.map((coor) => "${coor.x.toPrecision(2)}c${coor.y.toPrecision(2)}").join("x")}",
                  "true,${mask.type == MaskType.showOutside},${maskSprites.length},${maskSprites.map((e) => e.id).join(",")}${maskSprites.isNotEmpty ? "," : ""}${coords.map((coor) => "${(coor.x /* / simValue.width */).toPrecision(2)}c${(coor.y /* / simValue.height */).toPrecision(2)}").join("x")}",
                );
              });
            }
          });
        }

        _generateNavigations(builder, simValue);
      });

      _generateEnvironmentStates(builder, simValue);
      _generateSimFrames(builder, simValue);
      _generatePlugins(builder, simValue);
      _generateTriggerActions(builder);
    });

    final doc = builder.buildDocument();
    return doc.toXmlString(pretty: true, indent: "  ");
  });
}



/// Generate element values for a location
void _generateElementValues(xml.XmlBuilder builder, SimulationLocation loc) {
  // Generate sprite element values
  for (final sprite in loc.sprites) {
    builder.element("elementVal", nest: () {
      builder.attribute("id", sprite.id);
      builder.text(getSpriteInnerText(sprite));
    });
  }

  // Generate image element values
  for (final pic in loc.images) {
    builder.element("elementVal", nest: () {
      builder.attribute("id", pic.id);
      builder.text(getPicInnerText(pic));
    });
  }

  // Generate text element values
  for (final text in loc.texts) {
    builder.element("elementVal", nest: () {
      builder.attribute("id", text.id);
      builder.text(getTextInnerText(text));
    });
  }

  // Generate shape element values
  for (final shape in loc.shapes) {
    builder.element("elementVal", nest: () {
      builder.attribute("id", shape.id);
      builder.text(getSimShapeInnerText(shape));
    });
  }

  // Generate location jumper element values
  for (final locJumper in loc.jumpers) {
    builder.element("elementVal", nest: () {
      builder.attribute("id", locJumper.id);
      builder.text(getLocationJumperInnerText(locJumper));
    });
  }

  // Generate label element values
  for (final label in loc.labels) {
    builder.element("elementVal", nest: () {
      builder.attribute("id", label.id);
      builder.text(getLabelText(label));
    });
  }

  // Generate container element values
  for (final container in loc.containers) {
    builder.element("elementVal", nest: () {
      builder.attribute("id", container.id);
      builder.text(getContainerText(container));
    });
  }

  // Generate person element values
  for (final person in loc.people) {
    builder.element("elementVal", nest: () {
      builder.attribute("id", person.id);
      builder.attribute("name", person.name);
      if (person.filterColor != Colors.transparent) {
        builder.attribute("color", getColorValue(person.filterColor, withAlpha: true));
      }
      builder.text(getPersonText(person));
    });
  }

  // Generate timer element values
  for (final timer in loc.timers) {
    builder.element("elementVal", nest: () {
      builder.attribute("id", timer.id);
      builder.text(getTimerInnerText(timer));
    });
  }
}

/// Generate core plugins (masks, navigation, images, shapes, sounds)
void _generateCorePlugins(xml.XmlBuilder builder, Scenario simValue) {
  if (simValue.masks.isNotEmpty) {
    builder.element("plugin", nest: () {
      builder.attribute("lib", "CSMask_v1_lib.swf");
      builder.attribute("assetIDs", "CSMask");
    });
  }

  if (simValue.navigations.isNotEmpty) {
    builder.element("plugin", nest: () {
      builder.attribute("lib", "LocJumper_v1_lib.swf");
      builder.attribute("assetIDs", "LocJumper");
    });
  }

  if (simValue.locations.firstWhereOrNull((loc) => loc.images.isNotEmpty) != null) {
    builder.element("plugin", nest: () {
      builder.attribute("lib", "CSPic_v1_lib.swf");
      builder.attribute("assetIDs", "CSPic");
    });
  }

  if (simValue.locations.firstWhereOrNull((loc) => loc.shapes.isNotEmpty) != null) {
    builder.element("plugin", nest: () {
      builder.attribute("lib", "CSShape_v1_lib.swf");
      builder.attribute("assetIDs", "CSShape");
    });
  }

  if (simValue.locations.firstWhereOrNull((loc) => loc.sounds.isNotEmpty) != null) {
    builder.element("plugin", nest: () {
      builder.attribute("lib", "AudioClip_v1_lib.swf");
      builder.attribute("assetIDs", "AudioClip");
    });
  }
}

/// Generate sprite plugins
void _generateSpritePlugins(xml.XmlBuilder builder, Scenario simValue) {
  final allSprites = <String, bool>{};
  for (final loc in simValue.locations) {
    for (var sprite in loc.sprites) {
      allSprites[sprite.assetName] = true;
    }
  }

  for (final assetName in allSprites.keys) {
    builder.element("plugin", nest: () {
      builder.attribute("lib", "${assetName}_v1_lib.swf");
      builder.attribute("assetIDs", assetName);
    });
  }
}

/// Generate people plugins
void _generatePeoplePlugins(xml.XmlBuilder builder, Scenario simValue) {
  final allVictims = <String, bool>{};
  for (final loc in simValue.locations) {
    for (var person in loc.people) {
      allVictims[peopleToAssetMapping[person.type]!] = true;
    }
  }

  for (final assetName in allVictims.keys) {
    builder.element("plugin", nest: () {
      builder.attribute("lib", "${assetName}_v1_lib.swf");
      builder.attribute("assetIDs", assetName);
    });
  }
}

/// Generate trigger actions section
void _generateTriggerActions(xml.XmlBuilder builder) {
  builder.element("triggeractions", nest: () {
    builder.element("actions");
    builder.element("triggers");
    builder.element("mappings");
  });
}

String getTimingTriggerDefValue(SimObject simObj) {
  return ParserUtils.getTimingTriggerDefValue(simObj);
}

String getSpriteInnerText(SimSprite sprite) {
  String timingTrigger = getTimingTriggerDefValue(sprite);
  // out,0,0,brightness,RGB-color,color-opacity,opacity,framerate,speed,fadeInWhen,fadeInDuration,mirrorX,mirrorY,widthScale,fadeOut,fadeOutWhen,fadeOutDuration,timingTrigger
  // NOTE: brightness will always be set to zero because it might need an extra post-processing step to calculate the brightness to the RGB color
  return "out,0,0,1,${getColorValue(sprite.filterColor)},${sprite.filterColor.opacity},${sprite.opacity},${(sprite.framerate * sprite.speed).toPrecision(2)},${sprite.fadeInWhen},${sprite.fadeInDuration},${sprite.mirrorX},${sprite.mirrorY},${sprite.widthScale},${sprite.fadeOut},${sprite.fadeOutWhen},${sprite.fadeOutDuration},$timingTrigger";
}

String getLocationJumperInnerText(SimLocationJumper jumper) {
  String shapeNumber = "0";
  switch (jumper.shape) {
    case "arrow":
    case "arrow-1":
      shapeNumber = "0"; // NOTE: there are 4 variants of arrow from 1 to 4. We only have arrow-1
      break;
    case "arrow-2":
      shapeNumber = "1";
      break;
    case "arrow-3":
      shapeNumber = "2";
      break;
    case "arrow-4":
      shapeNumber = "3";
      break;
    case "rectangle":
      shapeNumber = "4";
      break;
    case "square":
      shapeNumber = "5";
      break;
    case "circle":
      shapeNumber = "6";
      break;
    case "triangle": // NOTE: this is not supported in old players
      shapeNumber = "7";
      break;
    case "rounded-rectangle": // NEW SHAPE
      shapeNumber = "8";
      break;
    default:
      throw "Invalid shape: ${jumper.shape}";
  }
  final hsl = HSLColor.fromColor(jumper.filterColor);
  /* starting ${jumper.fadeInWhen + jumper.fadeInDuration > 0}, these are new add-ons */
  /* ${jumper.fadeInWhen + jumper.fadeInDuration > 0} checks if either is larger than 0 */
  return "in,${jumper.to},$shapeNumber,${jumper.delay},${(jumper.delay > 0).toString()},${jumper.clickable.toString()},${hsl.lightness.toPrecision(3)},${hsl.hue},${hsl.saturation},${hsl.alpha.toPrecision(3)},${jumper.mirrorY.toString()},${jumper.mirrorX.toString()},${(jumper.widthScale)},${jumper.fadeOut},${jumper.fadeOutWhen},${jumper.fadeOutDuration},${jumper.blur},${getTimingTriggerDefValue(jumper)},${jumper.fadeInWhen + jumper.fadeInDuration > 0},${jumper.fadeInWhen},${jumper.fadeInDuration}";
}

String getShapeInnerText(SimShape shape) {
  final hsl = HSLColor.fromColor(shape.filterColor);
  return "out,0,0,${hsl.lightness.toPrecision(3)},${hsl.hue},${hsl.saturation},${hsl.alpha.toPrecision(3)},${shape.shape},${shape.fadeInWhen},${shape.fadeInDuration},${shape.mirrorX},${shape.mirrorY},${shape.widthScale},${shape.fadeOut},${shape.fadeOutWhen},${shape.fadeOutDuration},${shape.blur},${getTimingTriggerDefValue(shape)}";
}

String getPicInnerText(SimImage pic) {
  // final hsl = HSLColor.fromColor(pic.filterColor);
  // TODO: missing implementation for last 3 values
  // TODO: check if default value of "out" (index 0) is responsible for showing the image on start
  return "${pic.hideOnStart ? "in" : "out"},0,0,1,${getColorValue(pic.filterColor)},${pic.filterColor.opacity},${pic.opacity},${pic.fadeInWhen},${pic.fadeInDuration},${pic.movable},${pic.mirrorX},${pic.mirrorY},${pic.scale.toPrecision(3)},${pic.fadeOut},${pic.fadeOutWhen},${pic.fadeOutDuration},${pic.blur},${getTimingTriggerDefValue(pic)},${pic.clickToToggle},0,0";
}

String getSimShapeInnerText(SimShape shape) {
  // final hsl = HSLColor.fromColor(shape.filterColor);
  // return "out,0,0,${hsl.lightness.toPrecision(3)},${hsl.hue},${hsl.saturation},${hsl.alpha.toPrecision(3)},${shape.fadeInWhen},${shape.fadeInDuration},null,${shape.mirrorX},${shape.mirrorY},${shape.widthScale},${shape.fadeOut},${shape.fadeOutWhen},${shape.fadeOutDuration},${shape.blur},${shape.trigger},0,0,0";
  return "out,0,0,1,${getColorValue(shape.filterColor)},${shape.filterColor.opacity},${shape.opacity},${shape.fadeInWhen},${shape.fadeInDuration},null,${shape.mirrorX},${shape.mirrorY},${shape.widthScale},${shape.fadeOut},${shape.fadeOutWhen},${shape.fadeOutDuration},${shape.blur},${shape.trigger},0,0,0";
}

String getTextInnerText(SimText txt) {
  // return "${Uri.encodeFull(txt.text)},${txt.backgroundColor},${txt.fadeInWhen},${txt.fadeInDuration},${txt.fadeOut.toString()},${txt.fadeOutWhen},${txt.fadeOutDuration},${txt.trigger}";
  return "${Uri.encodeFull(txt.text)},${txt.backgroundColor.toHexString()},${txt.fadeInWhen},${txt.fadeInDuration},${txt.fadeOut.toString()},${txt.fadeOutWhen},${txt.fadeOutDuration},${getTimingTriggerDefValue(txt)}";
}

String getTimerInnerText(SimTimer timer) {
  // NOTE: missing time of day attribute at the end of the string
  return "${timerFormats.indexOf(timer.format)},${timerTypes.indexOf(timer.type)},${timer.startingSecond},${timer.filterColor.value},${timer.fadeInWhen},${timer.fadeInDuration},${timer.fadeOut.toString()},${timer.fadeOutWhen},${timer.fadeOutDuration},${timer.trigger}";
}

void setLocationJumperFromText(SimLocationJumper jumper, List<String> details) {
  String getShape(String shapeNumber) {
    switch (shapeNumber) {
      case "0":
        return "arrow-1";
      case "1":
        return "arrow-2";
      case "2":
        return "arrow-3";
      case "3":
        return "arrow-4";
      case "4":
        return "rectangle";
      case "5":
        return "square";
      case "6":
        return "circle";
      case "7":
        return "triangle";
      case "8": // NEW SHAPE
        return "rounded-rectangle";
    }
    // print("INVALID SHAPE NUMBER: $shapeNumber");
    throw "Invalid shape number: $shapeNumber";
  }

  if (details.length >= 2) {
    jumper.to = details[1];
  }
  if (details.length >= 3) {
    jumper.shape = getShape(details[2]);
  }
  // split this "in,${jumper.to},$shapeNumber,${jumper.delay},${(jumper.delay > 0).toString()},${jumper.clickable.toString()},${hsl.lightness.toPrecision(3)},${hsl.hue},${hsl.saturation},${hsl.alpha.toPrecision(3)},${jumper.mirrorY.toString()},${jumper.mirrorX.toString()},${(jumper.widthScale ?? 0)}";
  // and convert the details based on the index starting from index 7
  if (details.length > 3) {
    jumper.delay = int.parse(details[3]);
  }
  if (details.length > 5) {
    jumper.clickable = details[5] == "true";
  }
  if (details.length > 6) {
    jumper.filterColor =
        HSLColor.fromAHSL(double.parse(details[9]), double.parse(details[7]), double.parse(details[8]), double.parse(details[6])).toColor();
  }
  if (details.length >= 11) {
    jumper.mirrorY = details[10] == "true";
  }
  if (details.length >= 12) {
    jumper.mirrorX = details[11] == "true";
  }
  if (details.length >= 13) {
    jumper.widthScale = double.parse(details[12]);
  }
}

double getInitialSize() {
  return ParserUtils.getInitialSize();
}

int getColorValue(Color c, {bool withAlpha = false}) {
  return ColorUtils.getColorValue(c, withAlpha: withAlpha);
}

Color getColorFromValue(int value, {bool withAlpha = false}) {
  return ColorUtils.getColorFromValue(value, withAlpha: withAlpha);
}
