// Dart core libraries
import 'dart:io';

// Third-party packages
import 'package:flutter/material.dart';
import 'package:flutter_colorpicker/flutter_colorpicker.dart';
import 'package:get/get.dart';
import 'package:path/path.dart' as path;
import 'package:xml/xml.dart' as xml;

// Application models
import 'package:simsushare_player/models/Mask.dart';
import 'package:simsushare_player/models/SimObjects.dart';
import 'package:simsushare_player/models/Simulation.dart';

// Application utilities and parsers
import 'package:simsushare_player/utils/constants.dart';
import 'package:simsushare_player/utils/element_parsers/element_parser_factory.dart';
import 'package:simsushare_player/utils/element_parsers/mask_parser.dart';
import 'package:simsushare_player/utils/parser_base.dart';
import 'package:simsushare_player/utils/parser_constants.dart' as parser_constants;
import 'package:simsushare_player/utils/parser_utilities.dart';
import 'package:simsushare_player/utils/performance/performance_monitor.dart';

// Application components
import 'package:simsushare_player/flame/Containers/parser.dart';
import 'package:simsushare_player/flame/Labels/parser.dart';
import 'package:simsushare_player/flame/People/parser.dart';
import 'package:simsushare_player/pages/NewScenarioWithPlayer/helpers/helper_methods.dart';

/// Parse a mask definition string into a Mask object
///
/// [id] The unique identifier for the mask
/// [definition] The mask definition string containing type and coordinates
/// [locationId] The ID of the location this mask belongs to
///
/// Returns a [Mask] object with parsed properties
/// Throws [ArgumentError] if the definition format is invalid
Mask parseMask(String id, String definition, String locationId) {
  if (id.isEmpty) {
    throw ArgumentError(parser_constants.ParserErrors.missingElementId);
  }
  if (definition.isEmpty) {
    throw ArgumentError(parser_constants.ParserErrors.invalidMaskDefinition);
  }
  if (locationId.isEmpty) {
    throw ArgumentError("Location ID cannot be empty");
  }

  try {
    final defSplit = ParserUtils.parseDefinition(definition);

    if (defSplit.length < 3) {
      throw ArgumentError(parser_constants.ParserErrors.invalidMaskDefinition);
    }

    final showOutside = ParserUtils.parseBool(defSplit[1]);
    final maskType = showOutside ? MaskType.showOutside : MaskType.showWithin;

    final coordinates = ParserUtils.parseCoordinates(defSplit.last);

    return Mask(
      type: maskType,
      coordinates: coordinates,
      id: id,
      name: id,
      locationId: locationId,
    );
  } catch (e) {
    throw ArgumentError("Failed to parse mask '$id': $e");
  }
}

/// Retrieve the sprite IDs from a mask definition string
///
/// [definition] The mask definition string to parse
/// Returns a list of sprite IDs found in the mask definition
/// Throws [ArgumentError] if the definition is invalid
List<String> parseMaskSprites(String definition) {
  if (definition.isEmpty) {
    throw ArgumentError("Mask definition cannot be empty");
  }

  try {
    final maskParser = MaskParser();
    return maskParser.parseMaskSprites(definition);
  } catch (e) {
    throw ArgumentError("Failed to parse mask sprites: $e");
  }
}

/// Retrieve the simulation object IDs from a mask definition string
///
/// [definition] The mask definition string to parse
/// Returns a list of simulation object IDs found in the mask definition
/// Throws [ArgumentError] if the definition is invalid
List<String> parseSimObjectsInMask(String definition) {
  if (definition.isEmpty) {
    throw ArgumentError("Mask definition cannot be empty");
  }

  try {
    final maskParser = MaskParser();
    return maskParser.parseSimObjectsInMask(definition);
  } catch (e) {
    throw ArgumentError("Failed to parse sim objects in mask: $e");
  }
}

/// Parse and normalize a timing trigger value
///
/// [trigger] The timing trigger value to parse (can be numeric or string)
/// Returns the normalized timing trigger string
/// Throws [ArgumentError] if the trigger value is invalid
String parseTimingTrigger(String trigger) {
  if (trigger.isEmpty) {
    throw ArgumentError("Timing trigger cannot be empty");
  }

  try {
    return ParserUtils.parseTimingTrigger(trigger);
  } catch (e) {
    throw ArgumentError("Failed to parse timing trigger '$trigger': $e");
  }
}

/// Parse navigation elements from XML document
List<SimulationNavigation> _parseNavigations(xml.XmlDocument simdef) {
  final simDefNavs = simdef.findAllElements("nav").toList();
  return simDefNavs
      .map((e) => SimulationNavigation(
            direction: e.getAttribute("dir") ?? "",
            from: e.getAttribute("fromID") ?? "",
            to: e.getAttribute("toID") ?? "",
          ))
      .toList();
}

/// Parse state-location mapping from simFrame elements
Map<String, String> _parseStateLocationMapping(xml.XmlDocument simdef) {
  final simDefSimFrames = simdef.findAllElements("simFrame").toList();
  final stateLocationMapping = <String, String>{};

  for (final simFrame in simDefSimFrames) {
    final state = simFrame.getAttribute("states");
    final location = simFrame.getAttribute("locs");
    if (state != null && location != null) {
      stateLocationMapping[location] = state;
    }
  }

  return stateLocationMapping;
}

/// Parse simulation states from XML document
List<SimulationState> _parseStates(xml.XmlDocument simdef) {
  final simDefStates = simdef.findAllElements("state").toList();
  return simDefStates
      .map((e) => SimulationState(
            id: e.getAttribute("id") ?? "",
            name: e.getAttribute("name") ?? "",
          ))
      .toList();
}

/// Parse simulation variables from XML document
Map<String, String> _parseVariables(xml.XmlDocument simdef) {
  final variables = simdef.findAllElements("variable").toList();
  final variableMap = <String, String>{};

  for (final variable in variables) {
    final id = variable.getAttribute("id");
    final defaultValue = variable.getAttribute("default");
    if (id != null && defaultValue != null) {
      variableMap[id] = defaultValue;
    }
  }

  return variableMap;
}

/// Parse all locations from XML document
Future<List<SimulationLocation>> _parseLocations(
  xml.XmlDocument simdef,
  Directory dir,
  List<String> allFileNames,
  SimulationMetadata metadata,
  Map<String, String> stateLocationMapping,
  List<SimulationState> states,
  List<Mask> simMasks,
) async {
  final simDefLocations = simdef.findAllElements("location").toList();

  return await Future.wait(simDefLocations.map((location) async {
    final locId = location.getAttribute("id");
    final locName = location.getAttribute("name");
    final brightness = location.getAttribute("brightness");

    final loc = SimulationLocation(
      id: locId ?? "",
      name: locName ?? "",
      color: location.getAttribute("color") ?? "",
      state: stateLocationMapping[locId] ?? states.first.id,
      sprites: [],
      imageBrightness: double.tryParse(brightness ?? "0") ?? 0,
    );

    final elements = location.findElements("element");
    await _parseLocationElements(elements, loc, simdef, dir, allFileNames, metadata, simMasks);

    return loc;
  }));
}

/// Parse all elements within a location with parallel processing
Future<void> _parseLocationElements(
  Iterable<xml.XmlElement> elements,
  SimulationLocation location,
  xml.XmlDocument simdef,
  Directory dir,
  List<String> allFileNames,
  SimulationMetadata metadata,
  List<Mask> simMasks,
) async {
  final context = {
    "locationId": location.id,
    "directory": dir,
    "allFileNames": allFileNames,
    "metadata": metadata,
    "simMasks": simMasks,
  };

  // Separate elements by type for optimized processing
  // Async elements (images/sprites) require asset loading and are processed in parallel
  // Sync elements (shapes/text/etc.) are lightweight and processed sequentially first
  final asyncElements = <xml.XmlElement>[];
  final syncElements = <xml.XmlElement>[];

  for (final element in elements) {
    final elementType = element.getAttribute("type");
    // CSPic and Sprite elements require async asset loading
    if (elementType == "CSPic" || elementType?.contains("Sprite") == true) {
      asyncElements.add(element);
    } else {
      // All other elements can be processed synchronously
      syncElements.add(element);
    }
  }

  // Process sync elements first (faster)
  for (final element in syncElements) {
    try {
      final parsedElement = await ElementParserFactory.parseElement(element, simdef, context);
      if (parsedElement != null) {
        _addElementToLocation(parsedElement, location);
      }
    } catch (e) {
      print("Error parsing element ${element.getAttribute("id")}: $e");
    }
  }

  // Process async elements in parallel with controlled concurrency
  // Limit concurrent operations to prevent memory overload and improve performance
  const maxConcurrency = 3;
  final futures = <Future<void>>[];

  // Process elements in batches to maintain controlled concurrency
  for (int i = 0; i < asyncElements.length; i += maxConcurrency) {
    final batch = asyncElements.skip(i).take(maxConcurrency);
    final batchFutures = batch.map((element) async {
      try {
        // Parse element using factory pattern with performance monitoring
        final parsedElement = await ElementParserFactory.parseElement(element, simdef, context);
        if (parsedElement != null) {
          // Add to appropriate collection based on element type
          _addElementToLocation(parsedElement, location);
        }
      } catch (e) {
        // Log errors but continue processing other elements
        print("Error parsing element ${element.getAttribute("id")}: $e");
      }
    });

    futures.addAll(batchFutures);

    // Wait for current batch to complete before starting next batch
    // This prevents overwhelming the system with too many concurrent operations
    await Future.wait(batchFutures);
  }
}

/// Add parsed element to appropriate location collection
void _addElementToLocation(SimObject element, SimulationLocation location) {
  if (element is SimImage) {
    location.images.add(element);
  } else if (element is SimShape) {
    location.shapes.add(element);
  } else if (element is SimLocationJumper) {
    location.jumpers.add(element);
  } else if (element is SimText) {
    location.texts.add(element);
  } else if (element is SimTimer) {
    location.timers.add(element);
  } else if (element is SimSound) {
    location.sounds.add(element);
  } else if (element is SimContainer) {
    location.containers.add(element);
  } else if (element is SimPerson) {
    location.people.add(element);
  } else if (element is SimLabel) {
    location.labels.add(element);
  } else if (element is SimSprite) {
    location.sprites.add(element);
  }
}

/// Parse simulation metadata from XML document
SimulationMetadata _parseSimulationMetadata(xml.XmlDocument simdef) {
  final simElement = simdef.getElement("sim");
  if (simElement == null) {
    throw ArgumentError(parser_constants.ParserErrors.xmlParsingError);
  }

  final width = ParserUtils.parseDouble(
    simElement.getAttribute("width"),
    fallback: Get.width.toDouble(),
  );
  final height = ParserUtils.parseDouble(
    simElement.getAttribute("height"),
    fallback: Get.height.toDouble(),
  );

  return SimulationMetadata(
    id: simElement.getAttribute("id") ?? "",
    name: simElement.getAttribute("title") ?? "",
    width: width,
    height: height,
    backgroundColor: simElement.getAttribute("backgroundColor") ?? parser_constants.ParserDefaults.defaultBackgroundColor,
    navClusterX: ParserUtils.calculateNavClusterPosition(
      simElement.getAttribute("NavClusterXPct"),
      width,
      false,
    ),
    navClusterY: ParserUtils.calculateNavClusterPosition(
      simElement.getAttribute("NavClusterYPct"),
      height,
      true,
    ),
  );
}

/// Main orchestration function for parsing SimDef XML into Scenario
///
/// This is the primary entry point for parsing simulation definition XML files.
/// It handles the complete parsing process including:
/// - XML document parsing and validation
/// - Element parser factory initialization
/// - Performance monitoring and optimization
/// - Parallel processing of simulation components
///
/// [simdefXml] The XML content of the simulation definition file
/// [dir] The directory containing simulation assets and files
///
/// Returns a complete [Scenario] object with all parsed simulation data
/// Throws [ArgumentError] if the XML is invalid or required data is missing
/// Throws [FileSystemException] if asset files cannot be accessed
Future<Scenario> parseSimDef(String simdefXml, Directory dir) async {
  if (simdefXml.isEmpty) {
    throw ArgumentError("SimDef XML content cannot be empty");
  }
  if (!dir.existsSync()) {
    throw ArgumentError("Simulation directory does not exist: ${dir.path}");
  }

  final monitor = PerformanceMonitor();

  return await monitor.timeAsyncOperation('parse_simdef_total', () async {
    print("Parsing sim def: $simdefXml");

    try {
      // Initialize element parser factory
      ElementParserFactory.initialize();

      final simdef = await monitor.timeAsyncOperation('parse_xml_document', () async {
        return xml.XmlDocument.parse(simdefXml);
      });

      return await _orchestrateSimDefParsing(simdef, dir);
    } catch (e) {
      throw ArgumentError("Failed to parse SimDef: $e");
    }
  });
}

/// Orchestrate the complete parsing process
Future<Scenario> _orchestrateSimDefParsing(xml.XmlDocument simdef, Directory dir) async {
  final monitor = PerformanceMonitor();

  return await monitor.timeAsyncOperation('orchestrate_parsing', () async {
    // Parse core simulation metadata
    final metadata = await monitor.timeAsyncOperation('parse_metadata', () async {
      return _parseSimulationMetadata(simdef);
    });
    print("Sim width: ${metadata.width} x height: ${metadata.height}");

    // Parse simulation structure components in parallel
    final futures = await Future.wait([
      monitor.timeAsyncOperation('parse_navigations', () async => _parseNavigations(simdef)),
      monitor.timeAsyncOperation('parse_state_mapping', () async => _parseStateLocationMapping(simdef)),
      monitor.timeAsyncOperation('parse_states', () async => _parseStates(simdef)),
      monitor.timeAsyncOperation('parse_variables', () async => _parseVariables(simdef)),
    ]);

    final navs = futures[0] as List<SimulationNavigation>;
    final stateLocationMapping = futures[1] as Map<String, String>;
    final states = futures[2] as List<SimulationState>;
    final variables = futures[3] as Map<String, String>;

    // Prepare context for location parsing
    final allFileNames = await monitor.timeAsyncOperation('extract_filenames', () async {
      return _extractFileNames(dir);
    });
    final simMasks = <Mask>[];

    // Parse all locations with their elements
    final locations = await monitor.timeAsyncOperation('parse_locations', () async {
      return await _parseLocations(simdef, dir, allFileNames, metadata, stateLocationMapping, states, simMasks);
    });

    // Build and return the complete scenario
    return await monitor.timeAsyncOperation('build_scenario', () async {
      return _buildScenario(metadata, locations, states, navs, variables, simMasks);
    });
  });
}

/// Extract file names from directory for element parsing
List<String> _extractFileNames(Directory dir) {
  return dir.listSync().map((e) => e.path.split("/").last).toList();
}

/// Build the final Scenario object from parsed components
Scenario _buildScenario(
  SimulationMetadata metadata,
  List<SimulationLocation> locations,
  List<SimulationState> states,
  List<SimulationNavigation> navs,
  Map<String, String> variables,
  List<Mask> simMasks,
) {
  return Scenario(
    name: metadata.name,
    id: metadata.id,
    width: metadata.width,
    height: metadata.height,
    locations: locations,
    currentState: states.isNotEmpty ? states[0].id : "",
    states: states,
    initialLocationId: variables["CURRENT_LOCATION"],
    initialStateId: variables["CURRENT_SIM_STATE"],
    navClusterX: metadata.navClusterX,
    navClusterY: metadata.navClusterY,
  )
    ..navigations.addAll(navs)
    ..masks.addAll(simMasks);
}

/// Generate SimDef XML from Scenario object
///
/// This is the primary function for converting a Scenario object back into
/// SimDef XML format. It handles the complete XML generation process including:
/// - Simulation attributes and metadata
/// - Variable table and environment setup
/// - Location definitions with all elements
/// - Plugin dependencies and configurations
/// - Performance optimization and cleanup
///
/// [simValue] The Scenario object to convert to XML
/// [cleanup] Whether to perform cleanup of unused sound files (default: false)
///
/// Returns a formatted XML string representing the complete simulation definition
/// Throws [ArgumentError] if the scenario data is invalid or incomplete
String generateSimDef(Scenario simValue, {bool cleanup = false}) {
  if (simValue.locations.isEmpty) {
    throw ArgumentError("Scenario must contain at least one location");
  }
  if (simValue.id.isEmpty) {
    throw ArgumentError("Scenario ID cannot be empty");
  }
  if (simValue.name.isEmpty) {
    throw ArgumentError("Scenario name cannot be empty");
  }

  try {
    print("Sim Masks: ${simValue.masks}");
    final builder = xml.XmlBuilder();

    builder.element("sim", nest: () {
      _generateSimAttributes(builder, simValue);
      _generateSimHeader(builder, simValue);
      _generateVariableTable(builder, simValue);
      _generateEnvironment(builder, simValue, cleanup);
      _generateEnvironmentStates(builder, simValue);
      _generateSimFrames(builder, simValue);
      _generatePlugins(builder, simValue);
      _generateTriggerActions(builder);
    });

    final doc = builder.buildDocument();
    return doc.toXmlString(pretty: true, indent: "  ");
  } catch (e) {
    throw ArgumentError("Failed to generate SimDef XML: $e");
  }
}

/// Generate sim element attributes
void _generateSimAttributes(xml.XmlBuilder builder, Scenario simValue) {
  builder.attribute("id", simValue.id);
  builder.attribute("title", simValue.name);
  builder.attribute("width", simValue.width.ceil());
  builder.attribute("height", simValue.height.ceil());
  builder.attribute("backgroundColor", "0x222222");

  if (simValue.navClusterX != null) {
    builder.attribute("NavClusterXPct", ((simValue.navClusterX! + 20) / simValue.width).toStringAsFixed(3));
  }

  if (simValue.navClusterY != null) {
    builder.attribute("NavClusterYPct", (1 - ((simValue.navClusterY! + 20) / simValue.height)).toStringAsFixed(3));
  }
}

/// Generate sim header section
void _generateSimHeader(xml.XmlBuilder builder, Scenario simValue) {
  builder.element("header", nest: () {
    builder.element("summary");
    builder.element("description");
    builder.element("attachments");
    builder.element("keywords");
    builder.element("categories", nest: () {
      builder.attribute("id", simValue.categoryId);
    });
    builder.element("history");
  });
}

/// Generate variable table section
void _generateVariableTable(xml.XmlBuilder builder, Scenario simValue) {
  builder.element("varTable", nest: () {
    builder.element("variable", nest: () {
      builder.attribute("id", "CURRENT_SIM_STATE");
      builder.attribute("type", "string");
      builder.attribute("default", simValue.initialStateId ?? simValue.states[0].id);
      builder.attribute("desc", "the current sim state");
    });
    builder.element("variable", nest: () {
      builder.attribute("id", "CURRENT_LOCATION");
      builder.attribute("type", "string");
      builder.attribute("default", simValue.initialLocationId ?? simValue.locations[0].id);
      builder.attribute("desc", "the current sim location");
    });
  });
}

/// Generate environment section with locations and navigations
void _generateEnvironment(xml.XmlBuilder builder, Scenario simValue, bool cleanup) {
  builder.element("environ", nest: () {
    _generateLocations(builder, simValue, cleanup);
    _generateNavigations(builder, simValue);
  });
}

/// Generate all locations with their elements
void _generateLocations(xml.XmlBuilder builder, Scenario simValue, bool cleanup) {
  builder.element("locations", nest: () {
    for (final loc in simValue.locations) {
      _generateSingleLocation(builder, simValue, loc, cleanup);
    }
  });
}

/// Generate a single location with all its elements
void _generateSingleLocation(xml.XmlBuilder builder, Scenario simValue, SimulationLocation loc, bool cleanup) {
  builder.element("location", nest: () {
    builder.attribute("id", loc.id);
    builder.attribute("name", loc.name);
    builder.attribute("brightness", loc.imageBrightness);
    if (loc.color.isNotEmpty) builder.attribute("color", loc.color);

    // Generate location elements
    _generateLocationBackground(builder, simValue, loc);
    _generateLocationImages(builder, loc);
    _generateLocationSprites(builder, simValue, loc);
    _generateLocationJumpers(builder, simValue, loc);
    _generateLocationTexts(builder, simValue, loc);
    _generateLocationShapes(builder, simValue, loc);
    _generateLocationLabels(builder, loc);
    _generateLocationContainers(builder, loc);
    _generateLocationPeople(builder, loc);
    _generateLocationTimers(builder, loc);
    _generateLocationSounds(builder, simValue, loc, cleanup);
    _generateLocationMasks(builder, simValue, loc);
  });
}

/// Generate location background element
void _generateLocationBackground(xml.XmlBuilder builder, Scenario simValue, SimulationLocation loc) {
  final locIndex = simValue.locations.indexOf(loc);
  builder.element("element", attributes: {
    "type": "CSPic",
    "id": "background",
    "file": loc.image.isNotEmpty ? "BK$locIndex${path.extension(loc.image)}" : "",
    "background": "true",
    "x": (simValue.width / 2).ceil().toString(),
    "y": (simValue.height / 2).ceil().toString(),
    "scaleX": (loc.imageScale).toString(),
    "scaleY": (loc.imageScale).toString(),
    "offset": "${loc.imageOffset.dx}x${loc.imageOffset.dy}",
  });
}

/// Generate location images
void _generateLocationImages(xml.XmlBuilder builder, SimulationLocation loc) {
  for (final pic in loc.images) {
    builder.element("element", nest: () {
      builder.attribute("type", "CSPic");
      builder.attribute("id", pic.id);
      builder.attribute("x", (pic.x).toString());
      builder.attribute("y", (pic.y).toString());
      builder.attribute("file", File(pic.path).path.substring(File(pic.path).parent.path.length + 1));
      builder.attribute("scaleX", (pic.widthScale).toString());
      builder.attribute("scaleY", (pic.heightScale).toString());
      builder.attribute("rotation", pic.rotation.toPrecision(3).toString());
      builder.attribute("priority", pic.priority.toString());
      if (pic.to.isNotEmpty) builder.attribute("to", pic.to);
      if (pic.syncVariable != null) {
        builder.attribute("syncVar", pic.syncVariable);
      }
    });
  }
}

/// Generate location sprites
void _generateLocationSprites(xml.XmlBuilder builder, Scenario simValue, SimulationLocation loc) {
  for (final sp in loc.sprites) {
    builder.element("element", attributes: {
      "type": sp.assetName,
      "id": sp.id,
      "name": sp.name,
      "x": (sp.x * simValue.width).toPrecision(2).toString(),
      "y": (sp.y * simValue.height).toPrecision(2).toString(),
      "scale": sp.scale.toStringAsFixed(2),
      "scaleX": (sp.widthScale).toString(),
      "scaleY": (sp.heightScale).toString(),
      "rotation": sp.rotation.toPrecision(3).toString(),
      "priority": sp.priority.toString(),
      if (sp.movable) "movable": "true",
      if (sp.triggerOnce) "triggerOnce": "true",
    });
  }
}

/// Generate navigations section
void _generateNavigations(xml.XmlBuilder builder, Scenario simValue) {
  builder.element("navigations", nest: () {
    for (final nav in simValue.navigations) {
      builder.element("nav", nest: () {
        builder.attribute("dir", nav.direction);
        builder.attribute("fromID", nav.from);
        builder.attribute("toID", nav.to);
      });
    }
  });
}

/// Generate environment states section
void _generateEnvironmentStates(xml.XmlBuilder builder, Scenario simValue) {
  builder.element("environstates", nest: () {
    builder.element("states", nest: () {
      for (final state in simValue.states) {
        builder.element("state", nest: () {
          builder.attribute("id", state.id);
          builder.attribute("name", state.name);
        });
      }
    });
  });
}

/// Generate sim frames section
void _generateSimFrames(xml.XmlBuilder builder, Scenario simValue) {
  builder.element("simFrames", nest: () {
    for (final loc in simValue.locations) {
      builder.element("simFrame", nest: () {
        builder.attribute("states", loc.state);
        builder.attribute("locs", loc.id);

        _generateElementValues(builder, loc);
      });
    }
  });
}

/// Generate plugins section
void _generatePlugins(xml.XmlBuilder builder, Scenario simValue) {
  builder.element("plugins", nest: () {
    _generateCorePlugins(builder, simValue);
    _generateSpritePlugins(builder, simValue);
    _generatePeoplePlugins(builder, simValue);
  });
}

/// Generate location jumpers
void _generateLocationJumpers(xml.XmlBuilder builder, Scenario simValue, SimulationLocation loc) {
  for (final jumper in loc.jumpers) {
    builder.element("element", nest: () {
      builder.attribute("type", "LocJumper");
      builder.attribute("id", jumper.id);
      builder.attribute("x", (jumper.x * simValue.width).toString());
      builder.attribute("y", (jumper.y * simValue.height).toString());
      builder.attribute("scaleX", jumper.widthScale.toString());
      builder.attribute("scaleY", jumper.heightScale.toString());
      builder.attribute("rotation", jumper.rotation.toPrecision(3).toString());
      builder.attribute("priority", jumper.priority.toString());
      builder.attribute("to", jumper.to);
      builder.attribute("shape", jumper.shape);
    });
  }
}

/// Generate location texts
void _generateLocationTexts(xml.XmlBuilder builder, Scenario simValue, SimulationLocation loc) {
  for (final text in loc.texts) {
    builder.element("element", nest: () {
      builder.attribute("type", "CSText");
      builder.attribute("id", text.id);
      builder.attribute("x", (text.x * simValue.width).toString());
      builder.attribute("y", (text.y * simValue.height).toString());
      builder.attribute("scaleX", text.widthScale.toString());
      builder.attribute("scaleY", text.heightScale.toString());
      builder.attribute("rotation", text.rotation.toPrecision(3).toString());
      builder.attribute("priority", text.priority.toString());
      builder.attribute("text", text.text);
    });
  }
}

/// Generate location shapes
void _generateLocationShapes(xml.XmlBuilder builder, Scenario simValue, SimulationLocation loc) {
  for (final shape in loc.shapes) {
    builder.element("element", nest: () {
      builder.attribute("type", "CSShape");
      builder.attribute("id", shape.id);
      builder.attribute("x", (shape.x * simValue.width).toString());
      builder.attribute("y", (shape.y * simValue.height).toString());
      builder.attribute("scaleX", shape.widthScale.toString());
      builder.attribute("scaleY", shape.heightScale.toString());
      builder.attribute("rotation", shape.rotation.toPrecision(3).toString());
      builder.attribute("priority", shape.priority.toString());
      builder.attribute("shape", shape.shape);
    });
  }
}

/// Generate location labels
void _generateLocationLabels(xml.XmlBuilder builder, SimulationLocation loc) {
  for (final label in loc.labels) {
    builder.element("element", nest: () {
      builder.attribute("type", "CSLabel");
      builder.attribute("id", label.id);
      builder.attribute("x", label.x.toString());
      builder.attribute("y", label.y.toString());
      builder.attribute("name", label.name);
    });
  }
}

/// Generate location containers
void _generateLocationContainers(xml.XmlBuilder builder, SimulationLocation loc) {
  for (final container in loc.containers) {
    builder.element("element", nest: () {
      builder.attribute("type", "CSContainer");
      builder.attribute("id", container.id);
      builder.attribute("x", container.x.toString());
      builder.attribute("y", container.y.toString());
      builder.attribute("name", container.name);
    });
  }
}

/// Generate location people
void _generateLocationPeople(xml.XmlBuilder builder, SimulationLocation loc) {
  for (final person in loc.people) {
    builder.element("element", nest: () {
      builder.attribute("type", person.type);
      builder.attribute("id", person.id);
      builder.attribute("x", person.x.toString());
      builder.attribute("y", person.y.toString());
      builder.attribute("name", person.name);
      builder.attribute("scaleX", person.widthScale.toString());
      builder.attribute("scaleY", person.heightScale.toString());
      builder.attribute("rotation", person.rotation.toPrecision(3).toString());
      builder.attribute("priority", person.priority.toString());
    });
  }
}

/// Generate location timers
void _generateLocationTimers(xml.XmlBuilder builder, SimulationLocation loc) {
  for (final timer in loc.timers) {
    builder.element("element", nest: () {
      builder.attribute("type", "CSTimer");
      builder.attribute("id", timer.id);
      builder.attribute("x", timer.x.toString());
      builder.attribute("y", timer.y.toString());
      builder.attribute("type", timer.type);
      builder.attribute("format", timer.format);
      builder.attribute("seconds", timer.seconds.toString());
      builder.attribute("startingSecond", timer.startingSecond.toString());
    });
  }
}

/// Generate location sounds
void _generateLocationSounds(xml.XmlBuilder builder, Scenario simValue, SimulationLocation loc, bool cleanup) {
  final soundNames = <String>{};

  for (final sound in loc.sounds) {
    soundNames.add(sound.path);
    builder.element("element", nest: () {
      builder.attribute("type", "AudioClip");
      builder.attribute("id", sound.id);
      builder.attribute("x", "0");
      builder.attribute("y", "0");
      builder.attribute("file", sound.path);
      builder.attribute("loop", sound.loop.toString());
      builder.attribute("volume", "1.0");
    });
  }

  // Handle cleanup of remnant sound files if requested
  if (cleanup) {
    Future.delayed(const Duration(seconds: 1), () {
      getSimSaveDirectory(simValue).then((dir) {
        final files = dir.listSync();
        print("Files in sim directory: ${files.map((e) => e.path).toList().join("\n")}");
        for (final file in files) {
          if (file is File && [".m4a", ".mp3", ".wav"].contains(path.extension(file.path)) && !soundNames.contains(file.path)) {
            print("Deleting remnant sound file: ${file.path}");
            file.deleteSync();
          }
        }
      });
    });
  }
}

/// Generate location masks
void _generateLocationMasks(xml.XmlBuilder builder, Scenario simValue, SimulationLocation loc) {
  final locMasks = simValue.masks.where((mask) => mask.locationId == loc.id).toList();
  print("Location: ${loc.name} has masks: ${locMasks.map((e) => e.id).toList()} and sprites: ${loc.sprites.map((e) => {
        "id": e.id,
        "maskIds": e.maskIds
      }).toList()}");

  for (final mask in locMasks) {
    final maskSprites = loc.sprites.where((sprite) => sprite.maskIds.contains(mask.id)).toList();

    builder.element("element", nest: () {
      builder.attribute("type", "CSMask");
      builder.attribute("id", mask.id);
      builder.attribute("name", mask.name);
      builder.attribute("x", "0");
      builder.attribute("y", "0");

      final coords = mask.coordinates;
      builder.text(
        "true,${mask.type == MaskType.showOutside},${maskSprites.length},${maskSprites.map((e) => e.id).join(",")}${maskSprites.isNotEmpty ? "," : ""}${coords.map((coor) => "${(coor.x).toPrecision(2)}c${(coor.y).toPrecision(2)}").join("x")}",
      );
    });
  }
}

/// Generate element values for a location
void _generateElementValues(xml.XmlBuilder builder, SimulationLocation loc) {
  // Generate sprite element values
  for (final sprite in loc.sprites) {
    builder.element("elementVal", nest: () {
      builder.attribute("id", sprite.id);
      builder.text(getSpriteInnerText(sprite));
    });
  }

  // Generate image element values
  for (final pic in loc.images) {
    builder.element("elementVal", nest: () {
      builder.attribute("id", pic.id);
      builder.text(getPicInnerText(pic));
    });
  }

  // Generate text element values
  for (final text in loc.texts) {
    builder.element("elementVal", nest: () {
      builder.attribute("id", text.id);
      builder.text(getTextInnerText(text));
    });
  }

  // Generate shape element values
  for (final shape in loc.shapes) {
    builder.element("elementVal", nest: () {
      builder.attribute("id", shape.id);
      builder.text(getSimShapeInnerText(shape));
    });
  }

  // Generate location jumper element values
  for (final locJumper in loc.jumpers) {
    builder.element("elementVal", nest: () {
      builder.attribute("id", locJumper.id);
      builder.text(getLocationJumperInnerText(locJumper));
    });
  }

  // Generate label element values
  for (final label in loc.labels) {
    builder.element("elementVal", nest: () {
      builder.attribute("id", label.id);
      builder.text(getLabelText(label));
    });
  }

  // Generate container element values
  for (final container in loc.containers) {
    builder.element("elementVal", nest: () {
      builder.attribute("id", container.id);
      builder.text(getContainerText(container));
    });
  }

  // Generate person element values
  for (final person in loc.people) {
    builder.element("elementVal", nest: () {
      builder.attribute("id", person.id);
      builder.attribute("name", person.name);
      if (person.filterColor != Colors.transparent) {
        builder.attribute("color", getColorValue(person.filterColor, withAlpha: true));
      }
      builder.text(getPersonText(person));
    });
  }

  // Generate timer element values
  for (final timer in loc.timers) {
    builder.element("elementVal", nest: () {
      builder.attribute("id", timer.id);
      builder.text(getTimerInnerText(timer));
    });
  }
}

/// Generate core plugins (masks, navigation, images, shapes, sounds)
void _generateCorePlugins(xml.XmlBuilder builder, Scenario simValue) {
  if (simValue.masks.isNotEmpty) {
    builder.element("plugin", nest: () {
      builder.attribute("lib", "CSMask_v1_lib.swf");
      builder.attribute("assetIDs", "CSMask");
    });
  }

  if (simValue.navigations.isNotEmpty) {
    builder.element("plugin", nest: () {
      builder.attribute("lib", "LocJumper_v1_lib.swf");
      builder.attribute("assetIDs", "LocJumper");
    });
  }

  if (simValue.locations.firstWhereOrNull((loc) => loc.images.isNotEmpty) != null) {
    builder.element("plugin", nest: () {
      builder.attribute("lib", "CSPic_v1_lib.swf");
      builder.attribute("assetIDs", "CSPic");
    });
  }

  if (simValue.locations.firstWhereOrNull((loc) => loc.shapes.isNotEmpty) != null) {
    builder.element("plugin", nest: () {
      builder.attribute("lib", "CSShape_v1_lib.swf");
      builder.attribute("assetIDs", "CSShape");
    });
  }

  if (simValue.locations.firstWhereOrNull((loc) => loc.sounds.isNotEmpty) != null) {
    builder.element("plugin", nest: () {
      builder.attribute("lib", "AudioClip_v1_lib.swf");
      builder.attribute("assetIDs", "AudioClip");
    });
  }
}

/// Generate sprite plugins
void _generateSpritePlugins(xml.XmlBuilder builder, Scenario simValue) {
  final allSprites = <String, bool>{};
  for (final loc in simValue.locations) {
    for (var sprite in loc.sprites) {
      allSprites[sprite.assetName] = true;
    }
  }

  for (final assetName in allSprites.keys) {
    builder.element("plugin", nest: () {
      builder.attribute("lib", "${assetName}_v1_lib.swf");
      builder.attribute("assetIDs", assetName);
    });
  }
}

/// Generate people plugins
void _generatePeoplePlugins(xml.XmlBuilder builder, Scenario simValue) {
  final allVictims = <String, bool>{};
  for (final loc in simValue.locations) {
    for (var person in loc.people) {
      allVictims[peopleToAssetMapping[person.type]!] = true;
    }
  }

  for (final assetName in allVictims.keys) {
    builder.element("plugin", nest: () {
      builder.attribute("lib", "${assetName}_v1_lib.swf");
      builder.attribute("assetIDs", assetName);
    });
  }
}

/// Generate trigger actions section
void _generateTriggerActions(xml.XmlBuilder builder) {
  builder.element("triggeractions", nest: () {
    builder.element("actions");
    builder.element("triggers");
    builder.element("mappings");
  });
}

String getTimingTriggerDefValue(SimObject simObj) {
  return ParserUtils.getTimingTriggerDefValue(simObj);
}

String getSpriteInnerText(SimSprite sprite) {
  String timingTrigger = getTimingTriggerDefValue(sprite);
  // out,0,0,brightness,RGB-color,color-opacity,opacity,framerate,speed,fadeInWhen,fadeInDuration,mirrorX,mirrorY,widthScale,fadeOut,fadeOutWhen,fadeOutDuration,timingTrigger
  // NOTE: brightness will always be set to zero because it might need an extra post-processing step to calculate the brightness to the RGB color
  return "out,0,0,1,${getColorValue(sprite.filterColor)},${sprite.filterColor.opacity},${sprite.opacity},${(sprite.framerate * sprite.speed).toPrecision(2)},${sprite.fadeInWhen},${sprite.fadeInDuration},${sprite.mirrorX},${sprite.mirrorY},${sprite.widthScale},${sprite.fadeOut},${sprite.fadeOutWhen},${sprite.fadeOutDuration},$timingTrigger";
}

String getLocationJumperInnerText(SimLocationJumper jumper) {
  String shapeNumber = "0";
  switch (jumper.shape) {
    case "arrow":
    case "arrow-1":
      shapeNumber = "0"; // NOTE: there are 4 variants of arrow from 1 to 4. We only have arrow-1
      break;
    case "arrow-2":
      shapeNumber = "1";
      break;
    case "arrow-3":
      shapeNumber = "2";
      break;
    case "arrow-4":
      shapeNumber = "3";
      break;
    case "rectangle":
      shapeNumber = "4";
      break;
    case "square":
      shapeNumber = "5";
      break;
    case "circle":
      shapeNumber = "6";
      break;
    case "triangle": // NOTE: this is not supported in old players
      shapeNumber = "7";
      break;
    case "rounded-rectangle": // NEW SHAPE
      shapeNumber = "8";
      break;
    default:
      throw "Invalid shape: ${jumper.shape}";
  }
  final hsl = HSLColor.fromColor(jumper.filterColor);
  /* starting ${jumper.fadeInWhen + jumper.fadeInDuration > 0}, these are new add-ons */
  /* ${jumper.fadeInWhen + jumper.fadeInDuration > 0} checks if either is larger than 0 */
  return "in,${jumper.to},$shapeNumber,${jumper.delay},${(jumper.delay > 0).toString()},${jumper.clickable.toString()},${hsl.lightness.toPrecision(3)},${hsl.hue},${hsl.saturation},${hsl.alpha.toPrecision(3)},${jumper.mirrorY.toString()},${jumper.mirrorX.toString()},${(jumper.widthScale)},${jumper.fadeOut},${jumper.fadeOutWhen},${jumper.fadeOutDuration},${jumper.blur},${getTimingTriggerDefValue(jumper)},${jumper.fadeInWhen + jumper.fadeInDuration > 0},${jumper.fadeInWhen},${jumper.fadeInDuration}";
}

String getShapeInnerText(SimShape shape) {
  final hsl = HSLColor.fromColor(shape.filterColor);
  return "out,0,0,${hsl.lightness.toPrecision(3)},${hsl.hue},${hsl.saturation},${hsl.alpha.toPrecision(3)},${shape.shape},${shape.fadeInWhen},${shape.fadeInDuration},${shape.mirrorX},${shape.mirrorY},${shape.widthScale},${shape.fadeOut},${shape.fadeOutWhen},${shape.fadeOutDuration},${shape.blur},${getTimingTriggerDefValue(shape)}";
}

String getPicInnerText(SimImage pic) {
  // final hsl = HSLColor.fromColor(pic.filterColor);
  // TODO: missing implementation for last 3 values
  // TODO: check if default value of "out" (index 0) is responsible for showing the image on start
  return "${pic.hideOnStart ? "in" : "out"},0,0,1,${getColorValue(pic.filterColor)},${pic.filterColor.opacity},${pic.opacity},${pic.fadeInWhen},${pic.fadeInDuration},${pic.movable},${pic.mirrorX},${pic.mirrorY},${pic.scale.toPrecision(3)},${pic.fadeOut},${pic.fadeOutWhen},${pic.fadeOutDuration},${pic.blur},${getTimingTriggerDefValue(pic)},${pic.clickToToggle},0,0";
}

String getSimShapeInnerText(SimShape shape) {
  // final hsl = HSLColor.fromColor(shape.filterColor);
  // return "out,0,0,${hsl.lightness.toPrecision(3)},${hsl.hue},${hsl.saturation},${hsl.alpha.toPrecision(3)},${shape.fadeInWhen},${shape.fadeInDuration},null,${shape.mirrorX},${shape.mirrorY},${shape.widthScale},${shape.fadeOut},${shape.fadeOutWhen},${shape.fadeOutDuration},${shape.blur},${shape.trigger},0,0,0";
  return "out,0,0,1,${getColorValue(shape.filterColor)},${shape.filterColor.opacity},${shape.opacity},${shape.fadeInWhen},${shape.fadeInDuration},null,${shape.mirrorX},${shape.mirrorY},${shape.widthScale},${shape.fadeOut},${shape.fadeOutWhen},${shape.fadeOutDuration},${shape.blur},${shape.trigger},0,0,0";
}

String getTextInnerText(SimText txt) {
  // return "${Uri.encodeFull(txt.text)},${txt.backgroundColor},${txt.fadeInWhen},${txt.fadeInDuration},${txt.fadeOut.toString()},${txt.fadeOutWhen},${txt.fadeOutDuration},${txt.trigger}";
  return "${Uri.encodeFull(txt.text)},${txt.backgroundColor.toHexString()},${txt.fadeInWhen},${txt.fadeInDuration},${txt.fadeOut.toString()},${txt.fadeOutWhen},${txt.fadeOutDuration},${getTimingTriggerDefValue(txt)}";
}

String getTimerInnerText(SimTimer timer) {
  // NOTE: missing time of day attribute at the end of the string
  return "${timerFormats.indexOf(timer.format)},${timerTypes.indexOf(timer.type)},${timer.startingSecond},${timer.filterColor.value},${timer.fadeInWhen},${timer.fadeInDuration},${timer.fadeOut.toString()},${timer.fadeOutWhen},${timer.fadeOutDuration},${timer.trigger}";
}

void setLocationJumperFromText(SimLocationJumper jumper, List<String> details) {
  String getShape(String shapeNumber) {
    switch (shapeNumber) {
      case "0":
        return "arrow-1";
      case "1":
        return "arrow-2";
      case "2":
        return "arrow-3";
      case "3":
        return "arrow-4";
      case "4":
        return "rectangle";
      case "5":
        return "square";
      case "6":
        return "circle";
      case "7":
        return "triangle";
      case "8": // NEW SHAPE
        return "rounded-rectangle";
    }
    // print("INVALID SHAPE NUMBER: $shapeNumber");
    throw "Invalid shape number: $shapeNumber";
  }

  if (details.length >= 2) {
    jumper.to = details[1];
  }
  if (details.length >= 3) {
    jumper.shape = getShape(details[2]);
  }
  // split this "in,${jumper.to},$shapeNumber,${jumper.delay},${(jumper.delay > 0).toString()},${jumper.clickable.toString()},${hsl.lightness.toPrecision(3)},${hsl.hue},${hsl.saturation},${hsl.alpha.toPrecision(3)},${jumper.mirrorY.toString()},${jumper.mirrorX.toString()},${(jumper.widthScale ?? 0)}";
  // and convert the details based on the index starting from index 7
  if (details.length > 3) {
    jumper.delay = int.parse(details[3]);
  }
  if (details.length > 5) {
    jumper.clickable = details[5] == "true";
  }
  if (details.length > 6) {
    jumper.filterColor =
        HSLColor.fromAHSL(double.parse(details[9]), double.parse(details[7]), double.parse(details[8]), double.parse(details[6])).toColor();
  }
  if (details.length >= 11) {
    jumper.mirrorY = details[10] == "true";
  }
  if (details.length >= 12) {
    jumper.mirrorX = details[11] == "true";
  }
  if (details.length >= 13) {
    jumper.widthScale = double.parse(details[12]);
  }
}

double getInitialSize() {
  return ParserUtils.getInitialSize();
}

int getColorValue(Color c, {bool withAlpha = false}) {
  return ColorUtils.getColorValue(c, withAlpha: withAlpha);
}

Color getColorFromValue(int value, {bool withAlpha = false}) {
  return ColorUtils.getColorFromValue(value, withAlpha: withAlpha);
}
