import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:simsushare_player/components/ConfirmationDialog.dart';
import 'package:simsushare_player/components/LoginDialog.dart';
import 'package:simsushare_player/components/UploadSimDialog.dart';
import 'package:simsushare_player/controllers/UserController.dart';

import 'package:simsushare_player/flame/Labels/builder.dart';
import 'package:archive/archive_io.dart';
import 'package:file_picker/file_picker.dart' show FileType;
import 'package:path_provider/path_provider.dart';
import 'package:simsushare_player/components/ContentDialog.dart';
import 'package:simsushare_player/components/DownloadSimDialog.dart';
import 'package:simsushare_player/components/MainButtons.dart';
import 'package:simsushare_player/components/RepositoriesDialog.dart';
import 'package:simsushare_player/controllers/SimController.dart';
import 'package:simsushare_player/models/SimObjects.dart';
import 'package:simsushare_player/models/Simulation.dart';
import 'package:simsushare_player/utils/picker.dart';

final isMobile = !kIsWeb && (Platform.isAndroid || Platform.isIOS);
bool get isMobileScreen => isMobile || Get.width < 700;

extension MobileScreen on BuildContext {
  bool get isMobileScreen => Get.width < 700 || isMobile;
}

const yellow = Color(0xFFE9BB41);
const sidebarDark = Color(0xFF101213);
const lightBackgrounds = Color(0xFF1A1D21);
const mainBackgrounds = Color(0xFF131517);
const bordersColor = Color(0xFF25292E);
const whiteBordersColor = Color(0xFF424349);
const white60 = Color(0x99FFFFFF);
const brick = Color(0xFFC75626);
const navigationGrey = Color(0xFF25292E);

const appWidth = 1200;
const appHeight = 800;
// const editorWidth = 930;
// const editorHeight = 620;
const editorWidth = 984; // OLD VALUE: 990
const editorHeight = 656; // OLD VALUE: 660
const previewToEditorRatio = appWidth / editorWidth;

const popupMenuIconSize = 20.0;
const popupMenuIconColor = Colors.white;
const popMenuTextStyle = TextStyle(color: Colors.white, fontSize: 16);
const headingTextStyle = TextStyle(color: Colors.white, fontSize: 20);
const subheadingTextStyle = TextStyle(color: white60, fontSize: 14);

const unselectedDrawerItemTextStyle = TextStyle(
  color: Color.fromARGB(95, 255, 255, 255),
  fontSize: 15,
  fontWeight: FontWeight.w400,
);

const selectedDrawerItemTextStyle = TextStyle(
  color: yellow,
  fontSize: 15,
  fontWeight: FontWeight.w400,
);

const tileContentPadding = EdgeInsets.only(top: 16, bottom: 16, right: 0, left: 39);

const tileTrailingDecoration = VerticalDivider(color: Color(0xFFE9BB41), thickness: 3);

const drawerItemsTextStyle = TextStyle(
  color: Color.fromARGB(95, 255, 255, 255),
  fontSize: 14,
);

const defaultTextStyle = TextStyle(
  color: Colors.white,
);

const notificationDialogTextStyle = TextStyle(
  color: Colors.white,
  fontSize: 24,
);

const scenarioMoreActions = [
  if (!kIsWeb) "Add Map",
  if (!kIsWeb) "Clone",
  if (!kIsWeb) "Delete",
  "Rename",
  "Upload",
];
const scenarioMoreIcons = [Icons.map, Icons.file_copy, Icons.delete, Icons.edit_note, Icons.upload_rounded];
const generalMoreActions = [
  if (!kIsWeb) "Merge Simulations",
  if (!kIsWeb) "Import Simulation",
  "Repositories",
  if (!kIsWeb) "Download Simulations",
  if (!kIsWeb) "Upload Simulations",
  if (!kIsWeb) "Delete Simulations"
];
const generalMoreIcons = [
  Icons.swap_horizontal_circle,
  Icons.drive_folder_upload,
  Icons.cloud_download,
  Icons.list,
  Icons.cloud_upload,
  Icons.delete
];

Future<bool?> generalMoreHandlers(int index, List<Scenario> selectedScenarios) async {
  if (index == 0) return null;

  if (index == 1) {
    try {
      final libPath = (await getApplicationDocumentsDirectory()).path;
      print("Lib Path: " + libPath);
      final zipped = await pickFiles(
        allowMultiple: false,
        type: FileType.custom,
        allowedExtensions: ["zip"],
        // type: FileType.custom,
        // allowedExtensions: ["zip"],
      );
      if (zipped == null) return false;
      // final dir = Directory.fromUri(Uri.directory(path))
      print("Zipped: " + zipped.files[0].name);
      final fileBytes = Platform.isWindows ? File(zipped.files[0].path!).readAsBytesSync() : zipped.files[0].bytes!;
      final archive = ZipDecoder().decodeBytes(fileBytes);
      for (var file in archive.files) {
        print("${file.name} --> ${file.isSymbolicLink}");
      }
      final directoryExists = await Directory(libPath + "/simulations/" + zipped.files[0].name).exists();
      String folderPath = libPath + "/simulations/" + zipped.files[0].name.substring(0, zipped.files[0].name.length - 4); // NOTE: assumes .zip
      if (directoryExists) {
        int i = 0;
        while (await Directory(libPath + "/simulations/" + zipped.files[0].name + " ($i)").exists()) {
          i++;
        }
        folderPath += " ($i)";
      }
      final dir = await Directory(folderPath).create();
      for (final file in archive.files.where((file) => !(file.name.startsWith("__MACOSX") || file.name.startsWith(".")))) {
        if (!file.isFile) continue;
        final filePath = dir.path + "/" + file.name;
        final data = file.content as List<int>;
        await File(filePath).create(recursive: true);
        await File(filePath).writeAsBytes(data);
      }
      await Get.dialog(
        ContentDialog(
          title: "Import Status",
          content: Center(
            child: Text("Import successful: ${folderPath.split("/").last}", style: notificationDialogTextStyle),
          ),
          actions: [
            OrangeButton(
              label: "Ok",
              onPressed: () {
                Get.back();
              },
            ),
          ],
        ),
      );
      return true;
    } catch (err) {
      print(err);
      await Get.dialog(ContentDialog(
        title: "Import Status",
        content: const Center(
          child: Text("Import Failed", style: notificationDialogTextStyle),
        ),
        actions: [
          OrangeButton(
            label: "Ok",
            onPressed: () {
              Get.back();
            },
          ),
        ],
      ));
      return false;
    }
  }

  if (index == 2) {
    final _userController = Get.find<UserController>();
    if (_userController.user.value == null) {
      final user = await Get.dialog(LoginDialog());
      if (user == null) return null;
      _userController.user.value = user;
    }
    Get.dialog(RepositoriesDialogLarge());
    return null;
  }

  if (index == 3) {
    print("Should go to download sims");
    final _userController = Get.find<UserController>();
    if (_userController.user.value == null) {
      final user = await Get.dialog(LoginDialog());
      if (user == null) return null;
      _userController.user.value = user;
    }
    Get.dialog(DownloadSimDialog());
  }

  if (index == 4) {
    final _userController = Get.find<UserController>();
    if (_userController.user.value == null) {
      final user = await Get.dialog(LoginDialog());
      if (user == null) return null;
      _userController.user.value = user;
    }
    Get.dialog(
      UploadSimDialog(fromSelection: true),
    );
    return null;
  }

  if (index == 5) {
    final _simController = Get.find<SimController>();
    final _userController = Get.find<UserController>();
    if (_userController.selectedSims.isEmpty) return null;
    Future.delayed(const Duration(milliseconds: 500), () {
      Get.dialog(
        ConfirmationDialog(
          message: "Are you sure you want to delete ${_userController.selectedSims.length} simulations?",
          onConfirmed: () async {
            int failed = 0;
            await Future.wait(_userController.selectedSims.values.map((sim) async {
              if (sim.directoryPath.isEmpty) {
                print("Empty path for sim: ${sim.name} with ID: ${sim.id}");
                failed++;
                return;
              }
              final dir = Directory(sim.directoryPath);
              if (!(await dir.exists())) {
                failed++;
                return;
              }
              await dir.delete(recursive: true);
              _simController.signalStream.add("get-sims");
              return;
            }));
            Get.back();
            if (failed > 0) {
              Get.showSnackbar(
                GetSnackBar(
                  title: "Error",
                  message: "Failed to delete $failed scenario(s)",
                  duration: const Duration(seconds: 3),
                ),
              );
              return;
            }
            Get.showSnackbar(
              GetSnackBar(
                title: "Success",
                message: "Successfully deleted ${_userController.selectedSims.length} scenario(s)",
                duration: const Duration(
                  seconds: 3,
                ),
              ),
            );
            _userController.selectedSims.clear();
          },
        ),
      );
    });
    return null;
  }

  return null;
}

const inputOutlinedBorder = OutlineInputBorder(
  borderRadius: BorderRadius.all(Radius.circular(12)),
  borderSide: BorderSide(color: white60, width: 1),
  gapPadding: 12,
);
const textFieldTextStyle = TextStyle(color: Colors.white);

final actionButtonStyle = ElevatedButton.styleFrom(
  textStyle: const TextStyle(color: Colors.white),
  backgroundColor: brick,
  padding: const EdgeInsets.symmetric(horizontal: 28, vertical: 14.5),
  shape: const RoundedRectangleBorder(borderRadius: BorderRadius.all(Radius.circular(50))),
);

const actionButtonsGap = SizedBox(width: 7);

String baseurl = const String.fromEnvironment("BASE_URL", defaultValue: "https://owdev.simsushare.com/api");

Future<void> reassignBaseUrl(String url) async {
  baseurl = url;
  reassignDio(url + "/api");
  final prefs = await SharedPreferences.getInstance();
  await prefs.setString("baseUrl", url);
}

Dio dio = Dio(
  BaseOptions(
    baseUrl: baseurl,
    validateStatus: (status) => status != null && status < 500,
  ),
);

void reassignDio(String url) {
  dio = Dio(
    BaseOptions(
      baseUrl: url,
      validateStatus: (status) => status != null && status < 500,
    ),
  );
}

const spriteCategoryMapping = <String, Map<String, String>>{
  "Fire": {
    // "Crate Wall Fire": "CrateWallFire",
    "Auto Ignition": "AutoIgnition",
    "Basic Fire": "BasicFire",
    "Bright Cloud": "BrightFire",
    "Bright Row": "FireRowBright",
    "Ceiling Fire": "CeilingFire",
    "Fire Burst": "FireBurst",
    "Fire Burst Sustained": "FireBurstSustain",
    "Fire Line Long": "LongFireLine",
    "Fire Track Angled": "FireTrackAngle",
    "Flame Jet": "FlameJet",
    "Flame Burst": "FlameBurst",
    "Flame Front": "WinFlameFront",
    "Flame Side": "WinFlameSide",
    "Front View Smoke and Fire": "FrontViewSmokeFire",
    "Ground Fire Ignition": "GroundFireIgnition",
    "Side View Smoke and Fire": "SideViewSmokeFire",
    "Slow Wide Ground Fire": "SlowWideGroundFire",
    "Small Ground Fire": "SmallGroundFire",
    "Wall Fire": "WallFire",
    "Wall Fire 36": "WallFire36",
    "Wide Row": "FireRow",
  },
  "Smoke": {
    "Angry Thin": "AngryThinSmoke",
    "Auto Ignition": "AutoIgnition",
    "Distant Plume": "DistantPlume",
    "Eaves": "Eaves",
    "Front View Smoke": "FrontViewSmoke",
    "Front View Smoke and Fire": "FrontViewSmokeFire",
    "Gas Approach": "GasApproach",
    "Ground Smoke Rising": "GroundSmokeRising",
    "Laminar": "GreyLaminar",
    "Side View Smoke": "SideViewSmoke",
    "Side View Smoke and Fire": "SideViewSmokeFire",
    "Thin Dark Smoke": "ThinDarkSmoke",
    "Turbulent": "GreyTurbulent",
    "Wide Plume": "WidePlume",
    "Wide Smoke": "BlenderWideSmoke",
    "Smokey Atmosphere": "SmokeyAtmosphere",
  },
  "Containers": {
    "5 Gallon Pail": "",
    "General Duty Metal": "",
    "Open Head Ring Top": "",
    "Poly Drum": "",
  },
  "Explosions": {
    "Basic Explosion": "AW1Explosion",
    "Bright Explosion": "AW2Explosion",
  },
  "HazMat": {
    "Gas Approach": "GasApproach",
    "Pooling Liquid": "",
    "Spark Burst": "SparkBurst",
    "Spout Leak": "SpoutLeak",
    "Steam": "Steam",
    "Steam Burst": "SteamBurst",
  },
  "Labels": {
    "Corrosive": "",
    "Explosives": "",
    "Flammable Gas": "",
    "Flammable Liquid": "",
    "Flammable Solid": "",
    "Hazard ID Num": "",
    "NFPA 704": "",
    "Non-Flammable Gas": "",
    "Oxidizer-Org Perox": "",
    "Oxidizing Gas": "",
    "Radioactive": "",
    "Specialty": "",
    "Substances Class 9": "",
    "Tox-Corr Gas": "",
    "Tocix-Corr Substance": "",
  },
  "People": {
    "Firefighter": "FFVictim-frames.png",
    "Resp Level A": "RespLevelA-frames.png",
    "Resp Level B": "RespLevelB-frames.png",
    "Boy": "VictimBoy-frames.png",
    "Girl": "VictimGirl-frames.png",
    "Man": "VictimMan-frames.png",
    "Woman": "VictimWoman-frames.png",
  }
};

const spriteCategoryColorMapping = <String, Map<String, Color>>{
  "Fire": {},
  "Smoke": {
    "_default": Colors.white,
  },
  "Containers": {},
  "Explosions": {
    "Basic Explosion": Colors.white,
  },
  "HazMat": {},
  "Labels": {},
  "People": {},
};

final labelToBuilderMapping = {
  "Corrosive": buildCorrosive,
  "Explosives": buildExplosives,
  "Flammable Gas": buildFlamGas,
  "Flammable Liquid": buildFlamLiq,
  "Flammable Solid": buildFlamSolid,
  "Hazard ID Num": buildHazIDNum,
  "NFPA 704": buildNFPA704,
  "Non-Flammable Gas": buildNonFlamGas,
  "Oxidizer-Org Perox": buildOxGas,
  "Oxidizing Gas": buildOxOrg,
  "Radioactive": buildRadioactive,
  "Specialty": buildSpecialty,
  "Substances Class 9": buildSubstances,
  "Tox-Corr Gas": buildToxCorGas,
  "Tocix-Corr Substance": buildToxCorSubst,
};

const labelToTypeMapping = {
  "Corrosive": "Corrosive",
  "Explosives": "Explosives",
  "Flammable Gas": "FlamGas",
  "Flammable Liquid": "FlamLiq",
  "Flammable Solid": "FlamSolid",
  "Hazard ID Num": "HazIDNum",
  "NFPA 704": "NFPA704",
  "Non-Flammable Gas": "NonFlamGas",
  "Oxidizer-Org Perox": "OxOrg",
  "Oxidizing Gas": "OxGas",
  "Radioactive": "Radioactive",
  "Specialty": "Specialty",
  "Substances Class 9": "Substances",
  "Tox-Corr Gas": "ToxCorGas",
  "Tocix-Corr Substance": "ToxCorSubst",
};

const labelOptions = {
  "Corrosive": {
    "Label Type": {
      "type": "dropdown",
      "options": ["Corrosive", "Blank", "Material Number"],
    },
    "Material Number": {
      "type": "string",
      "maxLength": 4,
    },
  },
  "Explosives": {
    "Label Type": {
      "type": "dropdown",
      "options": ["1 A", "1 B", "Explosives Blank", "1.1", "1.2", "1.3", "1.4", "1.5", "1.6", "Splat Alone", "Blank"],
    },
  },
  "Flammable Gas": {
    "Label Type": {
      "type": "dropdown",
      "options": ["Regular", "Blank"],
    },
  },
  "Flammable Liquid": {
    "Label Type": {
      "type": "dropdown",
      "options": ["Flammable", "Combustible", "Gasoline", "Fuel Oil", "Material Number", "Residue", "Blank"],
    },
    "Material Number": {
      "type": "string",
      "maxLength": 4,
    },
  },
  "Flammable Solid": {
    "Label Type": {
      "type": "dropdown",
      "options": ["4.1", "4.1 White Back", "4.1 Blank", "4.2", "4.2 Blank", "4.3", "4.3 Blank", "No Wet Solid"],
    },
  },
  "Hazard ID Num": {
    "Label Type": {
      "type": "dropdown",
      "options": ["Orange Back", "White Back"],
    },
    "Hazard Number": {
      "type": "string",
      "maxLength": 4,
    },
  },
  "NFPA 704": {
    "Flammability": {
      "type": "dropdown",
      "options": ["0", "1", "2", "3", "4"],
    },
    "Health": {
      "type": "dropdown",
      "options": ["0", "1", "2", "3", "4"],
    },
    "Instability": {
      "type": "dropdown",
      "options": ["0", "1", "2", "3", "4"],
    },
    "Special Hazard": {
      "type": "dropdown",
      "options": ["None", "No Water", "OX", "Radiation"],
    },
  },
  "Non-Flammable Gas": {
    "Label Type": {
      "type": "dropdown",
      "options": ["Non-Flammable", "Material Number Green", "Blank Green", "Material Number White", "Blank White"],
    },
    "Material Number": {
      "type": "string",
      "maxLength": 4,
    },
  },
  "Oxidizing Gas": {
    "Label Type": {
      "type": "dropdown",
      "options": ["Oxygen", "Blank"],
    },
  },
  "Oxidizer-Org Perox": {
    "Label Type": {
      "type": "dropdown",
      "options": ["5.1 Oxidizer", "5.1 Blank", "5.2 Organic Peroxide", "5.2 Blank"],
    },
  },
  "Radioactive": {
    "Label Type": {
      "type": "dropdown",
      "options": ["Radioactive", "Blank"],
    },
  },
  "Specialty": {
    "Label Type": {
      "type": "dropdown",
      "options": ["Biohazard", "Radiation", "Dangerous", "Danger", "Inhalation Hazard", "Marine Pollutant"],
    },
  },
  "Substances Class 9": {
    "Label Type": {
      "type": "dropdown",
      "options": ["Regular", "No Line"],
    },
  },
  "Tox-Corr Gas": {
    "Label Type": {
      "type": "dropdown",
      "options": ["Poison", "Toxic", "Material Number Black", "Material Number White", "Blank White", "Blank Black", "Inhalation Hazard"],
    },
    "Material Number": {
      "type": "string",
      "maxLength": 4,
    },
  },
  "Tocix-Corr Substance": {
    "Label Type": {
      "type": "dropdown",
      "options": ["Poison", "Toxic", "Blank White", "Blank Black", "Inhalation", "PG3"],
    },
  },
};

const containerViewsMapping = {
  "5 Gallon Pail": ["Upright"],
  "General Duty Metal": ["Upright", "Toppled"],
  "Open Head Ring Top": ["Open", "Closed"],
  "Poly Drum": ["Upright"],
};

const containerAssetsMapping = {
  "5 Gallon Pail": "5GallonPail",
  "General Duty Metal": "GeneralDutyMetal",
  "Open Head Ring Top": "OpenHeadRingTop",
  "Poly Drum": "PolyDrum",
};

const assetToPeopleMapping = {
  "FFVictim": "Firefighter",
  "RespLevelA": "Resp Level A",
  "RespLevelB": "Resp Level B",
  "VictimBoy": "Boy",
  "VictimGirl": "Girl",
  "VictimMan": "Man",
  "VictimWoman": "Woman",
};

const peopleToAssetMapping = {
  "Firefighter": "FFVictim",
  "Resp Level A": "RespLevelA",
  "Resp Level B": "RespLevelB",
  "Boy": "VictimBoy",
  "Girl": "VictimGirl",
  "Man": "VictimMan",
  "Woman": "VictimWoman",
};

const peoplePostureCount = {
  "FFVictim": 4,
  "RespLevelA": 1,
  "RespLevelB": 3,
  "VictimBoy": 4,
  "VictimGirl": 4,
  "VictimMan": 6,
  "VictimWoman": 6,
};

const peoplePostures = {
  "FFVictim": ["Prone", "Supine", "Crawling", "Two Crawling"],
  "RespLevelA": ["Side"],
  "RespLevelB": ["Back", "Side", "Front"],
  "VictimBoy": ["Prone", "Supine", "Crawling", "At window"],
  "VictimGirl": ["Prone", "Supine", "Crawling", "At window"],
  "VictimMan": ["Prone", "Prone towards camera", "On side", "Crawling", "At window", "Standing"],
  "VictimWoman": ["Prone", "Prone towards camera", "On side", "Crawling", "At window", "Standing"],
};

const postureName = ["Prone", "Prone towards camera", "On side", "Crawling", "At window", "Standing"];

const timerTypesMapping = {
  "countdown": "Countdown",
  "exercise": "Exercise",
  "location": "Location",
  "timeOfDay": "Time Of Day",
  "state": "State",
};

const timerFormatsMapping = {
  "minuteSeconds": "MM:SS",
  "hourMinuteSeconds": "HH:MM:SS",
};

const timerTypes = [
  SimTimerType.countdown,
  SimTimerType.exercise,
  SimTimerType.location,
  SimTimerType.timeOfDay,
  SimTimerType.state,
];

const timerFormats = [
  SimTimerFormat.minuteSeconds,
  SimTimerFormat.hourMinuteSeconds,
];
