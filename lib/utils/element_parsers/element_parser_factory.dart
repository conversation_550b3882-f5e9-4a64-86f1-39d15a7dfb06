/// Factory for creating and managing element parsers
/// Provides a centralized way to get the appropriate parser for each element type
/// and handles the parsing orchestration.

import 'package:xml/xml.dart' as xml;

import 'package:simsushare_player/models/SimObjects.dart';
import 'package:simsushare_player/utils/parser_base.dart';
import 'package:simsushare_player/utils/parser_constants.dart' as parser_constants;

import 'package:simsushare_player/utils/element_parsers/image_parser.dart';
import 'package:simsushare_player/utils/element_parsers/shape_parser.dart';
import 'package:simsushare_player/utils/element_parsers/text_parser.dart';
import 'package:simsushare_player/utils/element_parsers/audio_parser.dart';
import 'package:simsushare_player/utils/element_parsers/sprite_parser.dart';
import 'package:simsushare_player/flame/Containers/parser.dart' as container_parser;
import 'package:simsushare_player/flame/People/parser.dart' as person_parser;
import 'package:simsushare_player/flame/Labels/parser.dart' as label_parser;
import 'package:simsushare_player/utils/constants.dart';
import 'package:collection/collection.dart';

/// Factory class for creating element parsers
class ElementParserFactory {
  static final Map<String, ElementParser> _parsers = {};

  /// Initialize all parsers
  static void initialize() {
    _parsers.clear();

    // Register standard element parsers
    _registerParser(ImageParser());
    _registerParser(ShapeParser());
    _registerParser(LocJumperParser());
    _registerParser(TextParser());
    _registerParser(TimerParser());
    _registerParser(AudioParser());
    _registerParser(SpriteParser());
  }

  /// Register a parser for a specific element type
  static void _registerParser(ElementParser parser) {
    _parsers[parser.elementType.xmlName] = parser;
  }

  /// Get parser for element type
  static ElementParser? getParser(String elementType) {
    return _parsers[elementType];
  }

  /// Parse an element using the appropriate parser
  static Future<SimObject?> parseElement(
    xml.XmlElement element,
    xml.XmlDocument simdef,
    Map<String, dynamic> context,
  ) async {
    final elementType = element.getAttribute("type");
    if (elementType == null) return null;

    // Handle special cases that use existing parsers
    switch (elementType) {
      case "Container":
        return await _parseContainer(element, simdef, context);
      case "Person":
        return await _parsePerson(element, simdef, context);
      case "Label":
        return await _parseLabel(element, simdef, context);
      case "Sprite":
        return await _parseSprite(element, simdef, context);
    }

    // Use registered parsers for standard elements
    final parser = getParser(elementType);
    if (parser == null) {
      print("Warning: No parser found for element type: $elementType");
      return null;
    }

    try {
      // Special handling for parsers that require async operations
      if (parser is ImageParser) {
        return await parser.parseElementAsync(element, simdef, context);
      } else if (parser is SpriteParser) {
        return await parser.parseElementAsync(element, simdef, context);
      }

      return parser.parseElement(element, simdef, context);
    } catch (e) {
      print("Error parsing $elementType element: $e");
      return null;
    }
  }

  /// Generate XML element using the appropriate parser
  static xml.XmlElement? generateElement(
    SimObject object,
    Map<String, dynamic> context,
  ) {
    final elementType = _getElementTypeForObject(object);
    if (elementType == null) return null;

    // Handle special cases
    if (object is SimContainer) {
      return _generateContainer(object, context);
    } else if (object is SimPerson) {
      return _generatePerson(object, context);
    } else if (object is SimLabel) {
      return _generateLabel(object, context);
    } else if (object is SimSprite) {
      return _generateSprite(object, context);
    }

    // Use registered parsers
    final parser = getParser(elementType);
    if (parser == null) {
      print("Warning: No parser found for object type: ${object.runtimeType}");
      return null;
    }

    try {
      return parser.generateElement(object as dynamic, context);
    } catch (e) {
      print("Error generating XML for ${object.runtimeType}: $e");
      return null;
    }
  }

  /// Parse container element using existing parser
  static Future<SimContainer?> _parseContainer(
    xml.XmlElement element,
    xml.XmlDocument simdef,
    Map<String, dynamic> context,
  ) async {
    try {
      final elementType = element.getAttribute("type");
      if (elementType == null) return null;

      final elementAttrVal =
          simdef.findAllElements("elementVal").firstWhereOrNull((elementVal) => elementVal.getAttribute("id") == element.getAttribute("id"));

      final containerType = containerAssetsMapping.values.firstWhereOrNull((containerType) => elementType.startsWith(containerType));

      if (containerType != null) {
        return container_parser.parseContainer(
          containerType,
          elementAttrVal?.innerText ?? "",
          element.attributes.fold({}, (previousValue, element) => previousValue..addAll({element.name.toString(): element.value.toString()})),
        );
      }
      return null;
    } catch (e) {
      print("Error parsing Container: $e");
      return null;
    }
  }

  /// Parse person element using existing parser
  static Future<SimPerson?> _parsePerson(
    xml.XmlElement element,
    xml.XmlDocument simdef,
    Map<String, dynamic> context,
  ) async {
    try {
      final elementType = element.getAttribute("type");
      if (elementType == null) return null;

      final elementAttrVal =
          simdef.findAllElements("elementVal").firstWhereOrNull((elementVal) => elementVal.getAttribute("id") == element.getAttribute("id"));

      final personType = assetToPeopleMapping.values.firstWhereOrNull((personType) => elementType == "Victim$personType");

      if (personType != null) {
        return person_parser.parsePerson(
          personType,
          elementAttrVal?.innerText ?? "",
          element.attributes.fold({}, (previousValue, element) => previousValue..addAll({element.name.toString(): element.value.toString()})),
        );
      }
      return null;
    } catch (e) {
      print("Error parsing Person: $e");
      return null;
    }
  }

  /// Parse label element using existing parser
  static Future<SimLabel?> _parseLabel(
    xml.XmlElement element,
    xml.XmlDocument simdef,
    Map<String, dynamic> context,
  ) async {
    try {
      final elementType = element.getAttribute("type");
      if (elementType == null) return null;

      final elementAttrVal =
          simdef.findAllElements("elementVal").firstWhereOrNull((elementVal) => elementVal.getAttribute("id") == element.getAttribute("id"));

      final labelType = labelToTypeMapping.values.firstWhereOrNull((labelType) => elementType.endsWith(labelType));

      if (labelType != null) {
        return label_parser.parseLabel(
          labelType,
          elementAttrVal?.innerText ?? "",
          element.attributes.fold({}, (previousValue, element) => previousValue..addAll({element.name.toString(): element.value.toString()})),
        );
      }
      return null;
    } catch (e) {
      print("Error parsing Label: $e");
      return null;
    }
  }

  /// Parse sprite element using existing parser
  static Future<SimSprite?> _parseSprite(
    xml.XmlElement element,
    xml.XmlDocument simdef,
    Map<String, dynamic> context,
  ) async {
    try {
      // Note: This would need to be implemented based on existing sprite parsing logic
      print("Warning: Sprite parsing not yet implemented in factory");
      return null;
    } catch (e) {
      print("Error parsing Sprite: $e");
      return null;
    }
  }

  /// Generate container XML using existing parser
  static xml.XmlElement? _generateContainer(
    SimContainer container,
    Map<String, dynamic> context,
  ) {
    try {
      // Note: This would need to be implemented based on existing container generation logic
      print("Warning: Container generation not yet implemented in factory");
      return null;
    } catch (e) {
      print("Error generating Container XML: $e");
      return null;
    }
  }

  /// Generate person XML using existing parser
  static xml.XmlElement? _generatePerson(
    SimPerson person,
    Map<String, dynamic> context,
  ) {
    try {
      // Note: This would need to be implemented based on existing person generation logic
      print("Warning: Person generation not yet implemented in factory");
      return null;
    } catch (e) {
      print("Error generating Person XML: $e");
      return null;
    }
  }

  /// Generate label XML using existing parser
  static xml.XmlElement? _generateLabel(
    SimLabel label,
    Map<String, dynamic> context,
  ) {
    try {
      // Note: This would need to be implemented based on existing label generation logic
      print("Warning: Label generation not yet implemented in factory");
      return null;
    } catch (e) {
      print("Error generating Label XML: $e");
      return null;
    }
  }

  /// Generate sprite XML using existing parser
  static xml.XmlElement? _generateSprite(
    SimSprite sprite,
    Map<String, dynamic> context,
  ) {
    try {
      // Note: This would need to be implemented based on existing sprite generation logic
      print("Warning: Sprite generation not yet implemented in factory");
      return null;
    } catch (e) {
      print("Error generating Sprite XML: $e");
      return null;
    }
  }

  /// Get element type string for a SimObject
  static String? _getElementTypeForObject(SimObject object) {
    if (object is SimImage) return "CSPic";
    if (object is SimShape) return "CSShape";
    if (object is SimLocationJumper) return "LocJumper";
    if (object is SimText) return "CSText";
    if (object is SimTimer) return "CSTimer";
    if (object is SimSound) return "AudioClip";
    if (object is SimContainer) return "Container";
    if (object is SimPerson) return "Person";
    if (object is SimLabel) return "Label";
    if (object is SimSprite) return "Sprite";

    return null;
  }

  /// Get all registered parser types
  static List<String> getRegisteredTypes() {
    return _parsers.keys.toList();
  }

  /// Check if a parser is registered for the given type
  static bool hasParser(String elementType) {
    return _parsers.containsKey(elementType);
  }
}
