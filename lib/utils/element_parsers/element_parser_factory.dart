/// Factory for creating and managing element parsers
/// Provides a centralized way to get the appropriate parser for each element type
/// and handles the parsing orchestration.

import 'package:xml/xml.dart' as xml;

import 'package:simsushare_player/models/SimObjects.dart';
import 'package:simsushare_player/utils/parser_base.dart';
import 'package:simsushare_player/utils/parser_constants.dart' as parser_constants;
import 'package:simsushare_player/utils/element_parsers/mask_parser.dart';
import 'package:simsushare_player/utils/element_parsers/image_parser.dart';
import 'package:simsushare_player/utils/element_parsers/shape_parser.dart';
import 'package:simsushare_player/utils/element_parsers/text_parser.dart';
import 'package:simsushare_player/utils/element_parsers/audio_parser.dart';
import 'package:simsushare_player/flame/Containers/parser.dart' as container_parser;
import 'package:simsushare_player/flame/People/parser.dart' as person_parser;
import 'package:simsushare_player/flame/Labels/parser.dart' as label_parser;

/// Factory class for creating element parsers
class ElementParserFactory {
  static final Map<String, ElementParser> _parsers = {};
  
  /// Initialize all parsers
  static void initialize() {
    _parsers.clear();
    
    // Register standard element parsers
    _registerParser(ImageParser());
    _registerParser(MaskParser());
    _registerParser(ShapeParser());
    _registerParser(LocJumperParser());
    _registerParser(TextParser());
    _registerParser(TimerParser());
    _registerParser(AudioParser());
  }
  
  /// Register a parser for a specific element type
  static void _registerParser(ElementParser parser) {
    _parsers[parser.elementType.xmlName] = parser;
  }
  
  /// Get parser for element type
  static ElementParser? getParser(String elementType) {
    return _parsers[elementType];
  }
  
  /// Parse an element using the appropriate parser
  static Future<SimObject?> parseElement(
    xml.XmlElement element,
    xml.XmlDocument simdef,
    Map<String, dynamic> context,
  ) async {
    final elementType = element.getAttribute("type");
    if (elementType == null) return null;
    
    // Handle special cases that use existing parsers
    switch (elementType) {
      case "Container":
        return await _parseContainer(element, simdef, context);
      case "Person":
        return await _parsePerson(element, simdef, context);
      case "Label":
        return await _parseLabel(element, simdef, context);
      case "Sprite":
        return await _parseSprite(element, simdef, context);
    }
    
    // Use registered parsers for standard elements
    final parser = getParser(elementType);
    if (parser == null) {
      print("Warning: No parser found for element type: $elementType");
      return null;
    }
    
    try {
      return parser.parseElement(element, simdef, context);
    } catch (e) {
      print("Error parsing $elementType element: $e");
      return null;
    }
  }
  
  /// Generate XML element using the appropriate parser
  static xml.XmlElement? generateElement(
    SimObject object,
    Map<String, dynamic> context,
  ) {
    final elementType = _getElementTypeForObject(object);
    if (elementType == null) return null;
    
    // Handle special cases
    if (object is Container) {
      return _generateContainer(object, context);
    } else if (object is Person) {
      return _generatePerson(object, context);
    } else if (object is Label) {
      return _generateLabel(object, context);
    } else if (object is Sprite) {
      return _generateSprite(object, context);
    }
    
    // Use registered parsers
    final parser = getParser(elementType);
    if (parser == null) {
      print("Warning: No parser found for object type: ${object.runtimeType}");
      return null;
    }
    
    try {
      return parser.generateElement(object as dynamic, context);
    } catch (e) {
      print("Error generating XML for ${object.runtimeType}: $e");
      return null;
    }
  }
  
  /// Parse container element using existing parser
  static Future<Container?> _parseContainer(
    xml.XmlElement element,
    xml.XmlDocument simdef,
    Map<String, dynamic> context,
  ) async {
    try {
      return await container_parser.parseContainer(element, simdef, context);
    } catch (e) {
      print("Error parsing Container: $e");
      return null;
    }
  }
  
  /// Parse person element using existing parser
  static Future<Person?> _parsePerson(
    xml.XmlElement element,
    xml.XmlDocument simdef,
    Map<String, dynamic> context,
  ) async {
    try {
      return await person_parser.parsePerson(element, simdef, context);
    } catch (e) {
      print("Error parsing Person: $e");
      return null;
    }
  }
  
  /// Parse label element using existing parser
  static Future<Label?> _parseLabel(
    xml.XmlElement element,
    xml.XmlDocument simdef,
    Map<String, dynamic> context,
  ) async {
    try {
      return await label_parser.parseLabel(element, simdef, context);
    } catch (e) {
      print("Error parsing Label: $e");
      return null;
    }
  }
  
  /// Parse sprite element using existing parser
  static Future<Sprite?> _parseSprite(
    xml.XmlElement element,
    xml.XmlDocument simdef,
    Map<String, dynamic> context,
  ) async {
    try {
      // Note: This would need to be implemented based on existing sprite parsing logic
      print("Warning: Sprite parsing not yet implemented in factory");
      return null;
    } catch (e) {
      print("Error parsing Sprite: $e");
      return null;
    }
  }
  
  /// Generate container XML using existing parser
  static xml.XmlElement? _generateContainer(
    Container container,
    Map<String, dynamic> context,
  ) {
    try {
      return container_parser.generateContainer(container, context);
    } catch (e) {
      print("Error generating Container XML: $e");
      return null;
    }
  }
  
  /// Generate person XML using existing parser
  static xml.XmlElement? _generatePerson(
    Person person,
    Map<String, dynamic> context,
  ) {
    try {
      return person_parser.generatePerson(person, context);
    } catch (e) {
      print("Error generating Person XML: $e");
      return null;
    }
  }
  
  /// Generate label XML using existing parser
  static xml.XmlElement? _generateLabel(
    Label label,
    Map<String, dynamic> context,
  ) {
    try {
      return label_parser.generateLabel(label, context);
    } catch (e) {
      print("Error generating Label XML: $e");
      return null;
    }
  }
  
  /// Generate sprite XML using existing parser
  static xml.XmlElement? _generateSprite(
    Sprite sprite,
    Map<String, dynamic> context,
  ) {
    try {
      // Note: This would need to be implemented based on existing sprite generation logic
      print("Warning: Sprite generation not yet implemented in factory");
      return null;
    } catch (e) {
      print("Error generating Sprite XML: $e");
      return null;
    }
  }
  
  /// Get element type string for a SimObject
  static String? _getElementTypeForObject(SimObject object) {
    if (object is SimImage) return "CSPic";
    if (object is Mask) return "CSMask";
    if (object is SimShape) return "CSShape";
    if (object is SimLocJumper) return "LocJumper";
    if (object is SimText) return "CSText";
    if (object is SimTimer) return "CSTimer";
    if (object is SimAudio) return "AudioClip";
    if (object is Container) return "Container";
    if (object is Person) return "Person";
    if (object is Label) return "Label";
    if (object is Sprite) return "Sprite";
    
    return null;
  }
  
  /// Get all registered parser types
  static List<String> getRegisteredTypes() {
    return _parsers.keys.toList();
  }
  
  /// Check if a parser is registered for the given type
  static bool hasParser(String elementType) {
    return _parsers.containsKey(elementType);
  }
}
