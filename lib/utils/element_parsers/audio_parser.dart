/// Specialized parser for AudioClip elements
/// Handles parsing and generation of audio elements with proper validation
/// and error handling.

import 'dart:io';
import 'package:xml/xml.dart' as xml;
import 'package:path/path.dart' as path;
import 'package:collection/collection.dart';

import 'package:simsushare_player/models/SimObjects.dart';
import 'package:simsushare_player/utils/parser_base.dart';
import 'package:simsushare_player/utils/parser_constants.dart' as parser_constants;
import 'package:simsushare_player/utils/parser_utilities.dart';

/// Parser for AudioClip elements
class AudioParser extends ElementParser<SimSound> {
  @override
  parser_constants.ElementType get elementType => parser_constants.ElementType.audioClip;

  @override
  SimSound parseElement(
    xml.XmlElement element,
    xml.XmlDocument simdef,
    Map<String, dynamic> context,
  ) {
    final props = extractCommonProperties(element);
    final dir = context["directory"] as Directory?;
    final allFileNames = context["allFileNames"] as List<String>? ?? [];

    if (dir == null) {
      throw ArgumentError("Directory context is required for audio parsing");
    }

    final audioName = props["file"];
    if (audioName == null || audioName.isEmpty) {
      throw ArgumentError(parser_constants.ParserErrors.missingFileName);
    }

    // Find the full file name
    final fullFileName = allFileNames.firstWhereOrNull(
      (fileName) => fileName.startsWith(audioName),
    );

    if (fullFileName == null) {
      throw ArgumentError("${parser_constants.ParserErrors.fileNotFound}: $audioName");
    }

    final audioFile = File(path.join(dir.path, fullFileName));
    final details = _parseElementDetails(element, simdef);

    return SimSound(
      id: props["id"] ?? "",
      path: audioFile.path,
      x: ParserUtils.parseDouble(props["x"], fallback: 0.0),
      y: ParserUtils.parseDouble(props["y"], fallback: 0.0),
      width: ParserUtils.parseDouble(props["scaleX"], fallback: 0.0),
      height: ParserUtils.parseDouble(props["scaleY"], fallback: 0.0),
      loop: ParserUtils.parseBool(props["loop"]),
      mirrorX: ParserUtils.parseBool(props["mirrorX"]),
      mirrorY: ParserUtils.parseBool(props["mirrorY"]),
      rotation: ParserUtils.parseDouble(props["rotation"], fallback: 0.0),
    )
      ..hideOnStart = details.isNotEmpty ? details[0] == "in" : true
      ..trigger = details.length >= 17 ? ParserUtils.parseTimingTrigger(details[17]) : "scenario"
      ..priority = ParserUtils.parseInt(props["priority"], fallback: 1)
      ..triggerOnce = ParserUtils.parseBool(props["triggerOnce"])
      ..clickToToggle = details.length >= 18 ? ParserUtils.parseBool(details[18]) : false;
  }

  @override
  xml.XmlElement generateElement(SimSound audio, Map<String, dynamic> context) {
    final builder = xml.XmlBuilder();

    builder.element("element", nest: () {
      builder.attribute("type", elementType.xmlName);
      builder.attribute("id", audio.id);
      builder.attribute("file", path.basename(audio.path));
      builder.attribute("x", audio.x.toString());
      builder.attribute("y", audio.y.toString());
      builder.attribute("scaleX", audio.width.toString());
      builder.attribute("scaleY", audio.height.toString());
      builder.attribute("loop", audio.loop.toString());
      builder.attribute("mirrorX", audio.mirrorX.toString());
      builder.attribute("mirrorY", audio.mirrorY.toString());
      builder.attribute("rotation", audio.rotation.toString());
      builder.attribute("priority", audio.priority.toString());

      if (audio.triggerOnce) {
        builder.attribute("triggerOnce", "true");
      }

      // Add fade details as inner text
      final fadeDetails = [
        audio.hideOnStart ? "in" : "out",
        audio.fadeInDuration.toString(),
        audio.fadeOutDuration.toString(),
        audio.fadeInWhen.toString(),
        audio.fadeOutWhen.toString(),
      ];

      builder.text(fadeDetails.join(","));
    });

    return builder.buildFragment().firstElementChild!;
  }

  /// Parse element details from simdef
  List<String> _parseElementDetails(xml.XmlElement element, xml.XmlDocument simdef) {
    final elementVal = simdef.findAllElements("elementVal").firstWhereOrNull((el) => el.getAttribute("id") == element.getAttribute("id"));

    final source = elementVal ?? element;
    return ParserUtils.parseDefinition(source.innerText);
  }

  @override
  bool validateElement(xml.XmlElement element) {
    if (!super.validateElement(element)) return false;

    final fileName = element.getAttribute("file");
    if (fileName == null || fileName.isEmpty) return false;

    // Validate audio file extension
    final validExtensions = ['.mp3', '.wav', '.ogg', '.m4a', '.aac'];
    final extension = path.extension(fileName).toLowerCase();
    return validExtensions.contains(extension);
  }

  /// Check if file is a valid audio file
  static bool isAudioFile(String fileName) {
    final validExtensions = ['.mp3', '.wav', '.ogg', '.m4a', '.aac'];
    final extension = path.extension(fileName).toLowerCase();
    return validExtensions.contains(extension);
  }
}
