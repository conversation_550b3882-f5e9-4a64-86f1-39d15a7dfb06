/// Specialized parser for CSPic (image) elements
/// Handles parsing and generation of image elements with proper validation
/// and error handling.

import 'dart:io';
import 'dart:ui' as ui;
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:xml/xml.dart' as xml;
import 'package:path/path.dart' as path;
import 'package:collection/collection.dart';

import 'package:simsushare_player/models/SimObjects.dart';
import 'package:simsushare_player/utils/parser_base.dart';
import 'package:simsushare_player/utils/parser_constants.dart' as parser_constants;
import 'package:simsushare_player/utils/parser_utilities.dart';

/// Parser for CSPic (image) elements
class ImageParser extends ElementParser<SimImage> {
  
  @override
  parser_constants.ElementType get elementType => parser_constants.ElementType.csPic;
  
  @override
  SimImage parseElement(
    xml.XmlElement element,
    xml.XmlDocument simdef,
    Map<String, dynamic> context,
  ) {
    final props = extractCommonProperties(element);
    final isBackground = ParserUtils.parseBool(props["background"]);
    
    if (isBackground) {
      return _parseBackgroundImage(element, props, context);
    } else {
      return _parseRegularImage(element, simdef, props, context);
    }
  }
  
  @override
  xml.XmlElement generateElement(SimImage image, Map<String, dynamic> context) {
    final builder = xml.XmlBuilder();
    
    builder.element("element", nest: () {
      builder.attribute("type", elementType.xmlName);
      builder.attribute("id", image.id);
      builder.attribute("x", image.x.toString());
      builder.attribute("y", image.y.toString());
      builder.attribute("file", path.basename(image.path));
      builder.attribute("scaleX", image.widthScale.toString());
      builder.attribute("scaleY", image.heightScale.toString());
      builder.attribute("rotation", image.rotation.toStringAsFixed(3));
      builder.attribute("priority", image.priority.toString());
      
      if (image.to.isNotEmpty) {
        builder.attribute("to", image.to);
      }
      
      if (image.syncVariable != null) {
        builder.attribute("syncVar", image.syncVariable);
      }
    });
    
    return builder.buildFragment().firstElementChild!;
  }
  
  /// Parse background image element
  SimImage _parseBackgroundImage(
    xml.XmlElement element,
    Map<String, String> props,
    Map<String, dynamic> context,
  ) {
    final fileName = props["file"];
    if (fileName == null || fileName.isEmpty) {
      throw ArgumentError(parser_constants.ParserErrors.missingFileName);
    }
    
    final dir = context["directory"] as Directory?;
    if (dir == null) {
      throw ArgumentError("Directory context is required for background images");
    }
    
    final imagePath = File(path.join(dir.path, fileName)).path;
    final imageScale = ParserUtils.parseDouble(props["scaleX"], fallback: 1.0);
    final imageOffset = ParserUtils.parseOffset(props["offset"]);
    
    // For background images, we create a placeholder SimImage
    // The actual background handling is done at the location level
    return SimImage(
      id: props["id"] ?? "background",
      img: null, // Will be loaded separately
      path: imagePath,
      x: 0,
      y: 0,
      width: 0,
      height: 0,
      widthScale: imageScale,
      heightScale: imageScale,
      scale: 1.0,
      rotation: 0.0,
      opacity: 1.0,
      mirrorX: false,
      mirrorY: false,
      filterColor: Colors.transparent,
      blur: 0.0,
      to: "",
      syncVariable: null,
    );
  }
  
  /// Parse regular (non-background) image element
  Future<SimImage> _parseRegularImage(
    xml.XmlElement element,
    xml.XmlDocument simdef,
    Map<String, String> props,
    Map<String, dynamic> context,
  ) async {
    final imageName = props["file"];
    if (imageName == null || imageName.isEmpty) {
      throw ArgumentError(parser_constants.ParserErrors.missingFileName);
    }
    
    final dir = context["directory"] as Directory?;
    final allFileNames = context["allFileNames"] as List<String>? ?? [];
    final metadata = context["metadata"] as SimulationMetadata?;
    
    if (dir == null || metadata == null) {
      throw ArgumentError("Required context missing for image parsing");
    }
    
    // Find the full file name
    final fullFileName = allFileNames.firstWhereOrNull(
      (fileName) => fileName.startsWith(imageName),
    );
    
    if (fullFileName == null) {
      throw ArgumentError("${parser_constants.ParserErrors.fileNotFound}: $imageName");
    }
    
    final imageFile = File(path.join(dir.path, imageName));
    final img = await _loadImage(imageFile);
    
    final x = ParserUtils.parseDouble(props["x"]);
    final y = ParserUtils.parseDouble(props["y"]);
    
    // Parse element details from simdef
    final details = _parseElementDetails(element, simdef);
    
    final simImage = SimImage(
      id: props["id"] ?? "",
      img: img,
      path: imageFile.path,
      x: x,
      y: y,
      width: img.width.toDouble(),
      height: img.height.toDouble(),
      widthScale: ParserUtils.parseDouble(props["scaleX"], fallback: 1.0),
      heightScale: ParserUtils.parseDouble(props["scaleY"], fallback: 1.0),
      scale: details.length >= 12 ? ParserUtils.parseDouble(details[12], fallback: 1.0) : 1.0,
      rotation: ParserUtils.parseDouble(props["rotation"]),
      opacity: details.length > 6 ? ParserUtils.parseDouble(details[6], fallback: 1.0) : 1.0,
      mirrorX: details.length > 10 ? ParserUtils.parseBool(details[10]) : false,
      mirrorY: details.length > 11 ? ParserUtils.parseBool(details[11]) : false,
      filterColor: _parseFilterColor(details),
      blur: details.length >= 16 ? ParserUtils.parseDouble(details[16]) : 0.0,
      to: props["to"] ?? "",
      syncVariable: props["syncVar"],
    )
      ..clickToToggle = details.length >= 18 ? ParserUtils.parseBool(details[18]) : false
      ..hideOnStart = details.isNotEmpty ? details[0] == "in" : true
      ..trigger = details.length >= 17 ? ParserUtils.parseTimingTrigger(details[17]) : "scenario"
      ..priority = ParserUtils.parseInt(props["priority"], fallback: 1)
      ..triggerOnce = ParserUtils.parseBool(props["triggerOnce"]);
    
    return simImage;
  }
  
  /// Load image from file
  Future<ui.Image> _loadImage(File imageFile) async {
    if (kIsWeb) {
      return await decodeImageFromList(await imageFile.readAsBytes());
    } else {
      return await decodeImageFromList(await imageFile.readAsBytes());
    }
  }
  
  /// Parse element details from simdef
  List<String> _parseElementDetails(xml.XmlElement element, xml.XmlDocument simdef) {
    final elementVal = simdef
        .findAllElements("elementVal")
        .firstWhereOrNull((el) => el.getAttribute("id") == element.getAttribute("id"));
    
    final source = elementVal ?? element;
    return ParserUtils.parseDefinition(source.innerText);
  }
  
  /// Parse filter color from details
  Color _parseFilterColor(List<String> details) {
    if (details.length <= 5) return Colors.transparent;
    
    try {
      final colorValue = int.parse(details[4]);
      final opacity = double.parse(details[5]);
      return ColorUtils.getColorFromValue(colorValue).withValues(alpha: opacity);
    } catch (e) {
      return Colors.transparent;
    }
  }
  
  @override
  bool validateElement(xml.XmlElement element) {
    if (!super.validateElement(element)) return false;
    
    final fileName = element.getAttribute("file");
    return fileName != null && fileName.isNotEmpty;
  }
}
