/// Specialized parser for mask elements
/// Handles parsing and generation of CSMask elements with proper validation
/// and error handling.

import 'package:xml/xml.dart' as xml;
import 'package:collection/collection.dart';

import 'package:simsushare_player/models/Mask.dart';
import 'package:simsushare_player/models/SimObjects.dart';

import 'package:simsushare_player/utils/parser_constants.dart' as parser_constants;
import 'package:simsushare_player/utils/parser_utilities.dart';

/// Parser for mask elements
class MaskParser {
  parser_constants.ElementType get elementType => parser_constants.ElementType.csMask;

  Mask parseElement(
    xml.XmlElement element,
    xml.XmlDocument simdef,
    Map<String, dynamic> context,
  ) {
    final props = <String, String>{};
    for (final attr in element.attributes) {
      props[attr.name.toString()] = attr.value;
    }
    final definition = element.innerText;

    if (definition.isEmpty) {
      throw ArgumentError(parser_constants.ParserErrors.invalidMaskDefinition);
    }

    final locationId = context["locationId"] as String? ?? "";
    final simWidth = context["simWidth"] as double? ?? 1200.0;
    final simHeight = context["simHeight"] as double? ?? 800.0;

    return _parseMaskFromDefinition(
      props["id"] ?? "",
      definition,
      locationId,
      props,
      simWidth,
      simHeight,
    );
  }

  xml.XmlElement generateElement(Mask mask, Map<String, dynamic> context) {
    final builder = xml.XmlBuilder();
    final simWidth = context["simWidth"] as double? ?? 1200.0;
    final simHeight = context["simHeight"] as double? ?? 800.0;
    final maskSprites = context["maskSprites"] as List<SimObject>? ?? [];

    builder.element("element", nest: () {
      builder.attribute("type", elementType.xmlName);
      builder.attribute("id", mask.id);
      builder.attribute("name", mask.name);
      builder.attribute("x", "0"); // TODO: Add proper mask positioning
      builder.attribute("y", "0");

      // Generate mask definition text
      final coords = _prepareMaskCoordinates(mask, simWidth, simHeight);
      final definition = _generateMaskDefinition(mask, maskSprites, coords);
      builder.text(definition);
    });

    return builder.buildFragment().firstElementChild!;
  }

  /// Parse mask from definition string
  Mask _parseMaskFromDefinition(
    String id,
    String definition,
    String locationId,
    Map<String, String> props,
    double simWidth,
    double simHeight,
  ) {
    final defSplit = ParserUtils.parseDefinition(definition);

    if (defSplit.length < 3) {
      throw ArgumentError("${parser_constants.ParserErrors.invalidMaskDefinition}: insufficient parts");
    }

    // Parse mask type from second element
    final showOutside = ParserUtils.parseBool(defSplit[1]);
    final maskType = showOutside ? MaskType.showOutside : MaskType.showWithin;

    // Parse coordinates from last element
    final coordinates = _parseCoordinatesFromDefinition(
      defSplit.last,
      props,
      simWidth,
      simHeight,
    );

    // Normalize ID with location suffix
    final normalizedId = _normalizeMaskId(id, locationId);

    final mask = Mask(
      type: maskType,
      coordinates: coordinates,
      id: normalizedId,
      name: props["name"] ?? normalizedId,
      locationId: locationId,
    );

    return mask;
  }

  /// Parse coordinates from definition string
  List<Coordinate> _parseCoordinatesFromDefinition(
    String coordString,
    Map<String, String> props,
    double simWidth,
    double simHeight,
  ) {
    final coordinates = ParserUtils.parseCoordinates(coordString);

    // Apply offset if specified in element attributes
    final offsetX = ParserUtils.parseDouble(props["x"]);
    final offsetY = ParserUtils.parseDouble(props["y"]);

    // Normalize coordinates to percentage if needed
    return coordinates.map((coord) {
      final adjustedX = (coord.x + offsetX) / simWidth;
      final adjustedY = (coord.y + offsetY) / simHeight;
      return Coordinate(adjustedX, adjustedY);
    }).toList();
  }

  /// Normalize mask ID with location suffix
  String _normalizeMaskId(String id, String locationId) {
    if (id.endsWith("-$locationId")) {
      return id;
    }
    return "$id-$locationId";
  }

  /// Retrieve sprite IDs from mask definition
  List<String> parseMaskSprites(String definition) {
    final defSplit = ParserUtils.parseDefinition(definition);

    if (defSplit.length < 3) {
      return [];
    }

    final spriteCount = ParserUtils.parseInt(defSplit[2]);
    final spriteIds = <String>[];

    for (int i = 0; i < spriteCount && (3 + i) < defSplit.length; i++) {
      spriteIds.add(defSplit[3 + i]);
    }

    return spriteIds;
  }

  /// Retrieve sim object IDs from mask definition
  List<String> parseSimObjectsInMask(String definition) {
    // This is the same as parseMaskSprites for now
    // but kept separate for potential future differences
    return parseMaskSprites(definition);
  }

  /// Link mask to sprites in location
  void linkMaskToSprites(
    Mask mask,
    List<SimObject> availableObjects,
    String locationId,
    String definition,
  ) {
    final spriteIds = parseMaskSprites(definition);

    for (final spriteId in spriteIds) {
      final sprite = _findSpriteById(spriteId, availableObjects, locationId);
      if (sprite != null) {
        sprite.maskIds.add(mask.id);
      }
    }
  }

  /// Find sprite by ID with flexible matching
  SimObject? _findSpriteById(
    String spriteId,
    List<SimObject> objects,
    String locationId,
  ) {
    return objects.firstWhereOrNull((obj) =>
        obj.id == spriteId ||
        obj.id == "$spriteId-$locationId" ||
        (spriteId.length >= (locationId.length + 1) && obj.id.startsWith(spriteId.substring(0, locationId.length + 1))));
  }

  /// Prepare coordinates for XML generation
  List<Coordinate> _prepareMaskCoordinates(
    Mask mask,
    double simWidth,
    double simHeight,
  ) {
    if (mask.needsParsing()) {
      // Convert from percentage to absolute coordinates
      return mask.coordinates.map((c) => Coordinate(c.x * simWidth, c.y * simHeight)).toList();
    }
    return mask.coordinates;
  }

  /// Generate mask definition string for XML
  String _generateMaskDefinition(
    Mask mask,
    List<SimObject> maskSprites,
    List<Coordinate> coords,
  ) {
    final spriteIds = maskSprites.map((e) => e.id).join(",");
    final spriteSection = maskSprites.isNotEmpty ? ",$spriteIds" : "";

    final coordString = coords.map((coord) => "${coord.x.toStringAsFixed(2)}c${coord.y.toStringAsFixed(2)}").join("x");

    return "true,${mask.type == MaskType.showOutside},${maskSprites.length}$spriteSection,$coordString";
  }

  bool validateElement(xml.XmlElement element) {
    final definition = element.innerText;
    if (definition.isEmpty) return false;

    final defSplit = ParserUtils.parseDefinition(definition);
    return defSplit.length >= 3;
  }
}
