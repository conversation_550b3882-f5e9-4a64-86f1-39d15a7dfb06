/// Specialized parser for sprite elements
/// Handles parsing and generation of sprite elements with proper validation
/// and error handling.

import 'dart:convert';
import 'dart:ui' as ui;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:xml/xml.dart' as xml;
import 'package:collection/collection.dart';

import 'package:simsushare_player/models/SimObjects.dart';
import 'package:simsushare_player/utils/parser_base.dart';
import 'package:simsushare_player/utils/parser_constants.dart' as parser_constants;
import 'package:simsushare_player/utils/parser_utilities.dart';

/// Parser for sprite elements
class SpriteParser extends ElementParser<SimSprite> {
  
  @override
  parser_constants.ElementType get elementType => parser_constants.ElementType.sprite;
  
  @override
  SimSprite parseElement(
    xml.XmlElement element,
    xml.XmlDocument simdef,
    Map<String, dynamic> context,
  ) {
    // This method should not be called directly for SpriteParser
    // Use parseElementAsync instead
    throw UnsupportedError("SpriteParser requires async parsing. Use parseElementAsync instead.");
  }
  
  /// Async version of parseElement for sprite loading
  Future<SimSprite> parseElementAsync(
    xml.XmlElement element,
    xml.XmlDocument simdef,
    Map<String, dynamic> context,
  ) async {
    final props = extractCommonProperties(element);
    final metadata = context["metadata"] as SimulationMetadata?;
    final locationId = context["locationId"] as String? ?? "";
    
    if (metadata == null) {
      throw ArgumentError("Simulation metadata required for sprite parsing");
    }
    
    final elementType = element.getAttribute("type");
    if (elementType == null) {
      throw ArgumentError("Sprite element type is required");
    }
    
    // Find element details from simdef
    final elementAttrVal = simdef
        .findAllElements("elementVal")
        .firstWhereOrNull((el) => el.getAttribute("id") == element.getAttribute("id"));
    
    // Load sprite metadata and image
    final spriteData = await _loadSpriteData(elementType);
    final img = spriteData['image'] as ui.Image;
    final frames = spriteData['frames'] as List<SpriteFrame>;
    final meta = spriteData['meta'] as Map<String, dynamic>;
    
    // Parse sprite details from elementVal
    final spriteDetails = _parseSpriteDetails(elementAttrVal);
    
    // Create sprite ID with location suffix
    final spriteElementId = props["id"] ?? "";
    final spriteId = spriteElementId.endsWith("-$locationId") 
        ? spriteElementId 
        : "$spriteElementId-$locationId";
    
    return SimSprite(
      id: spriteId,
      name: props["name"] ?? spriteElementId,
      img: img,
      frames: frames,
      aseprite: meta,
      assetName: elementType,
      width: double.parse(meta["meta"]["size"]["w"].toString()),
      height: double.parse(meta["meta"]["size"]["h"].toString()),
      scaleFactor: double.tryParse(meta["meta"]["scale"].toString()) ?? 1.0,
      scale: ParserUtils.parseDouble(props["scale"], fallback: 1.0),
      x: ParserUtils.parseDouble(props["x"]) / metadata.width,
      y: ParserUtils.parseDouble(props["y"]) / metadata.height,
      widthScale: ParserUtils.parseDouble(props["scaleX"], fallback: 1.0),
      heightScale: ParserUtils.parseDouble(props["scaleY"], fallback: 1.0),
      rotation: ParserUtils.parseDouble(props["rotation"]),
      filterColor: _parseFilterColor(spriteDetails),
      opacity: _parseOpacity(spriteDetails),
      framerate: _parseFramerate(spriteDetails),
      speed: _parseSpeed(spriteDetails),
      fadeInWhen: _parseFadeInWhen(spriteDetails),
      fadeInDuration: _parseFadeInDuration(spriteDetails),
      mirrorX: _parseMirrorX(spriteDetails),
      mirrorY: _parseMirrorY(spriteDetails),
      pinch: _parsePinch(spriteDetails),
      fadeOut: _parseFadeOut(spriteDetails),
      fadeOutWhen: _parseFadeOutWhen(spriteDetails),
      fadeOutDuration: _parseFadeOutDuration(spriteDetails),
    )
      ..trigger = _parseTrigger(spriteDetails)
      ..priority = ParserUtils.parseInt(props["priority"], fallback: 1)
      ..triggerOnce = ParserUtils.parseBool(props["triggerOnce"])
      ..movable = ParserUtils.parseBool(props["movable"]);
  }
  
  @override
  xml.XmlElement generateElement(SimSprite sprite, Map<String, dynamic> context) {
    final builder = xml.XmlBuilder();
    final metadata = context["metadata"] as SimulationMetadata?;
    
    if (metadata == null) {
      throw ArgumentError("Simulation metadata required for sprite generation");
    }
    
    builder.element("element", nest: () {
      builder.attribute("type", sprite.assetName);
      builder.attribute("id", sprite.id);
      builder.attribute("name", sprite.name);
      builder.attribute("x", (sprite.x * metadata.width).toStringAsFixed(2));
      builder.attribute("y", (sprite.y * metadata.height).toStringAsFixed(2));
      builder.attribute("scale", sprite.scale.toStringAsFixed(2));
      builder.attribute("scaleX", sprite.widthScale.toString());
      builder.attribute("scaleY", sprite.heightScale.toString());
      builder.attribute("rotation", sprite.rotation.toStringAsFixed(3));
      builder.attribute("priority", sprite.priority.toString());
      
      if (sprite.movable) {
        builder.attribute("movable", "true");
      }
      
      if (sprite.triggerOnce) {
        builder.attribute("triggerOnce", "true");
      }
    });
    
    return builder.buildFragment().firstElementChild!;
  }
  
  /// Load sprite data (metadata, frames, and image)
  Future<Map<String, dynamic>> _loadSpriteData(String elementType) async {
    try {
      // Load sprite metadata
      final metaJson = await rootBundle.loadString("assets/sprites/$elementType-frames-high.json");
      final meta = jsonDecode(metaJson) as Map<String, dynamic>;
      
      // Parse frames from metadata
      final frames = (meta["frames"] as Map<String, dynamic>).values.map((e) {
        return SpriteFrame(
          x: (e["frame"]["x"] as int).toDouble(),
          y: (e["frame"]["y"] as int).toDouble(),
          width: (e["frame"]["w"] as int).toDouble(),
          height: (e["frame"]["h"] as int).toDouble(),
          rotated: e["rotated"] as bool,
        );
      }).toList();
      
      // Load sprite image
      final imageData = await rootBundle.load("assets/sprites/$elementType-frames-high.png");
      final img = await decodeImageFromList(imageData.buffer.asUint8List());
      
      return {
        'meta': meta,
        'frames': frames,
        'image': img,
      };
    } catch (e) {
      throw ArgumentError("Failed to load sprite data for $elementType: $e");
    }
  }
  
  /// Parse sprite details from elementVal inner text
  List<String> _parseSpriteDetails(xml.XmlElement? elementAttrVal) {
    if (elementAttrVal == null) return [];
    return ParserUtils.parseDefinition(elementAttrVal.innerText);
  }
  
  /// Parse filter color from sprite details
  Color _parseFilterColor(List<String> details) {
    if (details.length <= 4) return Colors.transparent;
    
    try {
      final colorValue = ParserUtils.parseInt(details[4]);
      final opacity = details.length > 5 ? ParserUtils.parseDouble(details[5], fallback: 1.0) : 1.0;
      return ColorUtils.getColorFromValue(colorValue).withValues(alpha: opacity);
    } catch (e) {
      return Colors.transparent;
    }
  }
  
  /// Parse opacity from sprite details
  double _parseOpacity(List<String> details) {
    return details.length > 6 ? ParserUtils.parseDouble(details[6], fallback: 1.0) : 1.0;
  }
  
  /// Parse framerate from sprite details
  int _parseFramerate(List<String> details) {
    return details.length > 7 ? ParserUtils.parseInt(details[7], fallback: 20) : 20;
  }
  
  /// Parse speed from sprite details
  double _parseSpeed(List<String> details) {
    return details.length > 8 ? ParserUtils.parseDouble(details[8], fallback: 1.0) : 1.0;
  }
  
  /// Parse fade in when from sprite details
  double _parseFadeInWhen(List<String> details) {
    return details.length > 9 ? ParserUtils.parseDouble(details[9], fallback: 0.0) : 0.0;
  }
  
  /// Parse fade in duration from sprite details
  double _parseFadeInDuration(List<String> details) {
    return details.length > 10 ? ParserUtils.parseDouble(details[10], fallback: 0.0) : 0.0;
  }
  
  /// Parse mirror X from sprite details
  bool _parseMirrorX(List<String> details) {
    return details.length > 11 ? ParserUtils.parseBool(details[11]) : false;
  }
  
  /// Parse mirror Y from sprite details
  bool _parseMirrorY(List<String> details) {
    return details.length > 12 ? ParserUtils.parseBool(details[12]) : false;
  }
  
  /// Parse pinch from sprite details
  double? _parsePinch(List<String> details) {
    return details.length > 13 ? ParserUtils.parseDouble(details[13]) : null;
  }
  
  /// Parse fade out from sprite details
  bool _parseFadeOut(List<String> details) {
    return details.length > 14 ? ParserUtils.parseBool(details[14]) : false;
  }
  
  /// Parse fade out when from sprite details
  double _parseFadeOutWhen(List<String> details) {
    return details.length > 15 ? ParserUtils.parseDouble(details[15], fallback: 0.0) : 0.0;
  }
  
  /// Parse fade out duration from sprite details
  double _parseFadeOutDuration(List<String> details) {
    return details.length > 16 ? ParserUtils.parseDouble(details[16], fallback: 0.0) : 0.0;
  }
  
  /// Parse timing trigger from sprite details
  String _parseTrigger(List<String> details) {
    return details.length > 17 ? ParserUtils.parseTimingTrigger(details[17]) : "scenario";
  }
  
  @override
  bool validateElement(xml.XmlElement element) {
    if (!super.validateElement(element)) return false;
    
    final elementType = element.getAttribute("type");
    return elementType != null && elementType.isNotEmpty;
  }
}
