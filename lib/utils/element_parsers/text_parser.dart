/// Specialized parser for CSText elements
/// Handles parsing and generation of text elements with proper validation
/// and error handling.

import 'package:flutter/material.dart';
import 'package:xml/xml.dart' as xml;
import 'package:collection/collection.dart';

import 'package:simsushare_player/models/SimObjects.dart';
import 'package:simsushare_player/utils/parser_base.dart';
import 'package:simsushare_player/utils/parser_constants.dart' as parser_constants;
import 'package:simsushare_player/utils/parser_utilities.dart';

/// Parser for CSText elements
class TextParser extends ElementParser<SimText> {
  @override
  parser_constants.ElementType get elementType => parser_constants.ElementType.csText;

  @override
  SimText parseElement(
    xml.XmlElement element,
    xml.XmlDocument simdef,
    Map<String, dynamic> context,
  ) {
    final props = extractCommonProperties(element);
    final metadata = context["metadata"] as SimulationMetadata?;

    if (metadata == null) {
      throw ArgumentError("Simulation metadata required for text parsing");
    }

    final details = _parseElementDetails(element, simdef);

    return SimText(
      id: props["id"] ?? "",
      x: ParserUtils.parseDouble(props["x"]) / metadata.width,
      y: ParserUtils.parseDouble(props["y"]) / metadata.height,
      width: ParserUtils.parseDouble(props["width"], fallback: 200.0),
      height: ParserUtils.parseDouble(props["height"], fallback: 50.0),
      widthScale: ParserUtils.parseDouble(props["scaleX"], fallback: 1.0),
      heightScale: ParserUtils.parseDouble(props["scaleY"], fallback: 1.0),
      rotation: ParserUtils.parseDouble(props["rotation"]),
      text: props["text"] ?? element.innerText,
      backgroundColor: _parseBackgroundColor(props),
      opacity: details.length > 6 ? ParserUtils.parseDouble(details[6], fallback: 1.0) : 1.0,
    )
      ..hideOnStart = details.isNotEmpty ? details[0] == "in" : true
      ..trigger = details.length >= 17 ? ParserUtils.parseTimingTrigger(details[17]) : "scenario"
      ..priority = ParserUtils.parseInt(props["priority"], fallback: 1)
      ..triggerOnce = ParserUtils.parseBool(props["triggerOnce"])
      ..clickToToggle = details.length >= 18 ? ParserUtils.parseBool(details[18]) : false;
  }

  @override
  xml.XmlElement generateElement(SimText text, Map<String, dynamic> context) {
    final builder = xml.XmlBuilder();
    final metadata = context["metadata"] as SimulationMetadata?;

    if (metadata == null) {
      throw ArgumentError("Simulation metadata required for text generation");
    }

    builder.element("element", nest: () {
      builder.attribute("type", elementType.xmlName);
      builder.attribute("id", text.id);
      builder.attribute("x", (text.x * metadata.width).toString());
      builder.attribute("y", (text.y * metadata.height).toString());
      builder.attribute("width", text.width.toString());
      builder.attribute("height", text.height.toString());
      builder.attribute("scaleX", text.widthScale.toString());
      builder.attribute("scaleY", text.heightScale.toString());
      builder.attribute("rotation", text.rotation.toStringAsFixed(3));
      builder.attribute("text", text.text);
      builder.attribute("backgroundColor", ColorUtils.colorToHex(text.backgroundColor));
      builder.attribute("priority", text.priority.toString());
    });

    return builder.buildFragment().firstElementChild!;
  }

  /// Parse element details from simdef
  List<String> _parseElementDetails(xml.XmlElement element, xml.XmlDocument simdef) {
    final elementVal = simdef.findAllElements("elementVal").firstWhereOrNull((el) => el.getAttribute("id") == element.getAttribute("id"));

    final source = elementVal ?? element;
    return ParserUtils.parseDefinition(source.innerText);
  }

  /// Parse background color from properties
  Color _parseBackgroundColor(Map<String, String> props) {
    final colorStr = props["backgroundColor"];
    if (colorStr == null) return Colors.transparent;

    final color = ColorUtils.colorFromHex(colorStr);
    return color ?? Colors.transparent;
  }

  @override
  bool validateElement(xml.XmlElement element) {
    if (!super.validateElement(element)) return false;

    final text = element.getAttribute("text") ?? element.innerText;
    return text.isNotEmpty;
  }
}

/// Parser for CSTimer elements
class TimerParser extends ElementParser<SimTimer> {
  @override
  parser_constants.ElementType get elementType => parser_constants.ElementType.csTimer;

  @override
  SimTimer parseElement(
    xml.XmlElement element,
    xml.XmlDocument simdef,
    Map<String, dynamic> context,
  ) {
    final props = extractCommonProperties(element);
    final metadata = context["metadata"] as SimulationMetadata?;

    if (metadata == null) {
      throw ArgumentError("Simulation metadata required for timer parsing");
    }

    final details = _parseElementDetails(element, simdef);

    return SimTimer(
      type: SimTimerType.countdown, // Default type
      format: SimTimerFormat.minuteSeconds, // Default format
      seconds: ParserUtils.parseInt(props["seconds"], fallback: 60),
      startingSecond: ParserUtils.parseInt(props["startingSecond"], fallback: 0),
      id: props["id"] ?? "",
      x: ParserUtils.parseDouble(props["x"]) / metadata.width,
      y: ParserUtils.parseDouble(props["y"]) / metadata.height,
      width: ParserUtils.parseDouble(props["width"], fallback: 100.0),
      height: ParserUtils.parseDouble(props["height"], fallback: 50.0),
      widthScale: ParserUtils.parseDouble(props["scaleX"], fallback: 1.0),
      heightScale: ParserUtils.parseDouble(props["scaleY"], fallback: 1.0),
      rotation: ParserUtils.parseDouble(props["rotation"]),
      opacity: details.length > 6 ? ParserUtils.parseDouble(details[6], fallback: 1.0) : 1.0,
    )
      ..hideOnStart = details.isNotEmpty ? details[0] == "in" : true
      ..trigger = details.length >= 17 ? ParserUtils.parseTimingTrigger(details[17]) : "scenario"
      ..priority = ParserUtils.parseInt(props["priority"], fallback: 1)
      ..triggerOnce = ParserUtils.parseBool(props["triggerOnce"])
      ..clickToToggle = details.length >= 18 ? ParserUtils.parseBool(details[18]) : false;
  }

  @override
  xml.XmlElement generateElement(SimTimer timer, Map<String, dynamic> context) {
    final builder = xml.XmlBuilder();
    final metadata = context["metadata"] as SimulationMetadata?;

    if (metadata == null) {
      throw ArgumentError("Simulation metadata required for timer generation");
    }

    builder.element("element", nest: () {
      builder.attribute("type", elementType.xmlName);
      builder.attribute("id", timer.id);
      builder.attribute("x", (timer.x * metadata.width).toString());
      builder.attribute("y", (timer.y * metadata.height).toString());
      builder.attribute("width", timer.width.toString());
      builder.attribute("height", timer.height.toString());
      builder.attribute("scaleX", timer.widthScale.toString());
      builder.attribute("scaleY", timer.heightScale.toString());
      builder.attribute("rotation", timer.rotation.toStringAsFixed(3));
      builder.attribute("seconds", timer.seconds.toString());
      builder.attribute("startingSecond", timer.startingSecond.toString());
      builder.attribute("type", timer.type.toString());
      builder.attribute("format", timer.format.toString());
      builder.attribute("priority", timer.priority.toString());
    });

    return builder.buildFragment().firstElementChild!;
  }

  /// Parse element details from simdef
  List<String> _parseElementDetails(xml.XmlElement element, xml.XmlDocument simdef) {
    final elementVal = simdef.findAllElements("elementVal").firstWhereOrNull((el) => el.getAttribute("id") == element.getAttribute("id"));

    final source = elementVal ?? element;
    return ParserUtils.parseDefinition(source.innerText);
  }

  /// Parse background color from properties
  Color _parseBackgroundColor(Map<String, String> props) {
    final colorStr = props["backgroundColor"];
    if (colorStr == null) return Colors.transparent;

    final color = ColorUtils.colorFromHex(colorStr);
    return color ?? Colors.transparent;
  }

  @override
  bool validateElement(xml.XmlElement element) {
    if (!super.validateElement(element)) return false;

    final duration = element.getAttribute("duration");
    if (duration == null) return false;

    final durationValue = double.tryParse(duration);
    return durationValue != null && durationValue > 0;
  }
}
