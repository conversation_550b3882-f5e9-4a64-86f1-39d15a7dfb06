/// Specialized parser for CSShape elements
/// Handles parsing and generation of shape elements with proper validation
/// and error handling.

import 'package:flutter/material.dart';
import 'package:xml/xml.dart' as xml;
import 'package:collection/collection.dart';

import 'package:simsushare_player/models/SimObjects.dart';
import 'package:simsushare_player/utils/parser_base.dart';
import 'package:simsushare_player/utils/parser_constants.dart' as parser_constants;
import 'package:simsushare_player/utils/parser_utilities.dart';

/// Parser for CSShape elements
class ShapeParser extends ElementParser<SimShape> {
  @override
  parser_constants.ElementType get elementType => parser_constants.ElementType.csShape;

  @override
  SimShape parseElement(
    xml.XmlElement element,
    xml.XmlDocument simdef,
    Map<String, dynamic> context,
  ) {
    final props = extractCommonProperties(element);
    final metadata = context["metadata"] as SimulationMetadata?;

    if (metadata == null) {
      throw ArgumentError("Simulation metadata required for shape parsing");
    }

    final details = _parseElementDetails(element, simdef);

    return SimShape(
      id: props["id"] ?? "",
      x: ParserUtils.parseDouble(props["x"]) / metadata.width,
      y: ParserUtils.parseDouble(props["y"]) / metadata.height,
      width: ParserUtils.parseDouble(props["width"], fallback: 100.0),
      height: ParserUtils.parseDouble(props["height"], fallback: 100.0),
      widthScale: ParserUtils.parseDouble(props["scaleX"], fallback: 1.0),
      heightScale: ParserUtils.parseDouble(props["scaleY"], fallback: 1.0),
      rotation: ParserUtils.parseDouble(props["rotation"]),
      shape: props["shape"] ?? "rectangle",
      filterColor: _parseFilterColor(details),
      opacity: details.length > 6 ? ParserUtils.parseDouble(details[6], fallback: 1.0) : 1.0,
    )
      ..hideOnStart = details.isNotEmpty ? details[0] == "in" : true
      ..trigger = details.length >= 17 ? ParserUtils.parseTimingTrigger(details[17]) : "scenario"
      ..priority = ParserUtils.parseInt(props["priority"], fallback: 1)
      ..triggerOnce = ParserUtils.parseBool(props["triggerOnce"])
      ..clickToToggle = details.length >= 18 ? ParserUtils.parseBool(details[18]) : false;
  }

  @override
  xml.XmlElement generateElement(SimShape shape, Map<String, dynamic> context) {
    final builder = xml.XmlBuilder();
    final metadata = context["metadata"] as SimulationMetadata?;

    if (metadata == null) {
      throw ArgumentError("Simulation metadata required for shape generation");
    }

    builder.element("element", nest: () {
      builder.attribute("type", elementType.xmlName);
      builder.attribute("id", shape.id);
      builder.attribute("x", (shape.x * metadata.width).toString());
      builder.attribute("y", (shape.y * metadata.height).toString());
      builder.attribute("width", shape.width.toString());
      builder.attribute("height", shape.height.toString());
      builder.attribute("scaleX", shape.widthScale.toString());
      builder.attribute("scaleY", shape.heightScale.toString());
      builder.attribute("rotation", shape.rotation.toStringAsFixed(3));
      builder.attribute("shape", shape.shape);
      builder.attribute("priority", shape.priority.toString());

      // Note: SimShape doesn't have 'to' and 'syncVariable' properties
      // These are only available in SimLocationJumper
    });

    return builder.buildFragment().firstElementChild!;
  }

  /// Parse element details from simdef
  List<String> _parseElementDetails(xml.XmlElement element, xml.XmlDocument simdef) {
    final elementVal = simdef.findAllElements("elementVal").firstWhereOrNull((el) => el.getAttribute("id") == element.getAttribute("id"));

    final source = elementVal ?? element;
    return ParserUtils.parseDefinition(source.innerText);
  }

  /// Parse filter color from details
  Color _parseFilterColor(List<String> details) {
    if (details.length <= 6) return Colors.transparent;

    try {
      final colorValue = int.parse(details[4]);
      final opacity = double.parse(details[5]);
      return ColorUtils.getColorFromValue(colorValue).withValues(alpha: opacity);
    } catch (e) {
      return Colors.transparent;
    }
  }

  @override
  bool validateElement(xml.XmlElement element) {
    if (!super.validateElement(element)) return false;

    final shape = element.getAttribute("shape");
    if (shape == null) return false;

    // Validate shape type
    final validShapes = ["rectangle", "circle", "arrow", "triangle"];
    return validShapes.contains(shape.toLowerCase());
  }
}

/// Parser for LocJumper elements (location navigation shapes)
class LocJumperParser extends ElementParser<SimLocationJumper> {
  @override
  parser_constants.ElementType get elementType => parser_constants.ElementType.locJumper;

  @override
  SimLocationJumper parseElement(
    xml.XmlElement element,
    xml.XmlDocument simdef,
    Map<String, dynamic> context,
  ) {
    final props = extractCommonProperties(element);
    final metadata = context["metadata"] as SimulationMetadata?;

    if (metadata == null) {
      throw ArgumentError("Simulation metadata required for LocJumper parsing");
    }

    final details = _parseElementDetails(element, simdef);
    final shapeNumber = details.isNotEmpty ? ParserUtils.parseInt(details[0]) : 1;

    return SimLocationJumper(
      to: props["to"] ?? "",
      shape: _getShapeFromNumber(shapeNumber),
      id: props["id"] ?? "",
      x: ParserUtils.parseDouble(props["x"]) / metadata.width,
      y: ParserUtils.parseDouble(props["y"]) / metadata.height,
      width: ParserUtils.parseDouble(props["width"], fallback: 100.0),
      height: ParserUtils.parseDouble(props["height"], fallback: 100.0),
      widthScale: ParserUtils.parseDouble(props["scaleX"], fallback: 1.0),
      heightScale: ParserUtils.parseDouble(props["scaleY"], fallback: 1.0),
      rotation: ParserUtils.parseDouble(props["rotation"]),
      filterColor: _parseFilterColor(details),
      opacity: details.length > 6 ? ParserUtils.parseDouble(details[6], fallback: 1.0) : 1.0,
    )
      ..hideOnStart = details.isNotEmpty ? details[0] == "in" : true
      ..trigger = details.length >= 17 ? ParserUtils.parseTimingTrigger(details[17]) : "scenario"
      ..priority = ParserUtils.parseInt(props["priority"], fallback: 1)
      ..triggerOnce = ParserUtils.parseBool(props["triggerOnce"])
      ..clickToToggle = details.length >= 18 ? ParserUtils.parseBool(details[18]) : false;
  }

  @override
  xml.XmlElement generateElement(SimLocationJumper locJumper, Map<String, dynamic> context) {
    final builder = xml.XmlBuilder();
    final metadata = context["metadata"] as SimulationMetadata?;

    if (metadata == null) {
      throw ArgumentError("Simulation metadata required for LocJumper generation");
    }

    builder.element("element", nest: () {
      builder.attribute("type", elementType.xmlName);
      builder.attribute("id", locJumper.id);
      builder.attribute("x", (locJumper.x * metadata.width).toString());
      builder.attribute("y", (locJumper.y * metadata.height).toString());
      builder.attribute("width", locJumper.width.toString());
      builder.attribute("height", locJumper.height.toString());
      builder.attribute("scaleX", locJumper.widthScale.toString());
      builder.attribute("scaleY", locJumper.heightScale.toString());
      builder.attribute("rotation", locJumper.rotation.toStringAsFixed(3));
      builder.attribute("priority", locJumper.priority.toString());

      if (locJumper.to.isNotEmpty) {
        builder.attribute("to", locJumper.to);
      }
    });

    return builder.buildFragment().firstElementChild!;
  }

  /// Parse element details from simdef
  List<String> _parseElementDetails(xml.XmlElement element, xml.XmlDocument simdef) {
    final elementVal = simdef.findAllElements("elementVal").firstWhereOrNull((el) => el.getAttribute("id") == element.getAttribute("id"));

    final source = elementVal ?? element;
    return ParserUtils.parseDefinition(source.innerText);
  }

  /// Parse filter color from details
  Color _parseFilterColor(List<String> details) {
    if (details.length <= 6) return Colors.transparent;

    try {
      final colorValue = int.parse(details[4]);
      final opacity = double.parse(details[5]);
      return ColorUtils.getColorFromValue(colorValue).withValues(alpha: opacity);
    } catch (e) {
      return Colors.transparent;
    }
  }

  /// Convert shape number to shape name
  String _getShapeFromNumber(int shapeNumber) {
    switch (shapeNumber) {
      case 1:
        return "arrow-1";
      case 2:
        return "arrow-2";
      case 3:
        return "arrow-3";
      case 4:
        return "arrow-4";
      default:
        return "arrow-1";
    }
  }

  @override
  bool validateElement(xml.XmlElement element) {
    if (!super.validateElement(element)) return false;

    final to = element.getAttribute("to");
    return to != null && to.isNotEmpty;
  }
}
