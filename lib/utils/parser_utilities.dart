/// Shared utility functions for parsing operations
/// This file contains common functionality used across different parser files
/// to eliminate code duplication and improve maintainability.

import 'dart:convert';
import 'dart:ui' as ui;
import 'package:flutter/material.dart';
import 'package:flutter_colorpicker/flutter_colorpicker.dart';
import 'package:collection/collection.dart';
import 'package:xml/xml.dart' as xml;
import 'package:get/get.dart';

import 'package:simsushare_player/models/SimObjects.dart';
import 'package:simsushare_player/models/Mask.dart';
import 'package:simsushare_player/utils/parser_constants.dart';

/// Utility class for common parsing operations
class ParserUtils {
  
  /// Safely parse a double value with fallback
  static double parseDouble(String? value, {double fallback = 0.0}) {
    if (value == null || value.isEmpty) return fallback;
    return double.tryParse(value) ?? fallback;
  }
  
  /// Safely parse an integer value with fallback
  static int parseInt(String? value, {int fallback = 0}) {
    if (value == null || value.isEmpty) return fallback;
    return int.tryParse(value) ?? fallback;
  }
  
  /// Safely parse a boolean value with fallback
  static bool parseBool(String? value, {bool fallback = false}) {
    if (value == null || value.isEmpty) return fallback;
    return value.toLowerCase() == "true";
  }
  
  /// Parse timing trigger from string value
  static String parseTimingTrigger(String trigger) {
    final timingTrigger = TimingTriggerExtension.fromString(trigger);
    return timingTrigger.name;
  }
  
  /// Get timing trigger definition value for XML generation
  static String getTimingTriggerDefValue(SimObject simObj) {
    switch (simObj.trigger) {
      case "scenario":
        return TimingTrigger.scenario.value;
      case "location":
        return TimingTrigger.location.value;
      case "state":
        return TimingTrigger.state.value;
      default:
        return TimingTrigger.scenario.value;
    }
  }
  
  /// Validate and normalize element ID
  static String normalizeElementId(String? id, String locationId) {
    if (id == null || id.isEmpty) {
      throw ArgumentError(ParserErrors.missingElementId);
    }
    
    // Add location suffix if not already present
    if (!id.endsWith("-$locationId")) {
      return "$id-$locationId";
    }
    return id;
  }
  
  /// Extract common element attributes from XML element
  static Map<String, String> extractElementAttributes(xml.XmlElement element) {
    return element.attributes.fold<Map<String, String>>(
      {},
      (map, attr) => map..[attr.name.toString()] = attr.value.toString(),
    );
  }
  
  /// Parse comma-separated definition string safely
  static List<String> parseDefinition(String definition) {
    if (definition.isEmpty) return [];
    return definition.split(",").map((s) => s.trim()).toList();
  }
  
  /// Get element value from simdef by ID
  static String? getElementValue(xml.XmlDocument simdef, String elementId) {
    final elementVal = simdef
        .findAllElements("elementVal")
        .firstWhereOrNull((el) => el.getAttribute("id") == elementId);
    return elementVal?.innerText;
  }
  
  /// Validate coordinate format and parse
  static List<Coordinate> parseCoordinates(String coordString) {
    if (coordString.isEmpty) return [];
    
    final coordinates = <Coordinate>[];
    for (final coords in coordString.split("x")) {
      final coordSplit = coords.split("c");
      if (coordSplit.length >= 2) {
        final x = parseDouble(coordSplit[0]);
        final y = parseDouble(coordSplit[1]);
        coordinates.add(Coordinate(x, y));
      }
    }
    return coordinates;
  }
  
  /// Calculate navigation cluster position
  static double calculateNavClusterPosition(
    String? pctValue,
    double simDimension,
    bool isY,
  ) {
    if (pctValue == null) return 0.0;
    
    final pct = parseDouble(pctValue);
    final position = pct * simDimension - ParserDefaults.navClusterOffset;
    
    // For Y coordinate, flip because app starts from bottom instead of top
    return isY ? (1 - pct) * simDimension - ParserDefaults.navClusterOffset : position;
  }
  
  /// Get initial size for elements
  static double getInitialSize() {
    return Get.size.width / 8;
  }
  
  /// Validate element properties
  static void validateElementProperties(Map<String, String> props) {
    final id = props["id"];
    if (id == null || id.isEmpty) {
      throw ArgumentError(ParserErrors.missingElementId);
    }
    
    // Validate scale values
    final scaleX = parseDouble(props["scaleX"], fallback: ParserDefaults.defaultScale);
    final scaleY = parseDouble(props["scaleY"], fallback: ParserDefaults.defaultScale);
    
    if (scaleX < ValidationConstants.minScale || scaleX > ValidationConstants.maxScale) {
      throw ArgumentError("Scale X value out of valid range: $scaleX");
    }
    
    if (scaleY < ValidationConstants.minScale || scaleY > ValidationConstants.maxScale) {
      throw ArgumentError("Scale Y value out of valid range: $scaleY");
    }
  }
  
  /// Create safe file path
  static String createSafeFilePath(String basePath, String fileName) {
    if (fileName.isEmpty) {
      throw ArgumentError(ParserErrors.missingFileName);
    }
    
    // Remove any path traversal attempts
    final safeName = fileName.replaceAll(RegExp(r'[\.\/\\]'), '_');
    return "$basePath/$safeName";
  }
  
  /// Parse offset from string format "XxY"
  static Offset parseOffset(String? offsetString) {
    if (offsetString == null || offsetString.isEmpty) {
      return Offset.zero;
    }
    
    final parts = offsetString.split("x");
    if (parts.length >= 2) {
      final x = parseDouble(parts[0]);
      final y = parseDouble(parts[1]);
      return Offset(x, y);
    }
    
    return Offset.zero;
  }
  
  /// Format offset to string format "XxY"
  static String formatOffset(Offset offset) {
    return "${offset.dx}x${offset.dy}";
  }
  
  /// Check if string is a valid base64 encoded value
  static bool isBase64(String value) {
    if (value.isEmpty) return false;
    try {
      base64.decode(value);
      return true;
    } catch (e) {
      return false;
    }
  }
  
  /// Normalize position values to percentage if needed
  static double normalizePosition(double position, double dimension) {
    // If position is greater than 1, assume it's absolute and convert to percentage
    if (position > 1.0) {
      return position / dimension;
    }
    return position;
  }
  
  /// Denormalize position from percentage to absolute
  static double denormalizePosition(double position, double dimension) {
    // If position is less than or equal to 1, assume it's percentage
    if (position <= 1.0) {
      return position * dimension;
    }
    return position;
  }
}

/// Color utility functions
class ColorUtils {
  
  /// Convert Color to integer value for XML storage
  static int getColorValue(Color c, {bool withAlpha = false}) {
    final a = withAlpha ? c.alpha.toRadixString(16).padLeft(2, "0") : "";
    final r = c.red.toRadixString(16).padLeft(2, "0");
    final g = c.green.toRadixString(16).padLeft(2, "0");
    final b = c.blue.toRadixString(16).padLeft(2, "0");
    return int.parse("$a$r$g$b", radix: 16);
  }
  
  /// Convert integer value to Color
  static Color getColorFromValue(int value, {bool withAlpha = false}) {
    final hex = value.toRadixString(16).padLeft(withAlpha ? 8 : 6, "0");
    return Color(int.parse("${withAlpha ? "" : "FF"}${hex.toUpperCase()}", radix: 16));
  }
  
  /// Safely parse color from hex string
  static Color? colorFromHex(String? hexString) {
    if (hexString == null || hexString.isEmpty) return null;
    
    try {
      // Remove # if present
      final hex = hexString.replaceAll("#", "");
      
      // Add FF prefix if no alpha channel
      final fullHex = hex.length == 6 ? "FF$hex" : hex;
      
      return Color(int.parse(fullHex, radix: 16));
    } catch (e) {
      return null;
    }
  }
  
  /// Convert color to hex string
  static String colorToHex(Color color, {bool withAlpha = false}) {
    if (withAlpha) {
      return "#${color.value.toRadixString(16).padLeft(8, '0').toUpperCase()}";
    } else {
      return "#${(color.value & 0xFFFFFF).toRadixString(16).padLeft(6, '0').toUpperCase()}";
    }
  }
}
