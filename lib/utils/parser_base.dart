/// Base classes and interfaces for parser implementations
/// This file defines the common structure and contracts for all parsers
/// to ensure consistency and enable polymorphic behavior.

import 'dart:ui' as ui;
import 'package:flutter/material.dart';
import 'package:xml/xml.dart' as xml;

import 'package:simsushare_player/models/SimObjects.dart';
import 'package:simsushare_player/models/Mask.dart';
import 'package:simsushare_player/models/Simulation.dart';
import 'package:simsushare_player/utils/parser_constants.dart';
import 'package:simsushare_player/utils/parser_utilities.dart';

/// Abstract base class for all element parsers
abstract class ElementParser<T extends SimObject> {
  
  /// Parse an element from XML and return the corresponding SimObject
  T parseElement(
    xml.XmlElement element,
    xml.XmlDocument simdef,
    Map<String, dynamic> context,
  );
  
  /// Generate XML element from SimObject
  xml.XmlElement generateElement(T object, Map<String, dynamic> context);
  
  /// Get the element type this parser handles
  ElementType get elementType;
  
  /// Validate element data before parsing
  bool validateElement(xml.XmlElement element) {
    final id = element.getAttribute("id");
    return id != null && id.isNotEmpty;
  }
  
  /// Extract common properties from XML element
  Map<String, String> extractCommonProperties(xml.XmlElement element) {
    return ParserUtils.extractElementAttributes(element);
  }
  
  /// Parse common SimObject properties
  void parseCommonProperties(SimObject object, Map<String, String> props, List<String> details) {
    object.priority = ParserUtils.parseInt(props["priority"], fallback: ParserDefaults.defaultPriority);
    object.triggerOnce = ParserUtils.parseBool(props["triggerOnce"]);
    object.movable = ParserUtils.parseBool(props["movable"]);
    
    // Parse timing trigger from details if available
    if (details.isNotEmpty) {
      final triggerIndex = _getTriggerIndex();
      if (details.length > triggerIndex) {
        object.trigger = ParserUtils.parseTimingTrigger(details[triggerIndex]);
      }
    }
  }
  
  /// Get the index of timing trigger in details array (varies by element type)
  int _getTriggerIndex() => 16; // Default, override in subclasses if different
}

/// Abstract base class for simulation parsers
abstract class SimulationParser {
  
  /// Parse simulation definition from XML
  Future<Scenario> parseSimulation(String xmlContent, Map<String, dynamic> context);
  
  /// Generate XML from simulation
  String generateSimulation(Scenario scenario, {bool cleanup = false});
  
  /// Parse simulation metadata (name, id, dimensions, etc.)
  SimulationMetadata parseMetadata(xml.XmlDocument simdef) {
    final simElement = simdef.getElement("sim");
    if (simElement == null) {
      throw ArgumentError(ParserErrors.xmlParsingError);
    }
    
    return SimulationMetadata(
      id: simElement.getAttribute("id") ?? "",
      name: simElement.getAttribute("title") ?? "",
      width: ParserUtils.parseDouble(simElement.getAttribute("width")),
      height: ParserUtils.parseDouble(simElement.getAttribute("height")),
      backgroundColor: simElement.getAttribute("backgroundColor") ?? ParserDefaults.defaultBackgroundColor,
      navClusterX: _parseNavCluster(simElement, "NavClusterXPct", false),
      navClusterY: _parseNavCluster(simElement, "NavClusterYPct", true),
    );
  }
  
  double? _parseNavCluster(xml.XmlElement simElement, String attribute, bool isY) {
    final value = simElement.getAttribute(attribute);
    if (value == null) return null;
    
    final width = ParserUtils.parseDouble(simElement.getAttribute("width"));
    final height = ParserUtils.parseDouble(simElement.getAttribute("height"));
    final dimension = isY ? height : width;
    
    return ParserUtils.calculateNavClusterPosition(value, dimension, isY);
  }
  
  /// Parse simulation states
  List<SimulationState> parseStates(xml.XmlDocument simdef) {
    return simdef
        .findAllElements("state")
        .map((e) => SimulationState(
              id: e.getAttribute("id") ?? "",
              name: e.getAttribute("name") ?? "",
            ))
        .toList();
  }
  
  /// Parse simulation navigations
  List<SimulationNavigation> parseNavigations(xml.XmlDocument simdef) {
    return simdef
        .findAllElements("nav")
        .map((e) => SimulationNavigation(
              direction: e.getAttribute("dir") ?? "",
              from: e.getAttribute("fromID") ?? "",
              to: e.getAttribute("toID") ?? "",
            ))
        .toList();
  }
  
  /// Parse state-location mapping
  Map<String, String> parseStateLocationMapping(xml.XmlDocument simdef) {
    final mapping = <String, String>{};
    
    for (final simFrame in simdef.findAllElements("simFrame")) {
      final state = simFrame.getAttribute("states");
      final location = simFrame.getAttribute("locs");
      if (state != null && location != null) {
        mapping[location] = state;
      }
    }
    
    return mapping;
  }
  
  /// Parse simulation variables
  Map<String, String> parseVariables(xml.XmlDocument simdef) {
    final variables = <String, String>{};
    
    for (final variable in simdef.findAllElements("variable")) {
      final id = variable.getAttribute("id");
      final defaultValue = variable.getAttribute("default");
      if (id != null && defaultValue != null) {
        variables[id] = defaultValue;
      }
    }
    
    return variables;
  }
}

/// Data class for simulation metadata
class SimulationMetadata {
  final String id;
  final String name;
  final double width;
  final double height;
  final String backgroundColor;
  final double? navClusterX;
  final double? navClusterY;
  
  const SimulationMetadata({
    required this.id,
    required this.name,
    required this.width,
    required this.height,
    required this.backgroundColor,
    this.navClusterX,
    this.navClusterY,
  });
}

/// Interface for file system operations
abstract class FileSystemProvider {
  
  /// Read file as bytes
  Future<List<int>> readFileAsBytes(String path);
  
  /// Read file as string
  Future<String> readFileAsString(String path);
  
  /// Check if file exists
  Future<bool> fileExists(String path);
  
  /// List files in directory
  Future<List<String>> listFiles(String directoryPath);
  
  /// Decode image from bytes
  Future<ui.Image> decodeImage(List<int> bytes);
}

/// Interface for asset loading operations
abstract class AssetProvider {
  
  /// Load sprite metadata
  Future<Map<String, dynamic>> loadSpriteMetadata(String assetName);
  
  /// Load sprite image
  Future<ui.Image> loadSpriteImage(String assetName);
  
  /// Check if asset exists
  Future<bool> assetExists(String assetName);
}

/// Context object for passing data between parsers
class ParsingContext {
  final FileSystemProvider? fileSystem;
  final AssetProvider? assetProvider;
  final Map<String, dynamic> metadata;
  final List<String> availableFiles;
  
  ParsingContext({
    this.fileSystem,
    this.assetProvider,
    this.metadata = const {},
    this.availableFiles = const [],
  });
  
  /// Create a copy with additional metadata
  ParsingContext copyWith({
    FileSystemProvider? fileSystem,
    AssetProvider? assetProvider,
    Map<String, dynamic>? metadata,
    List<String>? availableFiles,
  }) {
    return ParsingContext(
      fileSystem: fileSystem ?? this.fileSystem,
      assetProvider: assetProvider ?? this.assetProvider,
      metadata: {...this.metadata, ...?metadata},
      availableFiles: availableFiles ?? this.availableFiles,
    );
  }
}

/// Result object for parsing operations
class ParsingResult<T> {
  final T? data;
  final List<String> warnings;
  final List<String> errors;
  final bool isSuccess;
  
  const ParsingResult({
    this.data,
    this.warnings = const [],
    this.errors = const [],
    required this.isSuccess,
  });
  
  factory ParsingResult.success(T data, {List<String> warnings = const []}) {
    return ParsingResult(
      data: data,
      warnings: warnings,
      isSuccess: true,
    );
  }
  
  factory ParsingResult.failure(List<String> errors, {List<String> warnings = const []}) {
    return ParsingResult(
      errors: errors,
      warnings: warnings,
      isSuccess: false,
    );
  }
}
