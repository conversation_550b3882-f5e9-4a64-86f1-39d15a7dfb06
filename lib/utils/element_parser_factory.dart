import 'dart:convert';
import 'dart:io';
import 'dart:ui' as ui;
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:path/path.dart' as path;
import 'package:xml/xml.dart' as xml;
import 'package:collection/collection.dart';

import 'package:simsushare_player/models/SimObjects.dart';
import 'package:simsushare_player/models/Mask.dart';
import 'package:simsushare_player/utils/constants.dart';
import 'package:simsushare_player/utils/parsers.dart';
import 'package:simsushare_player/utils/parser_base.dart';
import 'package:simsushare_player/utils/parser_utilities.dart';
import 'package:simsushare_player/flame/Containers/parser.dart';
import 'package:simsushare_player/flame/Labels/parser.dart';
import 'package:simsushare_player/flame/People/parser.dart';
import 'package:nanoid/nanoid.dart';

/// Factory class for parsing different types of XML elements into SimObjects
class ElementParserFactory {
  /// Initialize the factory (placeholder for any setup needed)
  static void initialize() {
    // Any initialization logic can go here
  }

  /// Parse an XML element into a SimObject based on its type
  static Future<SimObject?> parseElement(
    xml.XmlElement element,
    xml.XmlDocument simdef,
    Map<String, dynamic> context,
  ) async {
    final elementType = element.getAttribute("type");

    switch (elementType) {
      case "CSPic":
        return await _parseCSPic(element, simdef, context);
      case "CSMask":
        return await _parseCSMask(element, simdef, context);
      case "CSShape":
        return await _parseCSShape(element, simdef, context);
      case "LocJumper":
        return await _parseLocJumper(element, simdef, context);
      case "CSText":
        return await _parseCSText(element, simdef, context);
      case "CSTimer":
        return await _parseCSTimer(element, simdef, context);
      case "AudioClip":
        return await _parseAudioClip(element, simdef, context);
      default:
        return await _parseDefaultElement(element, simdef, context);
    }
  }

  /// Parse CSPic elements (images)
  static Future<SimObject?> _parseCSPic(
    xml.XmlElement element,
    xml.XmlDocument simdef,
    Map<String, dynamic> context,
  ) async {
    final Directory dir = context["directory"];
    final List<String> allFileNames = context["allFileNames"];

    if (element.getAttribute("background") == "true") {
      // Background images are handled separately in location parsing
      return null;
    }

    final imageName = element.getAttribute("file");
    if (imageName == null) return null;

    final fullFileName = allFileNames.firstWhereOrNull((element) => element.startsWith(imageName));
    if (fullFileName == null) return null;

    final imageFile = File(path.join(dir.path, imageName));
    ui.Image img;
    if (kIsWeb) {
      img = await decodeImageFromList(await imageFile.readAsBytes());
    } else {
      img = await decodeImageFromList(await File(imageFile.path).readAsBytes());
    }

    final x = (double.tryParse(element.getAttribute("x") ?? "0") ?? 0);
    final y = (double.tryParse(element.getAttribute("y") ?? "0") ?? 0);

    final details = (simdef.findAllElements("elementVal").firstWhereOrNull((el) => el.getAttribute("id") == element.getAttribute("id")) ?? element)
        .innerText
        .split(",");

    final sImg = SimImage(
      id: element.getAttribute("id") ?? "",
      img: img,
      path: imageFile.path,
      x: x,
      y: y,
      width: img.width.toDouble(),
      height: img.height.toDouble(),
      widthScale: double.tryParse(element.getAttribute("scaleX") ?? "1") ?? 1,
      heightScale: double.tryParse(element.getAttribute("scaleY") ?? "1") ?? 1,
      scale: details.length >= 12 ? double.tryParse(details[12]) ?? 1 : 1,
      rotation: double.tryParse(element.getAttribute("rotation") ?? "") ?? 0.0,
      opacity: details.length > 6 ? double.tryParse(details[6]) ?? 1 : 1,
      mirrorX: details.length > 10 ? details[10] == "true" : false,
      mirrorY: details.length > 11 ? details[11] == "true" : false,
      filterColor:
          details.length > 5 ? ColorUtils.getColorFromValue(int.parse(details[4])).withValues(alpha: double.parse(details[5])) : Colors.transparent,
      blur: details.length >= 16 ? double.tryParse(details[16]) ?? 0 : 0,
      to: element.getAttribute("to") ?? "",
      syncVariable: element.getAttribute("syncVar"),
    )
      ..clickToToggle = details.length >= 18 ? details[18] == "true" : false
      ..hideOnStart = details.isNotEmpty ? details[0] == "in" : true
      ..trigger = details.length >= 17 ? ParserUtils.parseTimingTrigger(details[17]) : "scenario"
      ..priority = int.parse(element.getAttribute("priority") ?? "1")
      ..triggerOnce = element.getAttribute("triggerOnce") == "true";

    return sImg;
  }

  /// Parse CSMask elements
  static Future<SimObject?> _parseCSMask(
    xml.XmlElement element,
    xml.XmlDocument simdef,
    Map<String, dynamic> context,
  ) async {
    final SimulationMetadata metadata = context["metadata"];
    final List<Mask> simMasks = context["simMasks"];
    final String locationId = context["locationId"];

    final maskElementId = element.getAttribute("id") ?? "";
    final mask = parseMask(maskElementId + (maskElementId.endsWith("-$locationId") ? "" : "-$locationId"), element.innerText, locationId);

    final maskElementName = element.getAttribute("name");
    if (maskElementName != null) {
      mask.name = maskElementName;
    }

    final offsetX = double.tryParse(element.getAttribute("x") ?? "0") ?? 0;
    final offsetY = double.tryParse(element.getAttribute("y") ?? "0") ?? 0;
    mask.coordinates = mask.coordinates.map((coor) => Coordinate((coor.x + offsetX) / metadata.width, (coor.y + offsetY) / metadata.height)).toList();

    if (simMasks.firstWhereOrNull((element) => element.id == mask.id) == null) {
      simMasks.add(mask);
    }

    // Masks don't return a SimObject, they're added to the global masks list
    return null;
  }

  /// Parse CSShape elements
  static Future<SimObject?> _parseCSShape(
    xml.XmlElement element,
    xml.XmlDocument simdef,
    Map<String, dynamic> context,
  ) async {
    final SimulationMetadata metadata = context["metadata"];

    final shapeDetails =
        simdef.findAllElements("elementVal").firstWhere((el) => el.getAttribute("id") == element.getAttribute("id")).innerText.split(",");

    final sShape = SimShape(
      id: element.getAttribute("id") ?? nanoid(10),
      x: (double.tryParse(element.getAttribute("x") ?? "") ?? 1) / metadata.width,
      y: (double.tryParse(element.getAttribute("y") ?? "") ?? 1) / metadata.height,
      widthScale: double.tryParse(element.getAttribute("scaleX") ?? "") ?? 1.0,
      heightScale: double.tryParse(element.getAttribute("scaleY") ?? "") ?? 1.0,
      rotation: double.tryParse(element.getAttribute("rotation") ?? "") ?? 0.0,
      shape: element.getAttribute("shape") ?? "rectangle",
      filterColor: shapeDetails.length > 6
          ? ColorUtils.getColorFromValue(int.parse(shapeDetails[4])).withValues(alpha: double.parse(shapeDetails[5]))
          : Colors.transparent,
      fadeInWhen: shapeDetails.length > 8 ? double.tryParse(shapeDetails[8]) ?? 0 : 0,
      fadeInDuration: shapeDetails.length > 9 ? double.tryParse(shapeDetails[9]) ?? 0 : 0,
      mirrorX: shapeDetails.length > 10 ? (shapeDetails[10] == "true") : false,
      mirrorY: shapeDetails.length > 11 ? (shapeDetails[11] == "true") : false,
      pinch: shapeDetails.length > 12 ? double.tryParse(shapeDetails[12]) : null,
      fadeOut: shapeDetails.length > 13 ? (shapeDetails[13] == "true") : false,
      fadeOutWhen: shapeDetails.length > 14 ? double.tryParse(shapeDetails[14]) ?? 0 : 0,
      fadeOutDuration: shapeDetails.length > 15 ? double.tryParse(shapeDetails[15]) ?? 0 : 0,
      blur: shapeDetails.length > 16 ? double.tryParse(shapeDetails[16]) ?? 0 : 0,
      opacity: shapeDetails.length > 6 ? double.tryParse(shapeDetails[6]) ?? 1 : 1,
    )
      ..priority = int.parse(element.getAttribute("priority") ?? "1")
      ..trigger = shapeDetails.length > 17 ? ParserUtils.parseTimingTrigger(shapeDetails[17]) : "scenario"
      ..triggerOnce = element.getAttribute("triggerOnce") == "true";

    return sShape;
  }

  /// Parse LocJumper elements
  static Future<SimObject?> _parseLocJumper(
    xml.XmlElement element,
    xml.XmlDocument simdef,
    Map<String, dynamic> context,
  ) async {
    final SimulationMetadata metadata = context["metadata"];

    final jumperDetails = element.innerText.split(",");
    final sJumper = SimLocationJumper(
      id: element.getAttribute("id") ?? nanoid(10),
      name: element.getAttribute("name"),
      to: "",
      shape: "arrow",
      x: (double.tryParse(element.getAttribute("x") ?? "") ?? 1) / metadata.width,
      y: (double.tryParse(element.getAttribute("y") ?? "") ?? 1) / metadata.height,
      widthScale: double.tryParse(element.getAttribute("scaleX") ?? "") ?? 1.0,
      heightScale: double.tryParse(element.getAttribute("scaleY") ?? "") ?? 1.0,
      rotation: double.tryParse(element.getAttribute("rotation") ?? "") ?? 0.0,
    )
      ..filterColor = jumperDetails.length > 6
          ? HSLColor.fromAHSL(
              double.tryParse(jumperDetails.length > 9 ? jumperDetails[9] : "") ?? 1,
              double.tryParse(jumperDetails.length > 7 ? jumperDetails[7] : "") ?? 0,
              double.tryParse(jumperDetails.length > 8 ? jumperDetails[8] : "") ?? 0,
              (double.tryParse(jumperDetails.length > 6 ? jumperDetails[6] : "") ?? 0),
            ).toColor()
          : Colors.transparent
      ..fadeInWhen = jumperDetails.length > 19 ? double.tryParse(jumperDetails[19]) ?? 0 : 0
      ..fadeInDuration = jumperDetails.length > 20 ? double.tryParse(jumperDetails[20]) ?? 0 : 0
      ..mirrorX = jumperDetails.length > 10 ? (jumperDetails[10] == "true") : false
      ..mirrorY = jumperDetails.length > 11 ? (jumperDetails[11] == "true") : false
      ..widthScale = jumperDetails.length > 12 ? double.tryParse(jumperDetails[12]) ?? 0.0 : 0.0
      ..fadeOut = jumperDetails.length > 13 ? (jumperDetails[13] == "true") : false
      ..fadeOutWhen = jumperDetails.length > 14 ? double.tryParse(jumperDetails[14]) ?? 0 : 0
      ..fadeOutDuration = jumperDetails.length > 15 ? double.tryParse(jumperDetails[15]) ?? 0 : 0
      ..blur = jumperDetails.length > 16 ? double.tryParse(jumperDetails[16]) ?? 0 : 0
      ..triggerOnce = element.getAttribute("triggerOnce") == "true";

    setLocationJumperFromText(sJumper, jumperDetails);
    return sJumper;
  }

  /// Parse CSText elements
  static Future<SimObject?> _parseCSText(
    xml.XmlElement element,
    xml.XmlDocument simdef,
    Map<String, dynamic> context,
  ) async {
    final SimulationMetadata metadata = context["metadata"];

    final sInner = element.innerText.split(",");
    final sText = SimText(
      id: element.getAttribute("id") ?? nanoid(10),
      x: (double.tryParse(element.getAttribute("x") ?? "") ?? 1) / metadata.width,
      y: (double.tryParse(element.getAttribute("y") ?? "") ?? 1) / metadata.height,
      widthScale: double.tryParse(element.getAttribute("scaleX") ?? "") ?? 1.0,
      heightScale: double.tryParse(element.getAttribute("scaleY") ?? "") ?? 1.0,
      rotation: double.tryParse(element.getAttribute("rotation") ?? "") ?? 0.0,
      text: element.getAttribute("text") ?? "",
      backgroundColor: sInner.length >= 2 ? ColorUtils.colorFromHex(sInner[1]) ?? Colors.transparent : Colors.transparent,
      fadeInWhen: sInner.length >= 3 ? double.parse(sInner[2]) : 0.0,
      fadeInDuration: sInner.length >= 4 ? double.parse(sInner[3]) : 0.0,
      fadeOut: sInner.length >= 5 && sInner[4] == "true",
      fadeOutWhen: sInner.length >= 6 ? double.parse(sInner[5]) : 0.0,
      fadeOutDuration: sInner.length >= 7 ? double.parse(sInner[6]) : 0.0,
      filterColor: Color(int.tryParse(element.getAttribute("color") ?? "") ?? 0xFF000000),
    )
      ..trigger = sInner.length >= 8 ? ParserUtils.parseTimingTrigger(sInner[7]) : "scenario"
      ..priority = int.parse(element.getAttribute("priority") ?? "1")
      ..triggerOnce = element.getAttribute("triggerOnce") == "true";

    return sText;
  }

  /// Parse CSTimer elements
  static Future<SimObject?> _parseCSTimer(
    xml.XmlElement element,
    xml.XmlDocument simdef,
    Map<String, dynamic> context,
  ) async {
    final elementAttrVal =
        simdef.findAllElements("elementVal").firstWhereOrNull((elementVal) => elementVal.getAttribute("id") == element.getAttribute("id"));

    final sInner = elementAttrVal!.innerText.split(",");
    final sTimer = SimTimer(
      id: element.getAttribute("id") ?? "",
      format: timerFormats[int.tryParse(sInner[0]) ?? 0],
      type: timerTypes[int.tryParse(sInner[1]) ?? 0],
      seconds: int.tryParse(sInner[2]) ?? 0,
      x: (double.tryParse(element.getAttribute("x") ?? "") ?? 1),
      y: (double.tryParse(element.getAttribute("y") ?? "") ?? 1),
    )..triggerOnce = element.getAttribute("triggerOnce") == "true";

    return sTimer;
  }

  /// Parse AudioClip elements
  static Future<SimObject?> _parseAudioClip(
    xml.XmlElement element,
    xml.XmlDocument simdef,
    Map<String, dynamic> context,
  ) async {
    final Directory dir = context["directory"];

    final sAudio = SimSound(
      id: element.getAttribute("id") ?? "",
      path: path.join(dir.path, element.getAttribute("file")!),
      x: (double.tryParse(element.getAttribute("x") ?? "") ?? 1),
      y: (double.tryParse(element.getAttribute("y") ?? "") ?? 1),
      loop: element.getAttribute("loop") == "true",
      mirrorX: element.getAttribute("mirrorX") == "true",
      mirrorY: element.getAttribute("mirrorY") == "true",
      rotation: double.tryParse(element.getAttribute("rotation") ?? "") ?? 0.0,
      width: double.tryParse(element.getAttribute("scaleX") ?? "") ?? 0.0,
      height: double.tryParse(element.getAttribute("scaleY") ?? "") ?? 0.0,
    )
      ..loop = element.getAttribute("loop") == "true"
      ..priority = int.parse(element.getAttribute("priority") ?? "1")
      ..triggerOnce = element.getAttribute("triggerOnce") == "true";

    return sAudio;
  }

  /// Parse default elements (sprites, labels, victims, containers)
  static Future<SimObject?> _parseDefaultElement(
    xml.XmlElement element,
    xml.XmlDocument simdef,
    Map<String, dynamic> context,
  ) async {
    final String? elementType = element.getAttribute("type");
    if (elementType == null) return null;

    final elementAttrVal =
        simdef.findAllElements("elementVal").firstWhereOrNull((elementVal) => elementVal.getAttribute("id") == element.getAttribute("id"));

    // Check if type equals to any container type
    final containerType = containerAssetsMapping.values.firstWhereOrNull((containerType) => elementType.startsWith(containerType));
    if (containerType != null) {
      return parseContainer(
        containerType,
        elementAttrVal?.innerText ?? "",
        element.attributes.fold({}, (previousValue, element) => previousValue..addAll({element.name.toString(): element.value.toString()})),
      );
    }

    // Check if type starts with Victim
    final personType = assetToPeopleMapping.values.firstWhereOrNull((personType) => elementType == "Victim$personType");
    if (personType != null) {
      return parsePerson(
        personType,
        elementAttrVal?.innerText ?? "",
        element.attributes.fold({}, (previousValue, element) => previousValue..addAll({element.name.toString(): element.value.toString()})),
      );
    }

    // Check if type starts with Label
    final labelType = labelToTypeMapping.values.firstWhereOrNull((labelType) => elementType.endsWith(labelType));
    if (labelType != null) {
      return parseLabel(
        labelType,
        elementAttrVal?.innerText ?? "",
        element.attributes.fold({}, (previousValue, element) => previousValue..addAll({element.name.toString(): element.value.toString()})),
      );
    }

    // Parse as sprite
    return await _parseSprite(element, simdef, context, elementType, elementAttrVal);
  }

  /// Parse sprite elements
  static Future<SimObject?> _parseSprite(
    xml.XmlElement element,
    xml.XmlDocument simdef,
    Map<String, dynamic> context,
    String elementType,
    xml.XmlElement? elementAttrVal,
  ) async {
    final SimulationMetadata metadata = context["metadata"];
    final String locationId = context["locationId"];

    final meta = jsonDecode(await rootBundle.loadString("assets/sprites/$elementType-frames-high.json"));
    if (elementAttrVal == null) {
      print("Sprite value not found for element: $element");
    }

    final spriteDetails = (elementAttrVal?.innerText ?? "").split(",");
    final frames = (meta["frames"] as Map<String, dynamic>).values.map(
      (e) {
        return SpriteFrame(
          x: (e["frame"]["x"] as int).toDouble(),
          y: (e["frame"]["y"] as int).toDouble(),
          width: (e["frame"]["w"] as int).toDouble(),
          height: (e["frame"]["h"] as int).toDouble(),
          rotated: e["rotated"] as bool,
        );
      },
    ).toList();

    final img = await decodeImageFromList((await rootBundle.load("assets/sprites/$elementType-frames-high.png")).buffer.asUint8List());

    final spriteElementId = element.getAttribute("id") ?? "";
    final spriteToAdd = SimSprite(
      id: spriteElementId + (spriteElementId.endsWith("-$locationId") ? "" : "-$locationId"),
      name: element.getAttribute("name") ?? spriteElementId,
      img: img,
      frames: frames,
      aseprite: meta as Map<String, dynamic>,
      width: double.parse(meta["meta"]["size"]["w"].toString()),
      height: double.parse(meta["meta"]["size"]["h"].toString()),
      scaleFactor: double.tryParse(meta["meta"]["scale"].toString()) ?? 1,
      scale: double.tryParse(element.getAttribute("scale") ?? "") ?? 1,
      x: (double.tryParse(element.getAttribute("x")!) ?? 1) / metadata.width,
      y: (double.tryParse(element.getAttribute("y")!) ?? 1) / metadata.height,
      widthScale: double.tryParse(element.getAttribute("scaleX") ?? "") ?? 1,
      heightScale: double.tryParse(element.getAttribute("scaleY") ?? "") ?? 1,
      assetName: elementType,
      rotation: double.tryParse(element.getAttribute("rotation") ?? "") ?? 0.0,
      filterColor: spriteDetails.length >= 5
          ? ColorUtils.getColorFromValue(int.parse(spriteDetails[4])).withValues(alpha: double.parse(spriteDetails[5]))
          : Colors.transparent,
      opacity: spriteDetails.length > 6 ? double.tryParse(spriteDetails[6]) ?? 1 : 1,
      framerate: 20,
      speed: spriteDetails.length > 7 ? (double.tryParse(spriteDetails[7]) ?? 20) / 20 : 1,
      fadeInWhen: spriteDetails.length > 8 ? double.tryParse(spriteDetails[8]) ?? 0 : 0,
      fadeInDuration: spriteDetails.length > 9 ? double.tryParse(spriteDetails[9]) ?? 0 : 0,
      mirrorX: spriteDetails.length > 10 ? (spriteDetails[10] == "true") : false,
      mirrorY: spriteDetails.length > 11 ? (spriteDetails[11] == "true") : false,
      pinch: spriteDetails.length > 12 ? double.tryParse(spriteDetails[12]) : null,
      fadeOut: spriteDetails.length > 13 ? (spriteDetails[13] == "true") : false,
      fadeOutWhen: spriteDetails.length > 14 ? double.tryParse(spriteDetails[14]) ?? 0 : 0,
      fadeOutDuration: spriteDetails.length > 15 ? double.tryParse(spriteDetails[15]) ?? 0 : 0,
    )
      ..trigger = spriteDetails.length > 16 ? ParserUtils.parseTimingTrigger(spriteDetails[16]) : "scenario"
      ..priority = int.parse(element.getAttribute("priority") ?? "1")
      ..triggerOnce = element.getAttribute("triggerOnce") == "true";

    return spriteToAdd;
  }
}
