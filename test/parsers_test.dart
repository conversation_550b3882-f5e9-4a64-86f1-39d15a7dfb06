import 'package:flutter_test/flutter_test.dart';
import 'package:xml/xml.dart' as xml;
import 'dart:io';

import 'package:simsushare_player/utils/parsers.dart';
import 'package:simsushare_player/models/Simulation.dart';
import 'package:simsushare_player/models/Mask.dart';

void main() {
  group('Parser Utility Functions Tests', () {
    test('should parse mask definition correctly', () {
      const definition = "true,false,2,sprite1,sprite2,100c200x150c250";
      const maskId = "test-mask";
      const locationId = "loc1";

      final mask = parseMask(maskId, definition, locationId);

      expect(mask.id, equals(maskId));
      expect(mask.locationId, equals(locationId));
      expect(mask.type, equals(MaskType.showWithin)); // false means showWithin
      expect(mask.coordinates.length, equals(2));
      expect(mask.coordinates[0].x, equals(100.0));
      expect(mask.coordinates[0].y, equals(200.0));
      expect(mask.coordinates[1].x, equals(150.0));
      expect(mask.coordinates[1].y, equals(250.0));
    });

    test('should handle invalid mask definition', () {
      const definition = "invalid"; // Too short
      const maskId = "test-mask";
      const locationId = "loc1";

      expect(
        () => parseMask(maskId, definition, locationId),
        throwsA(isA<ArgumentError>()),
      );
    });

    test('should parse mask sprites correctly', () {
      const definition = "true,false,2,sprite1,sprite2,100c200x150c250";
      
      final sprites = parseMaskSprites(definition);

      expect(sprites.length, equals(2));
      expect(sprites[0], equals('sprite1'));
      expect(sprites[1], equals('sprite2'));
    });

    test('should parse sim objects in mask correctly', () {
      const definition = "true,false,3,obj1,obj2,obj3,100c200x150c250";
      
      final objects = parseSimObjectsInMask(definition);

      expect(objects.length, equals(3));
      expect(objects[0], equals('obj1'));
      expect(objects[1], equals('obj2'));
      expect(objects[2], equals('obj3'));
    });

    test('should parse timing trigger correctly', () {
      expect(parseTimingTrigger('0'), equals('scenario'));
      expect(parseTimingTrigger('1'), equals('location'));
      expect(parseTimingTrigger('2'), equals('state'));
      expect(parseTimingTrigger('scenario'), equals('scenario'));
      expect(parseTimingTrigger('location'), equals('location'));
      expect(parseTimingTrigger('state'), equals('state'));
    });
  });

  group('XML Parsing Functions Tests', () {
    late xml.XmlDocument testSimdef;

    setUp(() {
      testSimdef = xml.XmlDocument.parse('''
        <sim id="test-sim" title="Test Simulation" width="1200" height="800" backgroundColor="0x222222">
          <header>
            <summary></summary>
            <description></description>
          </header>
          <varTable>
            <variable id="CURRENT_SIM_STATE" type="string" default="state1" desc="current state"/>
            <variable id="CURRENT_LOCATION" type="string" default="loc1" desc="current location"/>
          </varTable>
          <environ>
            <locations>
              <location id="loc1" name="Location 1" brightness="0.5">
                <element type="CSPic" id="bg" file="background.jpg" x="600" y="400"/>
                <element type="CSShape" id="shape1" x="100" y="200" shape="rectangle"/>
              </location>
              <location id="loc2" name="Location 2" brightness="0.8">
                <element type="CSText" id="text1" x="300" y="400" text="Hello World"/>
              </location>
            </locations>
            <navigations>
              <nav dir="north" fromID="loc1" toID="loc2"/>
              <nav dir="south" fromID="loc2" toID="loc1"/>
            </navigations>
          </environ>
          <environstates>
            <states>
              <state id="state1" name="Initial State"/>
              <state id="state2" name="Second State"/>
            </states>
          </environstates>
          <simFrames>
            <simFrame states="state1" locs="loc1">
              <elementVal id="shape1">out,0,0,0.5,120,0.8,1.0,rectangle,0,0,false,false,1.0,false,0,0,0,scenario</elementVal>
            </simFrame>
          </simFrames>
        </sim>
      ''');
    });

    test('should parse simulation metadata correctly', () {
      // This tests the internal _parseSimulationMetadata function indirectly
      // by checking if the parsed scenario has correct metadata
      final tempDir = Directory.systemTemp.createTempSync('test_sim');
      
      expect(() async {
        final scenario = await parseSimDef(testSimdef.toXmlString(), tempDir);
        
        expect(scenario.id, equals('test-sim'));
        expect(scenario.name, equals('Test Simulation'));
        expect(scenario.width, equals(1200.0));
        expect(scenario.height, equals(800.0));
      }, returnsNormally);
      
      tempDir.deleteSync(recursive: true);
    });

    test('should parse navigations correctly', () {
      final tempDir = Directory.systemTemp.createTempSync('test_sim');
      
      expect(() async {
        final scenario = await parseSimDef(testSimdef.toXmlString(), tempDir);
        
        expect(scenario.navigations.length, equals(2));
        expect(scenario.navigations[0].direction, equals('north'));
        expect(scenario.navigations[0].from, equals('loc1'));
        expect(scenario.navigations[0].to, equals('loc2'));
        expect(scenario.navigations[1].direction, equals('south'));
        expect(scenario.navigations[1].from, equals('loc2'));
        expect(scenario.navigations[1].to, equals('loc1'));
      }, returnsNormally);
      
      tempDir.deleteSync(recursive: true);
    });

    test('should parse states correctly', () {
      final tempDir = Directory.systemTemp.createTempSync('test_sim');
      
      expect(() async {
        final scenario = await parseSimDef(testSimdef.toXmlString(), tempDir);
        
        expect(scenario.states.length, equals(2));
        expect(scenario.states[0].id, equals('state1'));
        expect(scenario.states[0].name, equals('Initial State'));
        expect(scenario.states[1].id, equals('state2'));
        expect(scenario.states[1].name, equals('Second State'));
      }, returnsNormally);
      
      tempDir.deleteSync(recursive: true);
    });

    test('should parse locations correctly', () {
      final tempDir = Directory.systemTemp.createTempSync('test_sim');
      
      expect(() async {
        final scenario = await parseSimDef(testSimdef.toXmlString(), tempDir);
        
        expect(scenario.locations.length, equals(2));
        expect(scenario.locations[0].id, equals('loc1'));
        expect(scenario.locations[0].name, equals('Location 1'));
        expect(scenario.locations[0].imageBrightness, equals(0.5));
        expect(scenario.locations[1].id, equals('loc2'));
        expect(scenario.locations[1].name, equals('Location 2'));
        expect(scenario.locations[1].imageBrightness, equals(0.8));
      }, returnsNormally);
      
      tempDir.deleteSync(recursive: true);
    });
  });

  group('Error Handling Tests', () {
    test('should handle invalid XML gracefully', () {
      const invalidXml = '<invalid><unclosed>';
      final tempDir = Directory.systemTemp.createTempSync('test_sim');
      
      expect(
        () async => await parseSimDef(invalidXml, tempDir),
        throwsA(isA<xml.XmlParserException>()),
      );
      
      tempDir.deleteSync(recursive: true);
    });

    test('should handle missing sim element', () {
      const xmlWithoutSim = '<root><other></other></root>';
      final tempDir = Directory.systemTemp.createTempSync('test_sim');
      
      expect(
        () async => await parseSimDef(xmlWithoutSim, tempDir),
        throwsA(isA<ArgumentError>()),
      );
      
      tempDir.deleteSync(recursive: true);
    });
  });

  group('Integration Tests', () {
    test('should parse and generate consistent XML', () async {
      final tempDir = Directory.systemTemp.createTempSync('test_sim');
      
      // Create a simple test file
      final testFile = File('${tempDir.path}/test.txt');
      await testFile.writeAsString('test content');
      
      const simdefXml = '''
        <sim id="test" title="Test" width="1200" height="800">
          <header><summary/><description/><attachments/><keywords/><categories id=""/><history/></header>
          <varTable>
            <variable id="CURRENT_SIM_STATE" type="string" default="state1" desc="current state"/>
            <variable id="CURRENT_LOCATION" type="string" default="loc1" desc="current location"/>
          </varTable>
          <environ>
            <locations>
              <location id="loc1" name="Test Location" brightness="0.5"/>
            </locations>
            <navigations/>
          </environ>
          <environstates>
            <states>
              <state id="state1" name="Test State"/>
            </states>
          </environstates>
          <simFrames/>
          <plugins/>
          <triggeractions><actions/><triggers/><mappings/></triggeractions>
        </sim>
      ''';
      
      // Parse the XML into a scenario
      final scenario = await parseSimDef(simdefXml, tempDir);
      
      // Generate XML from the scenario
      final generatedXml = generateSimDef(scenario);
      
      // Verify the generated XML is valid
      expect(() => xml.XmlDocument.parse(generatedXml), returnsNormally);
      
      // Verify key elements are present
      final doc = xml.XmlDocument.parse(generatedXml);
      final simElement = doc.getElement('sim');
      expect(simElement, isNotNull);
      expect(simElement!.getAttribute('id'), equals('test'));
      expect(simElement.getAttribute('title'), equals('Test'));
      
      tempDir.deleteSync(recursive: true);
    });
  });

  group('Performance Tests', () {
    test('should parse large simdef efficiently', () async {
      final tempDir = Directory.systemTemp.createTempSync('test_sim');
      
      // Create a simdef with many locations and elements
      final largeSimdef = _createLargeSimdef(50, 10); // 50 locations, 10 elements each
      
      final stopwatch = Stopwatch()..start();
      final scenario = await parseSimDef(largeSimdef, tempDir);
      stopwatch.stop();
      
      expect(scenario.locations.length, equals(50));
      expect(stopwatch.elapsedMilliseconds, lessThan(5000)); // Should complete in under 5 seconds
      
      tempDir.deleteSync(recursive: true);
    });
  });
}

/// Helper function to create a large simdef for performance testing
String _createLargeSimdef(int locationCount, int elementsPerLocation) {
  final buffer = StringBuffer();
  buffer.write('''
    <sim id="large-test" title="Large Test" width="1200" height="800">
      <header><summary/><description/><attachments/><keywords/><categories id=""/><history/></header>
      <varTable>
        <variable id="CURRENT_SIM_STATE" type="string" default="state1" desc="current state"/>
        <variable id="CURRENT_LOCATION" type="string" default="loc1" desc="current location"/>
      </varTable>
      <environ>
        <locations>
  ''');
  
  for (int i = 1; i <= locationCount; i++) {
    buffer.write('<location id="loc$i" name="Location $i" brightness="0.5">');
    
    for (int j = 1; j <= elementsPerLocation; j++) {
      buffer.write('<element type="CSShape" id="shape${i}_$j" x="${j * 50}" y="${j * 30}" shape="rectangle"/>');
    }
    
    buffer.write('</location>');
  }
  
  buffer.write('''
        </locations>
        <navigations/>
      </environ>
      <environstates>
        <states>
          <state id="state1" name="Test State"/>
        </states>
      </environstates>
      <simFrames/>
      <plugins/>
      <triggeractions><actions/><triggers/><mappings/></triggeractions>
    </sim>
  ''');
  
  return buffer.toString();
}
