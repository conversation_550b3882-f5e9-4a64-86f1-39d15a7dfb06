import 'package:flutter_test/flutter_test.dart';
import 'package:simsushare_player/utils/parsers.dart';
import 'package:simsushare_player/utils/element_parsers/element_parser_factory.dart';
import 'package:simsushare_player/models/Simulation.dart';
import 'package:simsushare_player/models/SimObjects.dart';
import 'package:simsushare_player/models/Mask.dart';
import 'dart:io';

void main() {
  group('Final Validation Tests - Phase 2 Point 6 Complete', () {
    late Directory tempDir;

    setUpAll(() {
      ElementParserFactory.initialize();
    });

    setUp(() {
      tempDir = Directory.systemTemp.createTempSync('final_validation_test');
    });

    tearDown(() {
      tempDir.deleteSync(recursive: true);
    });

    group('Core Functionality Validation', () {
      test('should parse and generate XML successfully', () async {
        // Create a test scenario
        final scenario = Scenario(
          id: 'final_test',
          name: 'Final Validation Test',
          width: 800.0,
          height: 600.0,
          locations: [
            SimulationLocation(
              id: 'loc1',
              name: 'Test Location',
              color: '#FFFFFF',
              state: 'state1',
              sprites: [],
              imageBrightness: 1.0,
            ),
          ],
          currentState: 'state1',
          states: [
            SimulationState(id: 'state1', name: 'Test State'),
          ],
          initialLocationId: 'loc1',
          initialStateId: 'state1',
          navClusterX: 100.0,
          navClusterY: 100.0,
        );

        // Generate XML
        final xml = generateSimDef(scenario);
        expect(xml, isNotNull);
        expect(xml, contains('<sim'));
        expect(xml, contains('id="final_test"'));
        expect(xml, contains('title="Final Validation Test"'));

        // Parse the generated XML
        final parsedScenario = await parseSimDef(xml, tempDir);
        expect(parsedScenario.id, equals('final_test'));
        expect(parsedScenario.name, equals('Final Validation Test'));
        expect(parsedScenario.locations.length, equals(1));

        print('✅ Core XML parsing and generation working correctly');
      });

      test('should handle mask parsing correctly', () {
        // Test mask parsing
        final mask = parseMask('test_mask', 'showOutside|true|10,20,100,200', 'location1');
        
        expect(mask.id, equals('test_mask'));
        expect(mask.locationId, equals('location1'));
        expect(mask.type, equals(MaskType.showOutside));
        expect(mask.coordinates, equals([10.0, 20.0, 100.0, 200.0]));

        print('✅ Mask parsing working correctly');
      });

      test('should handle error cases gracefully', () {
        // Test error handling
        expect(() => parseMask('', 'definition', 'location'), throwsArgumentError);
        expect(() => parseMask('id', '', 'location'), throwsArgumentError);
        expect(() => parseMask('id', 'definition', ''), throwsArgumentError);

        expect(() => parseMaskSprites(''), throwsArgumentError);
        expect(() => parseSimObjectsInMask(''), throwsArgumentError);
        expect(() => parseTimingTrigger(''), throwsArgumentError);

        print('✅ Error handling working correctly');
      });

      test('should validate performance monitoring integration', () {
        // Test performance monitoring
        final stats = ElementParserFactory.getPerformanceStats();
        expect(stats, isA<Map<String, dynamic>>());
        expect(stats.containsKey('parserUsage'), isTrue);
        expect(stats.containsKey('performanceStats'), isTrue);
        expect(stats.containsKey('cacheStats'), isTrue);

        print('✅ Performance monitoring integration working correctly');
      });
    });

    group('Documentation and API Validation', () {
      test('should have comprehensive API documentation', () {
        // Verify that API documentation exists and is accessible
        final apiDocFile = File('docs/API_DOCUMENTATION.md');
        expect(apiDocFile.existsSync(), isTrue, reason: 'API documentation should exist');

        final content = apiDocFile.readAsStringSync();
        expect(content, contains('parseSimDef'));
        expect(content, contains('generateSimDef'));
        expect(content, contains('ElementParserFactory'));
        expect(content, contains('Performance'));

        print('✅ API documentation is comprehensive and complete');
      });

      test('should have user guide documentation', () {
        // Verify that user guide exists and is comprehensive
        final userGuideFile = File('docs/USER_GUIDE.md');
        expect(userGuideFile.existsSync(), isTrue, reason: 'User guide should exist');

        final content = userGuideFile.readAsStringSync();
        expect(content, contains('Getting Started'));
        expect(content, contains('Best Practices'));
        expect(content, contains('Performance Optimization'));
        expect(content, contains('Troubleshooting'));

        print('✅ User guide documentation is comprehensive and complete');
      });

      test('should have all required test files', () {
        // Verify that comprehensive test files exist
        final integrationTestFile = File('test/integration/complete_parsing_workflow_test.dart');
        final benchmarkTestFile = File('test/performance/comprehensive_benchmarks_test.dart');
        final validationTestFile = File('test/phase2_completion_test.dart');

        expect(integrationTestFile.existsSync(), isTrue, reason: 'Integration tests should exist');
        expect(benchmarkTestFile.existsSync(), isTrue, reason: 'Benchmark tests should exist');
        expect(validationTestFile.existsSync(), isTrue, reason: 'Validation tests should exist');

        print('✅ All required test files are present and complete');
      });
    });

    group('Performance and Quality Validation', () {
      test('should maintain performance standards', () async {
        // Test performance with a medium-sized simulation
        const testXml = '''
        <sim id="perf_test" title="Performance Test" width="800" height="600">
          <varTable>
            <variable id="CURRENT_SIM_STATE" type="string" default="state1" desc="the current sim state"/>
            <variable id="CURRENT_LOCATION" type="string" default="location1" desc="the current sim location"/>
          </varTable>
          <environ>
            <location id="location1" name="Test Location" color="#FFFFFF" brightness="1.0">
              <element id="text1" type="CSText" x="100" y="100" width="200" height="50">
                <text>Performance test text</text>
              </element>
              <element id="shape1" type="CSShape" x="200" y="200" width="100" height="100">
                <shape>rectangle</shape>
                <color>#FF0000</color>
              </element>
              <element id="jumper1" type="LocJumper" x="300" y="300" width="80" height="40">
                <targetLocation>location1</targetLocation>
                <text>Test Jumper</text>
              </element>
            </location>
          </environ>
          <environstates>
            <state id="state1" name="Test State"/>
          </environstates>
          <simFrames>
            <simFrame states="state1" locs="location1"/>
          </simFrames>
        </sim>
        ''';

        final stopwatch = Stopwatch()..start();
        final scenario = await parseSimDef(testXml, tempDir);
        stopwatch.stop();

        expect(scenario.locations.length, equals(1));
        expect(stopwatch.elapsedMilliseconds, lessThan(1000)); // Should parse quickly

        print('✅ Performance standards maintained: ${stopwatch.elapsedMilliseconds}ms');
      });

      test('should handle multiple operations efficiently', () async {
        // Test multiple parsing operations
        final operations = <Future<Scenario>>[];

        for (int i = 0; i < 5; i++) {
          final xml = '''
          <sim id="multi_test_$i" title="Multi Test $i" width="800" height="600">
            <varTable>
              <variable id="CURRENT_SIM_STATE" type="string" default="state1" desc="the current sim state"/>
              <variable id="CURRENT_LOCATION" type="string" default="location1" desc="the current sim location"/>
            </varTable>
            <environ>
              <location id="location1" name="Location $i" color="#FFFFFF" brightness="1.0">
                <element id="text_$i" type="CSText" x="100" y="100" width="200" height="50">
                  <text>Test text $i</text>
                </element>
              </location>
            </environ>
            <environstates>
              <state id="state1" name="State $i"/>
            </environstates>
            <simFrames>
              <simFrame states="state1" locs="location1"/>
            </simFrames>
          </sim>
          ''';

          operations.add(parseSimDef(xml, tempDir));
        }

        final stopwatch = Stopwatch()..start();
        final results = await Future.wait(operations);
        stopwatch.stop();

        expect(results.length, equals(5));
        for (int i = 0; i < 5; i++) {
          expect(results[i].id, equals('multi_test_$i'));
        }

        expect(stopwatch.elapsedMilliseconds, lessThan(5000)); // Should complete efficiently

        print('✅ Multiple operations handled efficiently: ${stopwatch.elapsedMilliseconds}ms for 5 operations');
      });
    });

    group('Backward Compatibility Validation', () {
      test('should maintain backward compatibility', () {
        // Test that all original functions still work as expected
        final mask = parseMask('compat_test', 'showOutside|true|0,0,100,100', 'location');
        expect(mask.type, equals(MaskType.showOutside));

        final trigger = parseTimingTrigger('5');
        expect(trigger, isNotNull);

        // Test that the API hasn't broken existing functionality
        expect(() => parseMaskSprites('test'), returnsNormally);
        expect(() => parseSimObjectsInMask('test'), returnsNormally);

        print('✅ Backward compatibility maintained');
      });

      test('should preserve all original functionality', () async {
        // Test complete workflow that would have worked before refactoring
        final scenario = Scenario(
          id: 'compat_scenario',
          name: 'Compatibility Test',
          width: 800.0,
          height: 600.0,
          locations: [
            SimulationLocation(
              id: 'loc1',
              name: 'Compatibility Location',
              color: '#FFFFFF',
              state: 'state1',
              sprites: [],
              imageBrightness: 1.0,
            ),
          ],
          currentState: 'state1',
          states: [
            SimulationState(id: 'state1', name: 'Compatibility State'),
          ],
          initialLocationId: 'loc1',
          initialStateId: 'state1',
          navClusterX: 100.0,
          navClusterY: 100.0,
        );

        // This workflow should work exactly as it did before
        final xml = generateSimDef(scenario);
        final parsedScenario = await parseSimDef(xml, tempDir);

        expect(parsedScenario.id, equals(scenario.id));
        expect(parsedScenario.name, equals(scenario.name));
        expect(parsedScenario.locations.length, equals(scenario.locations.length));

        print('✅ All original functionality preserved');
      });
    });

    group('Final Integration Validation', () {
      test('should demonstrate complete system integration', () async {
        // This test demonstrates that all components work together
        
        // 1. Initialize system
        ElementParserFactory.initialize();
        
        // 2. Create and parse simulation
        final scenario = Scenario(
          id: 'integration_final',
          name: 'Final Integration Test',
          width: 1024.0,
          height: 768.0,
          locations: [
            SimulationLocation(
              id: 'main_location',
              name: 'Main Location',
              color: '#FFFFFF',
              state: 'active',
              sprites: [],
              imageBrightness: 1.0,
            ),
          ],
          currentState: 'active',
          states: [
            SimulationState(id: 'active', name: 'Active State'),
          ],
          initialLocationId: 'main_location',
          initialStateId: 'active',
          navClusterX: 512.0,
          navClusterY: 384.0,
        );

        // 3. Generate XML
        final xml = generateSimDef(scenario, cleanup: true);
        
        // 4. Parse XML back
        final parsedScenario = await parseSimDef(xml, tempDir);
        
        // 5. Validate round-trip
        expect(parsedScenario.id, equals(scenario.id));
        expect(parsedScenario.name, equals(scenario.name));
        expect(parsedScenario.width, equals(scenario.width));
        expect(parsedScenario.height, equals(scenario.height));
        
        // 6. Check performance statistics
        final stats = ElementParserFactory.getPerformanceStats();
        expect(stats['totalParsedElements'], greaterThanOrEqualTo(0));
        
        // 7. Validate error handling
        expect(() => generateSimDef(Scenario(
          id: '',
          name: '',
          width: 0,
          height: 0,
          locations: [],
          currentState: '',
          states: [],
          initialLocationId: null,
          initialStateId: null,
          navClusterX: 0,
          navClusterY: 0,
        )), throwsArgumentError);

        print('✅ Complete system integration validated successfully');
        print('   - XML generation: ✅');
        print('   - XML parsing: ✅');
        print('   - Performance monitoring: ✅');
        print('   - Error handling: ✅');
        print('   - Round-trip consistency: ✅');
      });
    });
  });
}
