import 'package:flutter_test/flutter_test.dart';
import 'package:flutter/material.dart';
import 'package:xml/xml.dart' as xml;

import 'package:simsushare_player/utils/element_parsers/element_parser_factory.dart';
import 'package:simsushare_player/utils/element_parsers/image_parser.dart';
import 'package:simsushare_player/utils/element_parsers/shape_parser.dart';
import 'package:simsushare_player/utils/element_parsers/text_parser.dart';
import 'package:simsushare_player/utils/element_parsers/audio_parser.dart';
import 'package:simsushare_player/utils/element_parsers/locjumper_parser.dart';
import 'package:simsushare_player/utils/element_parsers/timer_parser.dart';
import 'package:simsushare_player/models/SimObjects.dart';
import 'package:simsushare_player/models/Simulation.dart';

void main() {
  group('Element Parser Factory Tests', () {
    setUpAll(() {
      ElementParserFactory.initialize();
    });

    test('should initialize all parsers correctly', () {
      expect(ElementParserFactory.getParser('CSPic'), isA<ImageParser>());
      expect(ElementParserFactory.getParser('CSShape'), isA<ShapeParser>());
      expect(ElementParserFactory.getParser('CSText'), isA<TextParser>());
      expect(ElementParserFactory.getParser('AudioClip'), isA<AudioParser>());
      expect(ElementParserFactory.getParser('LocJumper'), isA<LocJumperParser>());
      expect(ElementParserFactory.getParser('CSTimer'), isA<TimerParser>());
    });

    test('should return null for unknown element types', () {
      expect(ElementParserFactory.getParser('UnknownType'), isNull);
    });
  });

  group('Image Parser Tests', () {
    late ImageParser parser;
    late Map<String, dynamic> context;

    setUp(() {
      parser = ImageParser();
      context = {
        'metadata': SimulationMetadata(
          id: 'test',
          name: 'Test Sim',
          width: 1200.0,
          height: 800.0,
          backgroundColor: '#000000',
          navClusterX: 100.0,
          navClusterY: 100.0,
        ),
        'locationId': 'loc1',
        'directory': null,
        'allFileNames': ['test.png', 'background.jpg'],
        'simMasks': [],
      };
    });

    test('should validate element correctly', () {
      final validElement = xml.XmlElement(xml.XmlName('element'));
      validElement.setAttribute('type', 'CSPic');
      validElement.setAttribute('id', 'test-image');
      validElement.setAttribute('x', '100');
      validElement.setAttribute('y', '200');

      expect(parser.validateElement(validElement), isTrue);
    });

    test('should reject invalid element', () {
      final invalidElement = xml.XmlElement(xml.XmlName('element'));
      // Missing required attributes

      expect(parser.validateElement(invalidElement), isFalse);
    });

    test('should extract common properties correctly', () {
      final element = xml.XmlElement(xml.XmlName('element'));
      element.setAttribute('id', 'test-image');
      element.setAttribute('x', '100');
      element.setAttribute('y', '200');
      element.setAttribute('scaleX', '1.5');
      element.setAttribute('scaleY', '2.0');
      element.setAttribute('rotation', '45.0');
      element.setAttribute('priority', '5');

      final props = parser.extractCommonProperties(element);

      expect(props['id'], equals('test-image'));
      expect(props['x'], equals('100'));
      expect(props['y'], equals('200'));
      expect(props['scaleX'], equals('1.5'));
      expect(props['scaleY'], equals('2.0'));
      expect(props['rotation'], equals('45.0'));
      expect(props['priority'], equals('5'));
    });
  });

  group('Shape Parser Tests', () {
    late ShapeParser parser;
    late Map<String, dynamic> context;
    late xml.XmlDocument simdef;

    setUp(() {
      parser = ShapeParser();
      context = {
        'metadata': SimulationMetadata(
          id: 'test',
          name: 'Test Sim',
          width: 1200.0,
          height: 800.0,
          backgroundColor: '#000000',
          navClusterX: 100.0,
          navClusterY: 100.0,
        ),
        'locationId': 'loc1',
      };

      // Create a mock simdef with elementVal
      simdef = xml.XmlDocument.parse('''
        <sim>
          <simFrames>
            <simFrame>
              <elementVal id="test-shape">out,0,0,0.5,120,0.8,1.0,rectangle,0,0,false,false,1.0,false,0,0,0,scenario</elementVal>
            </simFrame>
          </simFrames>
        </sim>
      ''');
    });

    test('should parse shape element correctly', () {
      final element = xml.XmlElement(xml.XmlName('element'));
      element.setAttribute('type', 'CSShape');
      element.setAttribute('id', 'test-shape');
      element.setAttribute('x', '100');
      element.setAttribute('y', '200');
      element.setAttribute('scaleX', '1.5');
      element.setAttribute('scaleY', '2.0');
      element.setAttribute('shape', 'rectangle');

      final shape = parser.parseElement(element, simdef, context);

      expect(shape, isA<SimShape>());
      expect(shape.id, equals('test-shape'));
      expect(shape.shape, equals('rectangle'));
      expect(shape.x, closeTo(100.0 / 1200.0, 0.001)); // Normalized to metadata width
      expect(shape.y, closeTo(200.0 / 800.0, 0.001)); // Normalized to metadata height
    });

    test('should generate XML element correctly', () {
      final shape = SimShape(
        id: 'test-shape',
        x: 0.1, // 10% of width
        y: 0.2, // 20% of height
        width: 100.0,
        height: 50.0,
        widthScale: 1.5,
        heightScale: 2.0,
        shape: 'circle',
      );

      final xmlElement = parser.generateElement(shape, context);

      expect(xmlElement.getAttribute('type'), equals('CSShape'));
      expect(xmlElement.getAttribute('id'), equals('test-shape'));
      expect(xmlElement.getAttribute('shape'), equals('circle'));
      expect(xmlElement.getAttribute('x'), equals('120.0')); // 0.1 * 1200
      expect(xmlElement.getAttribute('y'), equals('160.0')); // 0.2 * 800
    });
  });

  group('Text Parser Tests', () {
    late TextParser parser;
    late Map<String, dynamic> context;
    late xml.XmlDocument simdef;

    setUp(() {
      parser = TextParser();
      context = {
        'metadata': SimulationMetadata(
          id: 'test',
          name: 'Test Sim',
          width: 1200.0,
          height: 800.0,
          backgroundColor: '#000000',
          navClusterX: 100.0,
          navClusterY: 100.0,
        ),
        'locationId': 'loc1',
      };

      simdef = xml.XmlDocument.parse('''
        <sim>
          <simFrames>
            <simFrame>
              <elementVal id="test-text">Hello%20World,#FFFFFF,0,0,false,0,0,scenario</elementVal>
            </simFrame>
          </simFrames>
        </sim>
      ''');
    });

    test('should parse text element correctly', () {
      final element = xml.XmlElement(xml.XmlName('element'));
      element.setAttribute('type', 'CSText');
      element.setAttribute('id', 'test-text');
      element.setAttribute('x', '100');
      element.setAttribute('y', '200');
      element.setAttribute('text', 'Hello World');

      final text = parser.parseElement(element, simdef, context);

      expect(text, isA<SimText>());
      expect(text.id, equals('test-text'));
      expect(text.text, equals('Hello World'));
    });
  });

  group('Audio Parser Tests', () {
    late AudioParser parser;
    late Map<String, dynamic> context;

    setUp(() {
      parser = AudioParser();
      context = {
        'metadata': SimulationMetadata(
          id: 'test',
          name: 'Test Sim',
          width: 1200.0,
          height: 800.0,
          backgroundColor: '#000000',
          navClusterX: 100.0,
          navClusterY: 100.0,
        ),
        'locationId': 'loc1',
      };
    });

    test('should parse audio element correctly', () {
      final element = xml.XmlElement(xml.XmlName('element'));
      element.setAttribute('type', 'AudioClip');
      element.setAttribute('id', 'test-audio');
      element.setAttribute('file', 'sound.mp3');
      element.setAttribute('loop', 'true');
      element.setAttribute('x', '0');
      element.setAttribute('y', '0');

      final simdef = xml.XmlDocument.parse('<sim></sim>');
      final audio = parser.parseElement(element, simdef, context);

      expect(audio, isA<SimSound>());
      expect(audio.id, equals('test-audio'));
      expect(audio.path, equals('sound.mp3'));
      expect(audio.loop, isTrue);
    });
  });

  group('Error Handling Tests', () {
    test('should handle missing metadata gracefully', () {
      final parser = ShapeParser();
      final element = xml.XmlElement(xml.XmlName('element'));
      element.setAttribute('type', 'CSShape');
      element.setAttribute('id', 'test');

      final context = <String, dynamic>{}; // Missing metadata
      final simdef = xml.XmlDocument.parse('<sim></sim>');

      expect(
        () => parser.parseElement(element, simdef, context),
        throwsA(isA<ArgumentError>()),
      );
    });

    test('should handle invalid XML gracefully', () {
      final parser = ImageParser();
      final element = xml.XmlElement(xml.XmlName('invalid'));

      expect(parser.validateElement(element), isFalse);
    });
  });

  group('Performance Tests', () {
    test('should parse multiple elements efficiently', () {
      ElementParserFactory.initialize();
      
      final stopwatch = Stopwatch()..start();
      
      // Parse 100 elements
      for (int i = 0; i < 100; i++) {
        final element = xml.XmlElement(xml.XmlName('element'));
        element.setAttribute('type', 'CSShape');
        element.setAttribute('id', 'shape-$i');
        element.setAttribute('x', '100');
        element.setAttribute('y', '200');
        
        final parser = ElementParserFactory.getParser('CSShape');
        expect(parser, isNotNull);
      }
      
      stopwatch.stop();
      
      // Should complete in reasonable time (less than 1 second)
      expect(stopwatch.elapsedMilliseconds, lessThan(1000));
    });
  });
}
