import 'package:flutter_test/flutter_test.dart';
import 'package:simsushare_player/utils/performance/performance_monitor.dart';
import 'package:simsushare_player/utils/performance/asset_cache.dart';
import 'package:simsushare_player/utils/performance/lazy_loader.dart';
import 'package:simsushare_player/utils/performance/performance_benchmark.dart';

void main() {
  group('Performance Optimization Tests', () {
    late PerformanceMonitor monitor;
    late AssetCache cache;
    late LazyLoader loader;

    setUp(() {
      monitor = PerformanceMonitor();
      cache = AssetCache();
      loader = LazyLoader();

      // Clear all caches before each test
      monitor.clear();
      cache.clearAll();
      loader.clear();
    });

    group('PerformanceMonitor', () {
      test('should track operation timing correctly', () async {
        // Test synchronous operation timing
        final result = monitor.timeOperation('test_sync', () {
          // Simulate some work
          var sum = 0;
          for (int i = 0; i < 1000; i++) {
            sum += i;
          }
          return sum;
        });

        expect(result, equals(499500));

        final stats = monitor.getStats('test_sync');
        expect(stats, isNotNull);
        expect(stats!.totalOperations, equals(1));
        expect(stats.averageTimeMs, greaterThan(0));
      });

      test('should track async operation timing correctly', () async {
        final result = await monitor.timeAsyncOperation('test_async', () async {
          await Future.delayed(const Duration(milliseconds: 10));
          return 'completed';
        });

        expect(result, equals('completed'));

        final stats = monitor.getStats('test_async');
        expect(stats, isNotNull);
        expect(stats!.totalOperations, equals(1));
        expect(stats.averageTimeMs, greaterThanOrEqualTo(10));
      });

      test('should calculate statistics correctly', () {
        // Run multiple operations
        for (int i = 0; i < 5; i++) {
          monitor.timeOperation('multi_test', () {
            // Simulate variable work
            var sum = 0;
            for (int j = 0; j < (i + 1) * 100; j++) {
              sum += j;
            }
            return sum;
          });
        }

        final stats = monitor.getStats('multi_test');
        expect(stats, isNotNull);
        expect(stats!.totalOperations, equals(5));
        expect(stats.minTimeMs, greaterThan(0));
        expect(stats.maxTimeMs, greaterThanOrEqualTo(stats.minTimeMs));
        expect(stats.averageTimeMs, greaterThan(0));
      });

      test('should provide performance summary', () {
        // Run various operations
        monitor.timeOperation('op1', () => 'result1');
        monitor.timeOperation('op2', () => 'result2');
        monitor.timeOperation('op1', () => 'result3');

        final summary = monitor.getSummary();
        expect(summary.totalOperations, equals(3));
        expect(summary.uniqueOperations, equals(2));
        expect(summary.totalTimeMs, greaterThan(0));
      });
    });

    group('AssetCache', () {
      test('should track cache statistics', () {
        final stats = cache.statistics;
        expect(stats, isA<Map<String, dynamic>>());
        expect(stats.containsKey('hits'), isTrue);
        expect(stats.containsKey('misses'), isTrue);
        expect(stats.containsKey('memoryUsage'), isTrue);
      });

      test('should clear caches correctly', () {
        cache.clearAll();
        final stats = cache.statistics;
        expect(stats['hits'], equals(0));
        expect(stats['misses'], equals(0));
        expect(stats['memoryUsage'], equals(0));
      });
    });

    group('LazyLoader', () {
      test('should provide loading statistics', () {
        final stats = loader.getLoadingStats();
        expect(stats, isA<Map<String, dynamic>>());
        expect(stats.containsKey('currentLoads'), isTrue);
        expect(stats.containsKey('queuedLoads'), isTrue);
        expect(stats.containsKey('preloadedAssets'), isTrue);
      });

      test('should clear loading state correctly', () {
        loader.clear();
        final stats = loader.getLoadingStats();
        expect(stats['currentLoads'], equals(0));
        expect(stats['queuedLoads'], equals(0));
        expect(stats['preloadedAssets'], equals(0));
      });

      test('should handle load priorities correctly', () {
        expect(LoadPriority.immediate.value, equals(0));
        expect(LoadPriority.high.value, equals(1));
        expect(LoadPriority.normal.value, equals(2));
        expect(LoadPriority.low.value, equals(3));
        expect(LoadPriority.background.value, equals(4));
      });
    });

    group('Performance Integration', () {
      test('should work together correctly', () async {
        // Test that all performance components work together
        final operationResult = await monitor.timeAsyncOperation('integration_test', () async {
          // Simulate loading with cache
          await Future.delayed(const Duration(milliseconds: 5));
          return 'integration_complete';
        });

        expect(operationResult, equals('integration_complete'));

        // Check that monitoring worked
        final stats = monitor.getStats('integration_test');
        expect(stats, isNotNull);
        expect(stats!.totalOperations, equals(1));

        // Check that cache is tracking
        final cacheStats = cache.statistics;
        expect(cacheStats, isNotNull);

        // Check that loader is available
        final loaderStats = loader.getLoadingStats();
        expect(loaderStats, isNotNull);
      });
    });

    group('Performance Benchmarking', () {
      test('should create benchmark results correctly', () {
        final result = BenchmarkResult(
          filePath: '/test/file.xml',
          iteration: 1,
          startTime: DateTime.now(),
        );

        expect(result.filePath, equals('/test/file.xml'));
        expect(result.iteration, equals(1));
        expect(result.success, isFalse);
        expect(result.error, isNull);
      });

      test('should create benchmark suite correctly', () {
        final suite = BenchmarkSuite(
          startTime: DateTime.now(),
          testFiles: ['/test/file1.xml', '/test/file2.xml'],
          iterations: 3,
        );

        expect(suite.testFiles.length, equals(2));
        expect(suite.iterations, equals(3));
        expect(suite.results, isEmpty);
        expect(suite.endTime, isNull);
      });

      test('should serialize benchmark data correctly', () {
        final result = BenchmarkResult(
          filePath: '/test/file.xml',
          iteration: 1,
          startTime: DateTime.now(),
        );
        result.endTime = DateTime.now();
        result.success = true;
        result.totalElements = 10;
        result.memoryUsage = 1024;

        final json = result.toJson();
        expect(json, isA<Map<String, dynamic>>());
        expect(json['filePath'], equals('/test/file.xml'));
        expect(json['success'], isTrue);
        expect(json['totalElements'], equals(10));
        expect(json['memoryUsage'], equals(1024));
      });
    });

    group('Memory Management', () {
      test('should track memory usage', () {
        final stats = cache.statistics;
        final memoryUsage = stats['memoryUsage'] as int;
        expect(memoryUsage, greaterThanOrEqualTo(0));
      });

      test('should handle cache eviction', () {
        // This test verifies that cache eviction doesn't crash
        cache.clearAll();
        final stats = cache.statistics;
        expect(stats['memoryUsage'], equals(0));
      });
    });

    group('Error Handling', () {
      test('should handle timing errors gracefully', () {
        expect(() {
          monitor.timeOperation('error_test', () {
            throw Exception('Test error');
          });
        }, throwsException);

        // Should still record the operation
        final stats = monitor.getStats('error_test');
        expect(stats, isNotNull);
        expect(stats!.totalOperations, equals(1));
      });

      test('should handle async timing errors gracefully', () async {
        expect(() async {
          await monitor.timeAsyncOperation('async_error_test', () async {
            throw Exception('Async test error');
          });
        }, throwsException);

        // Should still record the operation
        final stats = monitor.getStats('async_error_test');
        expect(stats, isNotNull);
        expect(stats!.totalOperations, equals(1));
      });
    });
  });
}
