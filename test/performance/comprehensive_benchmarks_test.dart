import 'package:flutter_test/flutter_test.dart';
import 'package:simsushare_player/utils/parsers.dart';
import 'package:simsushare_player/utils/element_parsers/element_parser_factory.dart';
import 'package:simsushare_player/utils/performance/performance_monitor.dart';
import 'package:simsushare_player/utils/performance/asset_cache.dart';
import 'package:simsushare_player/utils/performance/performance_benchmark.dart';
import 'package:simsushare_player/models/Simulation.dart';
import 'dart:io';
import 'dart:math';

void main() {
  group('Comprehensive Performance Benchmarks', () {
    late Directory tempDir;
    late PerformanceMonitor monitor;
    late AssetCache cache;
    late PerformanceBenchmark benchmark;

    setUpAll(() {
      ElementParserFactory.initialize();
      monitor = PerformanceMonitor();
      cache = AssetCache();
      benchmark = PerformanceBenchmark();
    });

    setUp(() {
      tempDir = Directory.systemTemp.createTempSync('benchmark_test');
      monitor.clear();
      cache.clearAll();
    });

    tearDown(() {
      tempDir.deleteSync(recursive: true);
    });

    group('Parsing Performance Benchmarks', () {
      test('should benchmark small simulation parsing', () async {
        final testCases = _generateTestCases('small', [
          (5, 5),   // 5 locations, 5 elements each
          (3, 10),  // 3 locations, 10 elements each
          (10, 3),  // 10 locations, 3 elements each
        ]);

        final results = await _runBenchmarkSuite('Small Simulations', testCases, iterations: 5);
        
        // Performance expectations for small simulations
        expect(results.averageParseTime, lessThan(1000)); // < 1 second
        expect(results.successRate, equals(1.0)); // 100% success rate
        expect(results.averageElementsPerSecond, greaterThan(50)); // > 50 elements/sec

        print('📊 Small Simulation Benchmark Results:');
        print('   Average parse time: ${results.averageParseTime.toStringAsFixed(1)}ms');
        print('   Elements per second: ${results.averageElementsPerSecond.toStringAsFixed(0)}');
        print('   Success rate: ${(results.successRate * 100).toStringAsFixed(1)}%');
      });

      test('should benchmark medium simulation parsing', () async {
        final testCases = _generateTestCases('medium', [
          (20, 10),  // 20 locations, 10 elements each
          (15, 15),  // 15 locations, 15 elements each
          (25, 8),   // 25 locations, 8 elements each
        ]);

        final results = await _runBenchmarkSuite('Medium Simulations', testCases, iterations: 3);
        
        // Performance expectations for medium simulations
        expect(results.averageParseTime, lessThan(5000)); // < 5 seconds
        expect(results.successRate, equals(1.0)); // 100% success rate
        expect(results.averageElementsPerSecond, greaterThan(30)); // > 30 elements/sec

        print('📊 Medium Simulation Benchmark Results:');
        print('   Average parse time: ${results.averageParseTime.toStringAsFixed(1)}ms');
        print('   Elements per second: ${results.averageElementsPerSecond.toStringAsFixed(0)}');
        print('   Success rate: ${(results.successRate * 100).toStringAsFixed(1)}%');
      });

      test('should benchmark large simulation parsing', () async {
        final testCases = _generateTestCases('large', [
          (50, 20),  // 50 locations, 20 elements each
          (40, 25),  // 40 locations, 25 elements each
          (60, 15),  // 60 locations, 15 elements each
        ]);

        final results = await _runBenchmarkSuite('Large Simulations', testCases, iterations: 2);
        
        // Performance expectations for large simulations
        expect(results.averageParseTime, lessThan(15000)); // < 15 seconds
        expect(results.successRate, equals(1.0)); // 100% success rate
        expect(results.averageElementsPerSecond, greaterThan(20)); // > 20 elements/sec

        print('📊 Large Simulation Benchmark Results:');
        print('   Average parse time: ${results.averageParseTime.toStringAsFixed(1)}ms');
        print('   Elements per second: ${results.averageElementsPerSecond.toStringAsFixed(0)}');
        print('   Success rate: ${(results.successRate * 100).toStringAsFixed(1)}%');
      });
    });

    group('Memory Performance Benchmarks', () {
      test('should benchmark memory usage patterns', () async {
        final memoryResults = <MemoryBenchmarkResult>[];

        // Test different simulation sizes
        final testSizes = [(10, 5), (20, 10), (30, 15), (40, 20)];

        for (final (locations, elements) in testSizes) {
          cache.clearAll();
          final initialMemory = cache.statistics['memoryUsage'] as int;

          final xml = _generateSimulationXml('memory_test', locations, elements);
          final stopwatch = Stopwatch()..start();
          final scenario = await parseSimDef(xml, tempDir);
          stopwatch.stop();

          final finalMemory = cache.statistics['memoryUsage'] as int;
          final memoryUsed = finalMemory - initialMemory;
          final elementCount = _countElements(scenario);

          memoryResults.add(MemoryBenchmarkResult(
            elementCount: elementCount,
            memoryUsed: memoryUsed,
            parseTime: stopwatch.elapsedMilliseconds,
            memoryPerElement: elementCount > 0 ? memoryUsed / elementCount : 0,
          ));
        }

        // Analyze memory usage patterns
        final avgMemoryPerElement = memoryResults
            .map((r) => r.memoryPerElement)
            .reduce((a, b) => a + b) / memoryResults.length;

        expect(avgMemoryPerElement, lessThan(50000)); // < 50KB per element on average

        print('📊 Memory Usage Benchmark Results:');
        for (final result in memoryResults) {
          print('   ${result.elementCount} elements: ${(result.memoryUsed / 1024).toStringAsFixed(1)}KB (${result.memoryPerElement.toStringAsFixed(0)}B/element)');
        }
        print('   Average memory per element: ${avgMemoryPerElement.toStringAsFixed(0)}B');
      });

      test('should benchmark cache performance', () async {
        final cacheResults = <CacheBenchmarkResult>[];

        // Test cache performance with repeated parsing
        final xml = _generateSimulationXml('cache_test', 15, 10);

        for (int iteration = 1; iteration <= 5; iteration++) {
          final stopwatch = Stopwatch()..start();
          await parseSimDef(xml, tempDir);
          stopwatch.stop();

          final stats = cache.statistics;
          cacheResults.add(CacheBenchmarkResult(
            iteration: iteration,
            parseTime: stopwatch.elapsedMilliseconds,
            hitRate: stats['hitRate'] as double,
            memoryUsage: stats['memoryUsage'] as int,
          ));
        }

        // Cache should improve performance over iterations
        final firstParseTime = cacheResults.first.parseTime;
        final lastParseTime = cacheResults.last.parseTime;
        final finalHitRate = cacheResults.last.hitRate;

        expect(finalHitRate, greaterThan(0.5)); // > 50% hit rate after multiple parses
        expect(lastParseTime, lessThanOrEqualTo(firstParseTime * 1.5)); // Not significantly slower

        print('📊 Cache Performance Benchmark Results:');
        for (final result in cacheResults) {
          print('   Iteration ${result.iteration}: ${result.parseTime}ms, hit rate: ${(result.hitRate * 100).toStringAsFixed(1)}%');
        }
      });
    });

    group('Stress Tests', () {
      test('should handle concurrent parsing operations', () async {
        const concurrentOperations = 5;
        final futures = <Future<Scenario>>[];

        // Start multiple parsing operations concurrently
        for (int i = 0; i < concurrentOperations; i++) {
          final xml = _generateSimulationXml('concurrent_$i', 10, 8);
          futures.add(parseSimDef(xml, tempDir));
        }

        final stopwatch = Stopwatch()..start();
        final results = await Future.wait(futures);
        stopwatch.stop();

        // All operations should complete successfully
        expect(results.length, equals(concurrentOperations));
        for (final scenario in results) {
          expect(scenario.locations.length, equals(10));
        }

        final totalTime = stopwatch.elapsedMilliseconds;
        final avgTimePerOperation = totalTime / concurrentOperations;

        print('📊 Concurrent Operations Stress Test:');
        print('   ${concurrentOperations} operations completed in ${totalTime}ms');
        print('   Average time per operation: ${avgTimePerOperation.toStringAsFixed(1)}ms');

        expect(totalTime, lessThan(30000)); // Should complete within 30 seconds
      });

      test('should handle memory pressure gracefully', () async {
        final scenarios = <Scenario>[];

        // Parse multiple large simulations to create memory pressure
        for (int i = 0; i < 10; i++) {
          final xml = _generateSimulationXml('pressure_$i', 20, 15);
          final scenario = await parseSimDef(xml, tempDir);
          scenarios.add(scenario);

          // Check memory usage periodically
          final memoryUsage = cache.statistics['memoryUsage'] as int;
          if (memoryUsage > 50 * 1024 * 1024) { // 50MB threshold
            cache.clearAll(); // Trigger cleanup
            print('   Memory cleanup triggered at ${(memoryUsage / 1024 / 1024).toStringAsFixed(1)}MB');
          }
        }

        expect(scenarios.length, equals(10));
        print('📊 Memory Pressure Test: Successfully parsed ${scenarios.length} large simulations');
      });

      test('should maintain performance under sustained load', () async {
        final parseTimes = <int>[];
        const sustainedOperations = 20;

        // Perform sustained parsing operations
        for (int i = 0; i < sustainedOperations; i++) {
          final xml = _generateSimulationXml('sustained_$i', 8, 12);
          
          final stopwatch = Stopwatch()..start();
          final scenario = await parseSimDef(xml, tempDir);
          stopwatch.stop();

          parseTimes.add(stopwatch.elapsedMilliseconds);
          expect(scenario.locations.length, equals(8));

          // Brief pause to simulate real-world usage
          await Future.delayed(const Duration(milliseconds: 10));
        }

        // Analyze performance consistency
        final avgTime = parseTimes.reduce((a, b) => a + b) / parseTimes.length;
        final maxTime = parseTimes.reduce((a, b) => a > b ? a : b);
        final minTime = parseTimes.reduce((a, b) => a < b ? a : b);
        final variance = maxTime - minTime;

        // Performance should remain consistent
        expect(variance / avgTime, lessThan(2.0)); // Variance should be < 200% of average

        print('📊 Sustained Load Test Results:');
        print('   ${sustainedOperations} operations: avg=${avgTime.toStringAsFixed(1)}ms, min=${minTime}ms, max=${maxTime}ms');
        print('   Performance variance: ${(variance / avgTime * 100).toStringAsFixed(1)}%');
      });
    });
  });

  // Helper methods and classes
  List<String> _generateTestCases(String category, List<(int, int)> configurations) {
    return configurations.map((config) {
      final (locations, elements) = config;
      return _generateSimulationXml('${category}_${locations}x$elements', locations, elements);
    }).toList();
  }

  Future<BenchmarkResults> _runBenchmarkSuite(String suiteName, List<String> testCases, {int iterations = 3}) async {
    final results = <ParseResult>[];

    print('🚀 Running $suiteName benchmark suite...');

    for (int i = 0; i < testCases.length; i++) {
      final xml = testCases[i];
      
      for (int iteration = 0; iteration < iterations; iteration++) {
        monitor.clear();
        
        final stopwatch = Stopwatch()..start();
        try {
          final scenario = await parseSimDef(xml, tempDir);
          stopwatch.stop();
          
          results.add(ParseResult(
            success: true,
            parseTime: stopwatch.elapsedMilliseconds,
            elementCount: _countElements(scenario),
          ));
        } catch (e) {
          stopwatch.stop();
          results.add(ParseResult(
            success: false,
            parseTime: stopwatch.elapsedMilliseconds,
            elementCount: 0,
            error: e.toString(),
          ));
        }
      }
    }

    return BenchmarkResults.fromResults(results);
  }

  String _generateSimulationXml(String id, int locationCount, int elementsPerLocation) {
    final random = Random();
    final buffer = StringBuffer();
    
    buffer.writeln('<sim id="$id" title="Benchmark Test $id" width="1024" height="768">');
    buffer.writeln('  <varTable>');
    buffer.writeln('    <variable id="CURRENT_SIM_STATE" type="string" default="state1" desc="the current sim state"/>');
    buffer.writeln('    <variable id="CURRENT_LOCATION" type="string" default="location1" desc="the current sim location"/>');
    buffer.writeln('  </varTable>');
    buffer.writeln('  <environ>');

    for (int i = 1; i <= locationCount; i++) {
      buffer.writeln('    <location id="location$i" name="Location $i" color="#${random.nextInt(0xFFFFFF).toRadixString(16).padLeft(6, '0')}" brightness="${(random.nextDouble() * 0.5 + 0.5).toStringAsFixed(2)}">');
      
      for (int j = 1; j <= elementsPerLocation; j++) {
        final elementType = ['CSText', 'CSShape', 'LocJumper', 'Timer'][random.nextInt(4)];
        final x = random.nextInt(800);
        final y = random.nextInt(600);
        
        buffer.writeln('      <element id="${elementType.toLowerCase()}_${i}_$j" type="$elementType" x="$x" y="$y" width="${50 + random.nextInt(100)}" height="${30 + random.nextInt(50)}">');
        
        switch (elementType) {
          case 'CSText':
            buffer.writeln('        <text>Sample text $i-$j</text>');
            break;
          case 'CSShape':
            buffer.writeln('        <shape>${['rectangle', 'circle', 'triangle'][random.nextInt(3)]}</shape>');
            buffer.writeln('        <color>#${random.nextInt(0xFFFFFF).toRadixString(16).padLeft(6, '0')}</color>');
            break;
          case 'LocJumper':
            final targetLoc = random.nextInt(locationCount) + 1;
            buffer.writeln('        <targetLocation>location$targetLoc</targetLocation>');
            buffer.writeln('        <text>Go to $targetLoc</text>');
            break;
          case 'Timer':
            buffer.writeln('        <duration>${1000 + random.nextInt(9000)}</duration>');
            buffer.writeln('        <trigger>start</trigger>');
            break;
        }
        
        buffer.writeln('      </element>');
      }
      
      buffer.writeln('    </location>');
    }

    buffer.writeln('  </environ>');
    buffer.writeln('  <environstates>');
    buffer.writeln('    <state id="state1" name="Test State"/>');
    buffer.writeln('  </environstates>');
    buffer.writeln('  <simFrames>');
    buffer.writeln('    <simFrame states="state1" locs="location1"/>');
    buffer.writeln('  </simFrames>');
    buffer.writeln('</sim>');

    return buffer.toString();
  }

  int _countElements(Scenario scenario) {
    return scenario.locations.fold(0, (total, location) {
      return total +
          location.sprites.length +
          location.images.length +
          location.shapes.length +
          location.texts.length +
          location.jumpers.length +
          location.labels.length +
          location.containers.length +
          location.people.length +
          location.timers.length +
          location.sounds.length;
    });
  }
}

// Helper classes for benchmark results
class ParseResult {
  final bool success;
  final int parseTime;
  final int elementCount;
  final String? error;

  ParseResult({
    required this.success,
    required this.parseTime,
    required this.elementCount,
    this.error,
  });
}

class BenchmarkResults {
  final double averageParseTime;
  final double successRate;
  final double averageElementsPerSecond;
  final int totalOperations;

  BenchmarkResults({
    required this.averageParseTime,
    required this.successRate,
    required this.averageElementsPerSecond,
    required this.totalOperations,
  });

  factory BenchmarkResults.fromResults(List<ParseResult> results) {
    final successful = results.where((r) => r.success).toList();
    final avgTime = successful.isEmpty ? 0.0 : successful.map((r) => r.parseTime).reduce((a, b) => a + b) / successful.length;
    final successRate = results.isEmpty ? 0.0 : successful.length / results.length;
    final totalElements = successful.fold(0, (sum, r) => sum + r.elementCount);
    final totalTime = successful.fold(0, (sum, r) => sum + r.parseTime);
    final elementsPerSecond = totalTime > 0 ? (totalElements / (totalTime / 1000)) : 0.0;

    return BenchmarkResults(
      averageParseTime: avgTime,
      successRate: successRate,
      averageElementsPerSecond: elementsPerSecond,
      totalOperations: results.length,
    );
  }
}

class MemoryBenchmarkResult {
  final int elementCount;
  final int memoryUsed;
  final int parseTime;
  final double memoryPerElement;

  MemoryBenchmarkResult({
    required this.elementCount,
    required this.memoryUsed,
    required this.parseTime,
    required this.memoryPerElement,
  });
}

class CacheBenchmarkResult {
  final int iteration;
  final int parseTime;
  final double hitRate;
  final int memoryUsage;

  CacheBenchmarkResult({
    required this.iteration,
    required this.parseTime,
    required this.hitRate,
    required this.memoryUsage,
  });
}
