import 'package:flutter_test/flutter_test.dart';
import 'package:simsushare_player/utils/parsers.dart';
import 'package:simsushare_player/utils/element_parsers/element_parser_factory.dart';
import 'package:simsushare_player/utils/performance/performance_monitor.dart';
import 'package:simsushare_player/utils/performance/asset_cache.dart';
import 'package:simsushare_player/models/Simulation.dart';
import 'package:simsushare_player/models/SimObjects.dart';
import 'dart:io';

void main() {
  group('Complete Parsing Workflow Integration Tests', () {
    late Directory tempDir;
    late PerformanceMonitor monitor;
    late AssetCache cache;

    setUpAll(() {
      ElementParserFactory.initialize();
      monitor = PerformanceMonitor();
      cache = AssetCache();
    });

    setUp(() {
      tempDir = Directory.systemTemp.createTempSync('parser_integration_test');
      monitor.clear();
      cache.clearAll();
    });

    tearDown(() {
      tempDir.deleteSync(recursive: true);
    });

    group('End-to-End Parsing Tests', () {
      test('should parse complete simulation with all element types', () async {
        // Create a comprehensive test XML
        const testXml = '''
        <sim id="integration_test" title="Integration Test Simulation" width="1024" height="768">
          <varTable>
            <variable id="CURRENT_SIM_STATE" type="string" default="state1" desc="the current sim state"/>
            <variable id="CURRENT_LOCATION" type="string" default="location1" desc="the current sim location"/>
          </varTable>
          <environ>
            <location id="location1" name="Main Location" color="#FFFFFF" brightness="1.0">
              <element id="text1" type="CSText" x="100" y="100" width="200" height="50">
                <text>Welcome to the simulation</text>
              </element>
              <element id="shape1" type="CSShape" x="300" y="200" width="100" height="100">
                <shape>rectangle</shape>
                <color>#FF0000</color>
              </element>
              <element id="jumper1" type="LocJumper" x="500" y="300" width="80" height="40">
                <targetLocation>location2</targetLocation>
                <text>Go to Location 2</text>
              </element>
              <element id="timer1" type="Timer" x="600" y="400" width="60" height="30">
                <duration>5000</duration>
                <trigger>start</trigger>
              </element>
              <element id="audio1" type="AudioClip" x="700" y="500" width="50" height="50">
                <audioFile>test_sound.mp3</audioFile>
                <autoPlay>false</autoPlay>
              </element>
            </location>
            <location id="location2" name="Second Location" color="#00FF00" brightness="0.8">
              <element id="text2" type="CSText" x="150" y="150" width="250" height="60">
                <text>This is the second location</text>
              </element>
              <element id="jumper2" type="LocJumper" x="400" y="350" width="80" height="40">
                <targetLocation>location1</targetLocation>
                <text>Back to Location 1</text>
              </element>
            </location>
          </environ>
          <environstates>
            <state id="state1" name="Initial State"/>
            <state id="state2" name="Secondary State"/>
          </environstates>
          <simFrames>
            <simFrame states="state1" locs="location1"/>
            <simFrame states="state2" locs="location2"/>
          </simFrames>
          <plugins>
            <corePlugins>
              <plugin id="core1" name="CorePlugin"/>
            </corePlugins>
          </plugins>
        </sim>
        ''';

        // Parse the simulation
        final scenario = await parseSimDef(testXml, tempDir);

        // Verify basic structure
        expect(scenario.id, equals('integration_test'));
        expect(scenario.name, equals('Integration Test Simulation'));
        expect(scenario.width, equals(1024.0));
        expect(scenario.height, equals(768.0));

        // Verify locations
        expect(scenario.locations.length, equals(2));
        expect(scenario.locations[0].id, equals('location1'));
        expect(scenario.locations[1].id, equals('location2'));

        // Verify states
        expect(scenario.states.length, equals(2));
        expect(scenario.states[0].id, equals('state1'));
        expect(scenario.states[1].id, equals('state2'));

        // Verify elements in first location
        final location1 = scenario.locations[0];
        expect(location1.texts.length, equals(1));
        expect(location1.shapes.length, equals(1));
        expect(location1.jumpers.length, equals(1));
        expect(location1.timers.length, equals(1));
        expect(location1.sounds.length, equals(1));

        // Verify elements in second location
        final location2 = scenario.locations[1];
        expect(location2.texts.length, equals(1));
        expect(location2.jumpers.length, equals(1));

        print('✅ Successfully parsed complete simulation with ${_countTotalElements(scenario)} elements');
      });

      test('should handle round-trip parsing (XML -> Scenario -> XML)', () async {
        // Create initial scenario
        final originalScenario = _createTestScenario();

        // Generate XML from scenario
        final generatedXml = generateSimDef(originalScenario);
        expect(generatedXml, isNotNull);
        expect(generatedXml, contains('<sim'));
        expect(generatedXml, contains('id="roundtrip_test"'));

        // Parse the generated XML back to scenario
        final parsedScenario = await parseSimDef(generatedXml, tempDir);

        // Verify round-trip consistency
        expect(parsedScenario.id, equals(originalScenario.id));
        expect(parsedScenario.name, equals(originalScenario.name));
        expect(parsedScenario.width, equals(originalScenario.width));
        expect(parsedScenario.height, equals(originalScenario.height));
        expect(parsedScenario.locations.length, equals(originalScenario.locations.length));
        expect(parsedScenario.states.length, equals(originalScenario.states.length));

        print('✅ Round-trip parsing successful');
      });

      test('should handle large simulation with performance monitoring', () async {
        // Create a large simulation for performance testing
        final largeXml = _generateLargeSimulationXml(50, 10); // 50 locations, 10 elements each

        final stopwatch = Stopwatch()..start();
        final scenario = await parseSimDef(largeXml, tempDir);
        stopwatch.stop();

        // Verify parsing completed
        expect(scenario.locations.length, equals(50));
        
        final totalElements = _countTotalElements(scenario);
        expect(totalElements, greaterThan(400)); // Should have 500+ elements

        // Check performance
        final parseTime = stopwatch.elapsedMilliseconds;
        expect(parseTime, lessThan(10000)); // Should complete within 10 seconds

        // Get performance statistics
        final stats = ElementParserFactory.getPerformanceStats();
        expect(stats['totalParsedElements'], greaterThan(0));

        print('✅ Parsed large simulation: ${scenario.locations.length} locations, $totalElements elements in ${parseTime}ms');
      });

      test('should handle error recovery in mixed valid/invalid content', () async {
        // Create XML with some invalid elements
        const mixedXml = '''
        <sim id="error_test" title="Error Recovery Test" width="800" height="600">
          <varTable>
            <variable id="CURRENT_SIM_STATE" type="string" default="state1" desc="the current sim state"/>
            <variable id="CURRENT_LOCATION" type="string" default="location1" desc="the current sim location"/>
          </varTable>
          <environ>
            <location id="location1" name="Test Location" color="#FFFFFF" brightness="1.0">
              <element id="valid_text" type="CSText" x="100" y="100" width="200" height="50">
                <text>Valid text element</text>
              </element>
              <element id="invalid_element" type="UnknownType" x="200" y="200">
                <invalidProperty>This should be ignored</invalidProperty>
              </element>
              <element id="valid_shape" type="CSShape" x="300" y="300" width="100" height="100">
                <shape>circle</shape>
                <color>#0000FF</color>
              </element>
            </location>
          </environ>
          <environstates>
            <state id="state1" name="Test State"/>
          </environstates>
          <simFrames>
            <simFrame states="state1" locs="location1"/>
          </simFrames>
        </sim>
        ''';

        // Should parse successfully despite invalid elements
        final scenario = await parseSimDef(mixedXml, tempDir);

        expect(scenario.id, equals('error_test'));
        expect(scenario.locations.length, equals(1));

        final location = scenario.locations[0];
        expect(location.texts.length, equals(1)); // Valid text element
        expect(location.shapes.length, equals(1)); // Valid shape element
        // Invalid element should be ignored

        print('✅ Error recovery successful: parsed valid elements, ignored invalid ones');
      });
    });

    group('Performance Integration Tests', () {
      test('should maintain consistent performance across multiple parses', () async {
        final parseTimes = <int>[];
        final testXml = _generateMediumSimulationXml();

        // Parse the same simulation multiple times
        for (int i = 0; i < 5; i++) {
          monitor.clear(); // Clear between runs for accurate timing
          
          final stopwatch = Stopwatch()..start();
          final scenario = await parseSimDef(testXml, tempDir);
          stopwatch.stop();

          parseTimes.add(stopwatch.elapsedMilliseconds);
          expect(scenario.locations.length, greaterThan(0));
        }

        // Check performance consistency
        final avgTime = parseTimes.reduce((a, b) => a + b) / parseTimes.length;
        final maxTime = parseTimes.reduce((a, b) => a > b ? a : b);
        final minTime = parseTimes.reduce((a, b) => a < b ? a : b);

        // Performance should be consistent (max shouldn't be more than 2x min)
        expect(maxTime / minTime, lessThan(2.0));

        print('✅ Performance consistency: avg=${avgTime.toStringAsFixed(1)}ms, min=${minTime}ms, max=${maxTime}ms');
      });

      test('should show performance improvement with caching', () async {
        final testXml = _generateMediumSimulationXml();

        // First parse (cold cache)
        cache.clearAll();
        final stopwatch1 = Stopwatch()..start();
        await parseSimDef(testXml, tempDir);
        stopwatch1.stop();
        final coldTime = stopwatch1.elapsedMilliseconds;

        // Second parse (warm cache)
        final stopwatch2 = Stopwatch()..start();
        await parseSimDef(testXml, tempDir);
        stopwatch2.stop();
        final warmTime = stopwatch2.elapsedMilliseconds;

        // Warm cache should be faster (or at least not significantly slower)
        expect(warmTime, lessThanOrEqualTo(coldTime * 1.5));

        final cacheStats = cache.statistics;
        print('✅ Cache performance: cold=${coldTime}ms, warm=${warmTime}ms, hit rate=${(cacheStats['hitRate'] * 100).toStringAsFixed(1)}%');
      });
    });

    group('Memory Management Tests', () {
      test('should handle memory cleanup properly', () async {
        final initialStats = cache.statistics;
        final initialMemory = initialStats['memoryUsage'] as int;

        // Parse multiple simulations
        for (int i = 0; i < 3; i++) {
          final xml = _generateMediumSimulationXml();
          await parseSimDef(xml, tempDir);
        }

        final afterStats = cache.statistics;
        final afterMemory = afterStats['memoryUsage'] as int;

        // Clear cache and verify cleanup
        cache.clearAll();
        final cleanStats = cache.statistics;
        final cleanMemory = cleanStats['memoryUsage'] as int;

        expect(cleanMemory, lessThan(afterMemory));
        print('✅ Memory management: initial=${initialMemory}B, after=${afterMemory}B, clean=${cleanMemory}B');
      });
    });
  });

  // Helper methods
  int _countTotalElements(Scenario scenario) {
    return scenario.locations.fold(0, (total, location) {
      return total +
          location.sprites.length +
          location.images.length +
          location.shapes.length +
          location.texts.length +
          location.jumpers.length +
          location.labels.length +
          location.containers.length +
          location.people.length +
          location.timers.length +
          location.sounds.length;
    });
  }

  Scenario _createTestScenario() {
    return Scenario(
      id: 'roundtrip_test',
      name: 'Round Trip Test',
      width: 800.0,
      height: 600.0,
      locations: [
        SimulationLocation(
          id: 'loc1',
          name: 'Test Location',
          color: '#FFFFFF',
          state: 'state1',
          sprites: [],
          imageBrightness: 1.0,
        ),
      ],
      currentState: 'state1',
      states: [
        SimulationState(id: 'state1', name: 'Test State'),
      ],
      initialLocationId: 'loc1',
      initialStateId: 'state1',
      navClusterX: 100.0,
      navClusterY: 100.0,
    );
  }

  String _generateLargeSimulationXml(int locationCount, int elementsPerLocation) {
    final buffer = StringBuffer();
    buffer.writeln('<sim id="large_test" title="Large Test Simulation" width="1024" height="768">');
    buffer.writeln('  <varTable>');
    buffer.writeln('    <variable id="CURRENT_SIM_STATE" type="string" default="state1" desc="the current sim state"/>');
    buffer.writeln('    <variable id="CURRENT_LOCATION" type="string" default="location1" desc="the current sim location"/>');
    buffer.writeln('  </varTable>');
    buffer.writeln('  <environ>');

    for (int i = 1; i <= locationCount; i++) {
      buffer.writeln('    <location id="location$i" name="Location $i" color="#FFFFFF" brightness="1.0">');
      
      for (int j = 1; j <= elementsPerLocation; j++) {
        final x = (j * 50) % 800;
        final y = (j * 30) % 600;
        buffer.writeln('      <element id="text_${i}_$j" type="CSText" x="$x" y="$y" width="100" height="30">');
        buffer.writeln('        <text>Text $i-$j</text>');
        buffer.writeln('      </element>');
      }
      
      buffer.writeln('    </location>');
    }

    buffer.writeln('  </environ>');
    buffer.writeln('  <environstates>');
    buffer.writeln('    <state id="state1" name="Test State"/>');
    buffer.writeln('  </environstates>');
    buffer.writeln('  <simFrames>');
    buffer.writeln('    <simFrame states="state1" locs="location1"/>');
    buffer.writeln('  </simFrames>');
    buffer.writeln('</sim>');

    return buffer.toString();
  }

  String _generateMediumSimulationXml() {
    return _generateLargeSimulationXml(5, 5); // 5 locations, 5 elements each
  }
}
