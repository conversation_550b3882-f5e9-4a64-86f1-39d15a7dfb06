name: simsushare_player
description: A new Flutter project.

# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
version: 1.0.0+1

environment:
  sdk: ">=3.0.0 <4.0.0"

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter


  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.2
  shared_preferences: ^2.0.12
  get: ^4.6.1
  multi_window: ^0.2.0
  flutter_colorpicker: ^1.0.3
  nanoid: ^1.0.0
  point_in_polygon: ^1.0.0
  path_provider: ^2.0.11
  flame: 1.9.1
  audioplayers: ^5.2.1
  dio: ^5.3.4
  jwt_decoder: ^2.0.1
  json_annotation: ^4.7.0
  get_storage: ^2.0.3
  args: ^2.3.1
  archive: ^3.4.6
  desktop_window: ^0.4.0
  io: ^1.0.3
  record: ^4.4.4
  file_picker: ^5.2.5
  file_selector: ^0.9.2+2
  freezed_annotation: ^2.2.0
  popover: ^0.2.8+2
  scaled_app: ^2.1.0
  window_manager: ^0.3.5
  flame_svg: ^1.7.1
  # flutter_adaptive_scaffold: ^0.1.7
  easy_debounce: ^2.0.3
  device_info_plus: ^9.1.2
  fuzzywuzzy: ^1.2.0
  string_validator: ^1.1.0


  collection: any
  path: any
  xml: any
  image: any
  http: any
dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^1.0.0
  build_runner: ^2.3.2
  json_serializable: ^6.5.3
  freezed: ^2.3.2
  flutter_launcher_icons: ^0.13.1

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

  image: any
flutter_launcher_icons:
  android: true
  ios: true
  web:
    generate: true
    background_color: "#ffffff"
  macos: 
    generate: true
  windows: 
    generate: true
    icon_size: 48 
  linux: true
  remove_alpha_ios: true
  image_path: "assets/logo.png"

# The following section is specific to Flutter.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/images/
    - assets/images/new_element/
    - assets/svg/
    - assets/svg/new_element/
    - assets/sprites/
    - assets/sprites/thumbs/
    - assets/people/
    - assets/labels/Corrosive/
    - assets/labels/Explosives/
    - assets/labels/FlamGas/
    - assets/labels/FlamLiq/
    - assets/labels/FlamSolid/
    - assets/labels/HazIDNum/
    - assets/labels/NFPA704/
    - assets/labels/NonFlamGas/
    - assets/labels/OxGas/
    - assets/labels/OxOrg/
    - assets/labels/Radioactive/
    - assets/labels/Specialty/
    - assets/labels/Substances/
    - assets/labels/ToxCorGas/
    - assets/labels/ToxCorSubst/
    - assets/containers/5GallonPail/
    - assets/containers/GeneralDutyMetal/
    - assets/containers/OpenHeadRingTop/
    - assets/containers/PolyDrum/
    - assets/people/FFVictim/
    - assets/people/RespLevelA/
    - assets/people/RespLevelB/
    - assets/people/VictimBoy/
    - assets/people/VictimGirl/
    - assets/people/VictimMan/
    - assets/people/VictimWoman/
    
  #   - images/a_dot_burr.jpeg
  #   - images/a_dot_ham.jpeg

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware.

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages
