package main

import (
	"flag"
	"io/ioutil"
	"log"
	"os"
	"strings"
)

func main() {

	err := os.RemoveAll("./filtered")
	if err != nil {
		log.Fatalln(err)
	}

	var resolution string

	flag.StringVar(&resolution, "res", "low", "Defines the resolution to filter files with")
	flag.Parse()

	if resolution != "low" && resolution != "medium" && resolution != "high" {
		log.Fatalln("Invalid resolution: ", resolution)
	}

	// 438 represents binary to int of 110110110 which is rw_rw_rw_
	os.Mkdir("./filtered", os.ModePerm)
	pluginFiles, err := ioutil.ReadDir("./plugins")

	if err != nil {
		log.Fatalln(err)
	}

	for _, folder := range pluginFiles {
		if !folder.IsDir() {
			continue
		}
		pluginFolder, err := ioutil.ReadDir("./plugins/" + folder.Name())
		if err != nil {
			log.Fatalln(err)
		}
		for _, pluginFile := range pluginFolder {
			index := strings.Index(pluginFile.Name(), "-"+resolution)
			if index == -1 {
				continue
			}
			file, err := ioutil.ReadFile("./plugins/" + folder.Name() + "/" + pluginFile.Name())
			if err != nil {
				log.Fatalln(err)
			}
			err = ioutil.WriteFile("./filtered/"+folder.Name()+"-"+pluginFile.Name(), file, os.ModePerm)
			if err != nil {
				log.Fatalln(err)
			}
		}

	}

}
