import fs from 'fs';
import sharp from 'sharp';
import yargs from 'yargs/yargs';
import jimp from 'jimp';
const { hideBin } = require('yargs/helpers')


const argv = yargs(hideBin(process.argv)).argv;

const file = argv["file"] as unknown as string;
if(!file) {
	console.log("No file specified");
	process.exit(1);
}

const lookAhead = parseInt(argv["lookAhead"] as unknown as string) || 10;
const yBias = parseInt(argv["yBias"] as unknown as string) || 0;
const forceHorizontalAlign = argv["forceHorizontalAlign"] as unknown as string || undefined;

if(forceHorizontalAlign && !['left', 'right'].includes(forceHorizontalAlign)) {
	throw Error("forceHorizontalAlign must be either 'left' or 'right'");
}

// Find the bottom-left most non-transparent pixel
// This is used as the pivot point where all frames will be aligned to since the top frames change drastically
function getLowestPoint(image: Awaited<ReturnType<typeof jimp.read>>): { x: number, y: number } {
	for (let y = image.getHeight() - yBias; y >= 0 ; y--) {
		for (let x = 0; x < image.getWidth() - lookAhead; x++) {
			const color = image.getPixelColor(x, y);
			if (color !== 0) {
				let valid = true;
				for(let x2 = x + 1; x2 < x + lookAhead; x2++) {
					const color2 = image.getPixelColor(x2, y);
					if (color2 === 0) {
						valid = false;
						break;
					}
				}
				if (valid) {
				}
				return { x, y };
			}
		}
	}
	throw new Error("No non-transparent pixels found");
}

const json = JSON.parse(fs.readFileSync(file + '.json', 'utf8'));

const imageFile = fs.readFileSync(file + '.png');
const jimpImage = await jimp.read(imageFile);

let largestWidth = 0;
let largestHeight = 0;

const jsonFrames = Object.keys(json.frames);

// Read all frames and get the largest width and height
jsonFrames.forEach((key: string) => {
	console.log(key);
	const frame = json.frames[key].frame;
	if (frame.w > largestWidth) {
		largestWidth = frame.w;
	}
	if (frame.h > largestHeight) {
		largestHeight = frame.h;
	}
});

// Apply the largest width and height to all frames and return their buffers
let frameBuffers = await Promise.all(jsonFrames.map(async (frameKey, index) => {
	// const meta = await frame.metadata();
	// console.log("Frame Size:", meta.width, "x", meta.height)
	const frame = json.frames[frameKey].frame;
	return jimpImage.clone().crop(frame.x, frame.y, frame.w, frame.h)
	// return jimpImage.clone().crop(largestWidth * index, 0, largestWidth, largestHeight)
	.contain(largestWidth, largestHeight, (forceHorizontalAlign? (forceHorizontalAlign === 'left'? jimp.HORIZONTAL_ALIGN_LEFT : jimp.HORIZONTAL_ALIGN_RIGHT) : jimp.HORIZONTAL_ALIGN_CENTER) | jimp.VERTICAL_ALIGN_BOTTOM /* jimp.HORIZONTAL_ALIGN_CENTER | jimp.VERTICAL_ALIGN_MIDDLE */)
	.getBufferAsync(jimp.MIME_PNG);
	/* return sharpInstance
		.extend({
			top: Math.floor((largestHeight - json.frames[jsonFrames[index]].frame.h)),
			left: Math.floor((largestWidth - json.frames[jsonFrames[index]].frame.w)),
			// right: Math.floor((largestWidth - json.frames[jsonFrames[index]].frame.w) / 2),
			background: { r: 0, g: 0, b: 0, alpha: 0 },
		})
		// .trim()
		.resize({
			width: largestWidth,
			height: largestHeight,
			fit: 'cover',
			background: { r: 0, g: 0, b: 0, alpha: 0 },
			withoutEnlargement: true,
			withoutReduction: true,
			// position: 'center' // default
		})
		.toBuffer(); */
}));

if(!forceHorizontalAlign) {
	// Get the lowest point of each frame
	const lowestPoints = await Promise.all(frameBuffers.map(async (buf) => getLowestPoint(await jimp.read(buf))));
	
	const largestXInLowestPoints = Math.max(...lowestPoints.map(p => p.x));
	const smallestXInLowestPoints = Math.min(...lowestPoints.map(p => p.x));
	
	console.log("Lowest points:", lowestPoints, "Largest X:", largestXInLowestPoints, "Smallest X:", smallestXInLowestPoints);
	const largestDiff = largestXInLowestPoints - smallestXInLowestPoints;
	
	console.log("Largest width: " + largestWidth);
	console.log("Largest height: " + largestHeight);
	console.log("Number of frames: " + jsonFrames.length);
	
	// Add the new padding to the left and right of each frame
	// Left is the most important where it will match the lowest point
	// Right is whatever remains from the padding
	frameBuffers = await Promise.all(frameBuffers.map(async (buf, index) => {
		let left = largestXInLowestPoints - lowestPoints[index].x;
		let right = largestDiff - left;
		return sharp(buf).extend({
			left, right,
			background: { r: 0, g: 0, b: 0, alpha: 0 }
		}).toBuffer();
	}));
	
	// Add the largest diff to the largest width to compensate for the updated padding
	largestWidth += largestDiff;
}


// create an image with the width of the combination of all frames
// all frames too the same height and width
const newImage = sharp({
	create: {
		width: largestWidth * jsonFrames.length,
		height: largestHeight,
		channels: 4,
		background: { r: 0, g: 0, b: 0, alpha: 0 }
	}
}).composite(await Promise.all(frameBuffers.map(async (buf, index) => ({
	input: buf,
	left: index * largestWidth,
	top: 0
} as sharp.OverlayOptions))))
.png();

jsonFrames.forEach((key: string, index) => {
	json.frames[key].frame.x = index * largestWidth;
	json.frames[key].frame.y = 0;
	json.frames[key].frame.w = largestWidth;
	json.frames[key].frame.h = largestHeight;
});

// Update the meta size with the new largest width
json.meta.size.w = largestWidth * jsonFrames.length;

console.log("Updated largest width:", largestWidth);

await newImage.toFile(file + '-fixed.png');
fs.writeFileSync(file + '-fixed.json', JSON.stringify(json, null, 2));