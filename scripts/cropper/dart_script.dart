import 'dart:io';
import 'dart:convert';
import 'package:image/image.dart';

void main(List<String> args) {
  if (args.isEmpty) print('Please provide the filename');

  Directory('scripts/cropper/output/').createSync();

  for (var filename in args) {
    final jsonPath = 'assets/people/$filename-frames.json';
    final imagePath = 'assets/people/$filename-frames.png';

    // Read JSON
    final file = File(jsonPath).readAsStringSync();
    final Map<String, dynamic> json = jsonDecode(file);

    // Read source image
    final image = decodeImage(File(imagePath).readAsBytesSync());

    // Read frames from JSON
    var frames = (json['frames'] as Map<String, dynamic>);

    List<String> keys = frames.keys.toList();

    // Sorting keys
    keys.sort((a, b) =>
        int.parse(a.split('/')[1]).compareTo(int.parse(b.split('/')[1])));

    //Synchronously creates the directory if it doesn't exist
    Directory('scripts/cropper/output/$filename').createSync();

    // Crop each frame and save to output folder
    for (var key in keys) {
      var frame = frames[key]['frame'];

      // Consider rotation
      int height = frame['h'], width = frame['w'];
      if (frames[key]['rotated']) {
        height = frame['w'];
        width = frame['h'];
      }

      var croppedImage = copyCrop(
        image!,
        x: frame['x'],
        y: frame['y'],
        width: width,
        height: height,
      );

      // If rotated, also rotate the image 90 degrees to right
      if (frames[key]['rotated']) {
        croppedImage = copyRotate(croppedImage, angle: -90);
      }

      File('scripts/cropper/output/$filename/${key.split('/')[1]}.png')
          .writeAsBytesSync(encodePng(croppedImage));
    }
  }
}
