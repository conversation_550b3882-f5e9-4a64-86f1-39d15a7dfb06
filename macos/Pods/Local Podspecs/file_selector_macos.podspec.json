{"name": "file_selector_macos", "version": "0.0.1", "summary": "macOS implementation of file_selector.", "description": "Displays native macOS open and save panels.", "license": {"type": "BSD", "file": "../LICENSE"}, "homepage": "https://github.com/flutter/packages/tree/main/packages/file_selector", "authors": {"Flutter Dev Team": "<EMAIL>"}, "source": {"http": "https://github.com/flutter/packages/tree/main/packages/file_selector/file_selector_macos"}, "source_files": "Classes/**/*", "dependencies": {"FlutterMacOS": []}, "platforms": {"osx": "10.14"}, "pod_target_xcconfig": {"DEFINES_MODULE": "YES"}, "swift_versions": "5.0", "swift_version": "5.0"}