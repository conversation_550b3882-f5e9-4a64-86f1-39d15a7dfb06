{"name": "path_provider_macos", "version": "0.0.1", "summary": "A macOS implementation of the path_provider plugin.", "description": "A macOS implementation of the Flutter plugin for getting commonly used locations on the filesystem.", "homepage": "https://github.com/flutter/plugins/tree/main/packages/path_provider/path_provider_macos", "license": {"type": "BSD", "file": "../LICENSE"}, "authors": {"Flutter Dev Team": "<EMAIL>"}, "source": {"http": "https://github.com/flutter/plugins/tree/main/packages/path_provider/path_provider_macos"}, "source_files": "Classes/**/*", "dependencies": {"FlutterMacOS": []}, "platforms": {"osx": "10.11"}, "swift_versions": "5.0", "swift_version": "5.0"}