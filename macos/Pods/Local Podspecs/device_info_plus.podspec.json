{"name": "device_info_plus", "version": "0.0.1", "summary": "No-op implementation of the macos device_info_plus to avoid build issues on macos", "description": "No-op implementation of the device_info_plus plugin to avoid build issues on macos.\nhttps://github.com/flutter/flutter/issues/46618", "homepage": "https://github.com/fluttercommunity/plus_plugins/tree/master/packages/device_info_plus", "license": {"file": "../LICENSE"}, "authors": {"Flutter Community": "<EMAIL>"}, "source": {"path": "."}, "source_files": "Classes/**/*", "public_header_files": "Classes/**/*.h", "dependencies": {"FlutterMacOS": []}, "platforms": {"osx": "10.11"}}