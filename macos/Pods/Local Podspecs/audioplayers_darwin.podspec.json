{"name": "audioplayers_darwin", "version": "0.0.1", "summary": "macOS implementation of audioplayers, a Flutter plugin to play multiple audio files simultaneously.", "description": "macOS implementation of audioplayers, a Flutter plugin to play multiple audio files simultaneously.", "homepage": "https://github.com/bluefireteam/audioplayers", "license": {"file": "../LICENSE"}, "authors": {"Blue Fire": "<EMAIL>"}, "source": {"path": "."}, "source_files": "Classes/**/*", "dependencies": {"FlutterMacOS": []}, "platforms": {"osx": "10.11"}, "pod_target_xcconfig": {"DEFINES_MODULE": "YES", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "i386"}, "swift_versions": "5.0", "swift_version": "5.0"}