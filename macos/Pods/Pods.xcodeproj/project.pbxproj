// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 54;
	objects = {

/* Begin PBXAggregateTarget section */
		CA272E8348BAB4CE0B0C804FB7B818C4 /* FlutterMacOS */ = {
			isa = PBXAggregateTarget;
			buildConfigurationList = 624022EE1C2EAB1C3CC38699E1368C5F /* Build configuration list for PBXAggregateTarget "FlutterMacOS" */;
			buildPhases = (
			);
			dependencies = (
			);
			name = FlutterMacOS;
		};
/* End PBXAggregateTarget section */

/* Begin PBXBuildFile section */
		0281C92FE93262465DB955E0E4BFD0AE /* Pods-Runner-dummy.m in Sources */ = {isa = PBXBuildFile; fileRef = 7B3C932BD54DBB963102A89E0F9E3948 /* Pods-Runner-dummy.m */; };
		04C7AAFF254DE945358CC972D1BF09A0 /* window_manager-umbrella.h in Headers */ = {isa = PBXBuildFile; fileRef = 099F395273F9F406732762E0F5192B79 /* window_manager-umbrella.h */; settings = {ATTRIBUTES = (Public, ); }; };
		07AA0A61A4704612086224E36DE379AB /* WindowManagerPlugin.swift in Sources */ = {isa = PBXBuildFile; fileRef = FD16CC156506F46993BF8DAACDD5E9A3 /* WindowManagerPlugin.swift */; };
		07EC06F281E68056E0771E653F8F4242 /* WindowManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 98C1D1061D564355C42133E215916FD4 /* WindowManager.swift */; };
		0E5A97906C5A1DEC4B90F12BDA982F10 /* messages.g.swift in Sources */ = {isa = PBXBuildFile; fileRef = DA9046750A8B1DE2D827ACC8AE529BBB /* messages.g.swift */; };
		15250E8EEE6867D1DBF830F451CD1CB9 /* multi_window_macos-umbrella.h in Headers */ = {isa = PBXBuildFile; fileRef = 98D3F86882658A8C966BFF17743A0195 /* multi_window_macos-umbrella.h */; settings = {ATTRIBUTES = (Public, ); }; };
		1558DEC4A8D4A0950F842306E8C95442 /* MultiWindowViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = EBA52E7390F4944C62BD77EFEE735447 /* MultiWindowViewController.swift */; };
		180913764CC4035B807CC92BE642A653 /* Cocoa.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = C1430F003D39D9906881A6AC724B7931 /* Cocoa.framework */; };
		1E15E4F7400C067A1C273655830C16D1 /* Cocoa.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = C1430F003D39D9906881A6AC724B7931 /* Cocoa.framework */; };
		21C0D72E936B08B479F9C6057ECFCFCB /* AudioplayersDarwinPlugin.m in Sources */ = {isa = PBXBuildFile; fileRef = ADDC190D957E27F003180EB428A9D7F8 /* AudioplayersDarwinPlugin.m */; };
		2B46654967FBBF3D52AC6740DCFCE9A9 /* ScreenRetrieverPlugin.swift in Sources */ = {isa = PBXBuildFile; fileRef = 05E64873068573B0653A4E30E29DFF2E /* ScreenRetrieverPlugin.swift */; };
		2D069586367A5CA2767A1358D3FC4D79 /* SystemUUID.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B15EF5B7FFF1CCE52D78340C20A5AEF /* SystemUUID.swift */; };
		2F9F6D19A76B0556FC7354FBFC859A9A /* CwlSysctl.swift in Sources */ = {isa = PBXBuildFile; fileRef = B71E8DDA70CA7881D9751F807CF75073 /* CwlSysctl.swift */; };
		3F1709D0A30CFA36FDF89F94654E26B7 /* path_provider_foundation-dummy.m in Sources */ = {isa = PBXBuildFile; fileRef = 5ADC014B1C650CAD0D67338F38B61AE2 /* path_provider_foundation-dummy.m */; };
		436F2E76D8BA662F7DD9A34D700F9E2B /* shared_preferences_foundation-umbrella.h in Headers */ = {isa = PBXBuildFile; fileRef = 900D19BE89D89C11E741B4E9A02BB348 /* shared_preferences_foundation-umbrella.h */; settings = {ATTRIBUTES = (Public, ); }; };
		44F3CE91ABC0E0015619072C038210BB /* device_info_plus-umbrella.h in Headers */ = {isa = PBXBuildFile; fileRef = 7F64498D32120CB3D197021132458D11 /* device_info_plus-umbrella.h */; settings = {ATTRIBUTES = (Public, ); }; };
		487A8FD168E17AD3EE3A9C414898FD61 /* Pods-Runner-umbrella.h in Headers */ = {isa = PBXBuildFile; fileRef = 96BF45FBE2BC9AD7B2D7E56D01B5EE46 /* Pods-Runner-umbrella.h */; settings = {ATTRIBUTES = (Public, ); }; };
		4C9A0BE44F743E65E995D2795AF758DF /* Cocoa.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = C1430F003D39D9906881A6AC724B7931 /* Cocoa.framework */; };
		50CB3C7D94A747F6FC8CF1D334CF1714 /* FileSelectorPlugin.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9BF5C17691D3F5395125C7607A396F00 /* FileSelectorPlugin.swift */; };
		54BD6B8CBD748FE19B52A97A3D0B9204 /* Cocoa.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = C1430F003D39D9906881A6AC724B7931 /* Cocoa.framework */; };
		55C4043BEA65AFBDE9D722C6351DA4D6 /* DeviceInfoPlusMacosPlugin.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2748B403B6DA26EA6F01F95CE675BC8B /* DeviceInfoPlusMacosPlugin.swift */; };
		5FEE48A50E5066C58F1393213BCDEA77 /* PrivacyInfo.xcprivacy in Resources */ = {isa = PBXBuildFile; fileRef = F905C0527912C18B12F2DB53C10CEE78 /* PrivacyInfo.xcprivacy */; };
		6201B0DB8EB2F3AC96BAA072B91D2AF5 /* screen_retriever-umbrella.h in Headers */ = {isa = PBXBuildFile; fileRef = 4BB18C29B1C5BE9E6D63AC5392CAFDB9 /* screen_retriever-umbrella.h */; settings = {ATTRIBUTES = (Public, ); }; };
		68DD428154EE48C88AA2567DD5313830 /* path_provider_foundation-path_provider_foundation_privacy in Resources */ = {isa = PBXBuildFile; fileRef = 3DBD4BADE27F8B91024E4B4B4DD75DB5 /* path_provider_foundation-path_provider_foundation_privacy */; };
		6C98832E0F8A1C617DCF536465D063AB /* device_info_plus-dummy.m in Sources */ = {isa = PBXBuildFile; fileRef = 0C062BEF8B6E2C80D461BA635C57594B /* device_info_plus-dummy.m */; };
		6F72C1DC1A82858FDA5F7CC268A4DDA0 /* EventChannelListener.swift in Sources */ = {isa = PBXBuildFile; fileRef = 11C8021AEA3CA9734F58D09956A37458 /* EventChannelListener.swift */; };
		76D0455F85D87B8A2ABA7D922465A29D /* shared_preferences_foundation-dummy.m in Sources */ = {isa = PBXBuildFile; fileRef = 7D2BBEEB10ADBC290420E0C4813DA537 /* shared_preferences_foundation-dummy.m */; };
		7AA11278E70264F1FC7B95DD86892F38 /* screen_retriever-dummy.m in Sources */ = {isa = PBXBuildFile; fileRef = 7F51189724BDD20304EBEA85AE74F065 /* screen_retriever-dummy.m */; };
		84998710A9A5DEFEA83B593899210332 /* AudioContext.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A281B9F310704B1B4E888DFE81EE356 /* AudioContext.swift */; };
		947E8648A0E8848CDB07DD50662028F4 /* Cocoa.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = C1430F003D39D9906881A6AC724B7931 /* Cocoa.framework */; };
		9CFB6D13187650953A9A4E4E58198B9A /* file_selector_macos-dummy.m in Sources */ = {isa = PBXBuildFile; fileRef = 18057D4E83D5EF72D9DF3F2979818FB4 /* file_selector_macos-dummy.m */; };
		9DFF5A94CF86ABB7EB5ADCFE1FC0EA5C /* Cocoa.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = C1430F003D39D9906881A6AC724B7931 /* Cocoa.framework */; };
		A200FC7F0F229C17DD41F380D318718D /* PrivacyInfo.xcprivacy in Resources */ = {isa = PBXBuildFile; fileRef = A6E8B1B754F529483A426C3B5D25FBC8 /* PrivacyInfo.xcprivacy */; };
		A55DC48879B2FA2DE8EDED0772F58CCF /* Cocoa.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = C1430F003D39D9906881A6AC724B7931 /* Cocoa.framework */; };
		A765223F898A5B657BD44B857DFB5720 /* SharedPreferencesPlugin.swift in Sources */ = {isa = PBXBuildFile; fileRef = F770A3C232123E03D36982C96131604B /* SharedPreferencesPlugin.swift */; };
		AA63379DD5CC1A84A9B632F04E8D6E0F /* file_selector_macos-umbrella.h in Headers */ = {isa = PBXBuildFile; fileRef = 579565AF9E3A4D7AFD236BA08B35E74B /* file_selector_macos-umbrella.h */; settings = {ATTRIBUTES = (Public, ); }; };
		B04DE9FDFC04DD63298DBEEA5FC6D6FF /* record_macos-dummy.m in Sources */ = {isa = PBXBuildFile; fileRef = 9D09B1856882684DEB8A9A13831C988E /* record_macos-dummy.m */; };
		B28BBDD61EFA65C9FA590D580723E50D /* messages.g.swift in Sources */ = {isa = PBXBuildFile; fileRef = 3B5CCEC14639E136D7DBDA1FBE088A97 /* messages.g.swift */; };
		BF8B65AD745297048479AB07CB9F41DA /* desktop_window-dummy.m in Sources */ = {isa = PBXBuildFile; fileRef = 232ED8A5BD1DA9EB400D454AB1C26762 /* desktop_window-dummy.m */; };
		BFC152EE7FD9BD306562CC276AD8928B /* Cocoa.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = C1430F003D39D9906881A6AC724B7931 /* Cocoa.framework */; };
		C1F97623C2ACA30FCB952166C985ABA4 /* MultiWindowMacosPlugin.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2697C03A5287A8DD59B095AC2DF3F465 /* MultiWindowMacosPlugin.swift */; };
		C5266D741824A56C87869B30AB99424D /* multi_window_macos-dummy.m in Sources */ = {isa = PBXBuildFile; fileRef = 0446800210223FAA81EF568A7D83587A /* multi_window_macos-dummy.m */; };
		C97AC3AA45083063A03A0EE22B7253F4 /* WrappedMediaPlayer.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8889357EAD319C7FECE5D3A5682E0954 /* WrappedMediaPlayer.swift */; };
		C9ADB42A30D9548ED1E3C80B1831D589 /* Cocoa.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = C1430F003D39D9906881A6AC724B7931 /* Cocoa.framework */; };
		CAC9560FAFDF50AA15C13FD1D56F3E5A /* messages.g.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2D68B5A84B528514A620F55EA5837E1A /* messages.g.swift */; };
		CD14C40F02F316B96868EA1465138606 /* audioplayers_darwin-umbrella.h in Headers */ = {isa = PBXBuildFile; fileRef = 22358A15DA6E84083C5C9259D7FAF997 /* audioplayers_darwin-umbrella.h */; settings = {ATTRIBUTES = (Public, ); }; };
		D6B0759EB0B7810B717B1A8AD92EB4AF /* shared_preferences_foundation-shared_preferences_foundation_privacy in Resources */ = {isa = PBXBuildFile; fileRef = 0652FCE3FC19056983AABE058B3CC45B /* shared_preferences_foundation-shared_preferences_foundation_privacy */; };
		E2DDA62B8FD58FA51749BF57DF30A03B /* RecordMacosPlugin.swift in Sources */ = {isa = PBXBuildFile; fileRef = A895C3AB5E749C4CDB8E6EDCFF046A87 /* RecordMacosPlugin.swift */; };
		E306B7310F09277D1947C4FE93C7F68A /* Cocoa.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = C1430F003D39D9906881A6AC724B7931 /* Cocoa.framework */; };
		E3A99C11729C575D4FE1C28284D18189 /* audioplayers_darwin-dummy.m in Sources */ = {isa = PBXBuildFile; fileRef = CA3475F33AEA8A47E131A55474EBD03F /* audioplayers_darwin-dummy.m */; };
		EAE262A111384256AB9CD27997EF79B4 /* Cocoa.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = C1430F003D39D9906881A6AC724B7931 /* Cocoa.framework */; };
		EC3CF062BD0AC3E2D62E10783896DEEC /* AudioplayersDarwinPlugin.h in Headers */ = {isa = PBXBuildFile; fileRef = F88B1676BDD69C562B383E65599BD6BA /* AudioplayersDarwinPlugin.h */; settings = {ATTRIBUTES = (Public, ); }; };
		ED7AD705694A02558DD2DDAE917DE2D3 /* PathProviderPlugin.swift in Sources */ = {isa = PBXBuildFile; fileRef = B07A42623F72ADF474F9C19D66EF7044 /* PathProviderPlugin.swift */; };
		EF1D0BC4394B8E1BEBA0D6A5D4367A6C /* DesktopWindowPlugin.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6F9F3D886E330E0E2F0CFFA52FD7A29E /* DesktopWindowPlugin.swift */; };
		EF7AA2401C9C3188DC138BEBDFAF2407 /* window_manager-dummy.m in Sources */ = {isa = PBXBuildFile; fileRef = CFBB45B322F073F3DD81E3C9E6BB085C /* window_manager-dummy.m */; };
		F16AD2E26F4A37D995D76EB108F80C11 /* SwiftAudioplayersDarwinPlugin.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6C91EE4CE90B2DFFBE89163CE3E272A8 /* SwiftAudioplayersDarwinPlugin.swift */; };
		F1E7C2244FFFE807BFC6FBBF5D1B744C /* path_provider_foundation-umbrella.h in Headers */ = {isa = PBXBuildFile; fileRef = 470F1431FDA74F5164BE9BF1625C89F1 /* path_provider_foundation-umbrella.h */; settings = {ATTRIBUTES = (Public, ); }; };
		FE5A3CE2DE3DE273D4B60A1FC8534BBD /* record_macos-umbrella.h in Headers */ = {isa = PBXBuildFile; fileRef = 9537738151BB4F5EEE6BE544084FEBF2 /* record_macos-umbrella.h */; settings = {ATTRIBUTES = (Public, ); }; };
		FFB5707A42B2419794141FFFC8348D40 /* Utils.swift in Sources */ = {isa = PBXBuildFile; fileRef = EEB40DFF8C7EFBEAB59D7AE711120B7F /* Utils.swift */; };
		FFD1C771E6E4789E2310DC74B6FD99CC /* desktop_window-umbrella.h in Headers */ = {isa = PBXBuildFile; fileRef = 53D4A14054B72270B254CAF362F1FC1C /* desktop_window-umbrella.h */; settings = {ATTRIBUTES = (Public, ); }; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		09AE3BE4B2E23AAC0796419A9D9BE1D5 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = CACE6618E7996464E38687E13F67D945;
			remoteInfo = "path_provider_foundation-path_provider_foundation_privacy";
		};
		0C51AD193DEF3AA16AB40D8CEDBBA832 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 73AC765CD3B58203E603C3188F337EAF;
			remoteInfo = window_manager;
		};
		0C6C520CAF1EA07E3E3C6E51C1A97889 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 29DBB609D5AD628C13D43247863E2C75;
			remoteInfo = screen_retriever;
		};
		21DB6736BD6B131F749F212C6329929D /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 77A51E6814DE611A5B73D37FF0402B9A;
			remoteInfo = device_info_plus;
		};
		36C263F1CD303F1845B4E2A19388AE3D /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = CA272E8348BAB4CE0B0C804FB7B818C4;
			remoteInfo = FlutterMacOS;
		};
		433DD455DA18ADDBD9EBE6D857A08F4C /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 6A8E7C76CC52400427E368907F578553;
			remoteInfo = record_macos;
		};
		4C29B2BDCFD54D3B89BC8FF02463728C /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 67C6B567311D7E870E2D1A0DA187FE3B;
			remoteInfo = audioplayers_darwin;
		};
		50C328735939B9AA2F2AECB0DEA34E7E /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 1B7F7E3C261A66F29E18C818E182267C;
			remoteInfo = desktop_window;
		};
		53A8B57A49FEDA5BC2EFCEA85663735B /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = AB5EE685B22D01885ADD930538E8DD3C;
			remoteInfo = shared_preferences_foundation;
		};
		55EB1DFFF22C33A2667A3AFFA8A327B7 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = CA272E8348BAB4CE0B0C804FB7B818C4;
			remoteInfo = FlutterMacOS;
		};
		5EC1EFC8B1941B4150CA37E8251284A3 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = CA272E8348BAB4CE0B0C804FB7B818C4;
			remoteInfo = FlutterMacOS;
		};
		62469C1046B243DF2B7C720102ADCAD3 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = CA272E8348BAB4CE0B0C804FB7B818C4;
			remoteInfo = FlutterMacOS;
		};
		8C516A59B5737BC669141D0A54AFCB71 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = CA272E8348BAB4CE0B0C804FB7B818C4;
			remoteInfo = FlutterMacOS;
		};
		97A56264649E68C4D952270B5F4261CC /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = CA272E8348BAB4CE0B0C804FB7B818C4;
			remoteInfo = FlutterMacOS;
		};
		9D0536614E2BEA1FEA433B7DED1D0094 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = B6AF8B7CEAF6321719ABBC7E770624DA;
			remoteInfo = "shared_preferences_foundation-shared_preferences_foundation_privacy";
		};
		A46E15F178298CFCAAE548A8D86D5EEE /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = CA272E8348BAB4CE0B0C804FB7B818C4;
			remoteInfo = FlutterMacOS;
		};
		A758CD2D993E0DBF11BF6EA58C0069FD /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = CA272E8348BAB4CE0B0C804FB7B818C4;
			remoteInfo = FlutterMacOS;
		};
		ABAC67FCC558BB85F6AE76183B234AB7 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 773D4A7C7FA9CAB9B055EBF0A53821FB;
			remoteInfo = file_selector_macos;
		};
		B952214B3B3D5AE43381B011CFB5D5AF /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = CA272E8348BAB4CE0B0C804FB7B818C4;
			remoteInfo = FlutterMacOS;
		};
		B9EF19AC0AB20139E95AFEBE7FD7C34C /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = CA272E8348BAB4CE0B0C804FB7B818C4;
			remoteInfo = FlutterMacOS;
		};
		E3FBBC177D15A0A6E1EB78D34E1A1CF2 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = F43E72AFDFDC89259492104B959EC9F2;
			remoteInfo = multi_window_macos;
		};
		F2F9996F5746113E70A0D1A9EFB0800D /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = CA272E8348BAB4CE0B0C804FB7B818C4;
			remoteInfo = FlutterMacOS;
		};
		FF1A4AA9A40CA19B371262701FD4F6C1 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 56F581DDCB0A032454E604885E17AE3C;
			remoteInfo = path_provider_foundation;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		01E45A15AB8BA1B99F116F1BE1C66960 /* audioplayers_darwin */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; name = audioplayers_darwin; path = audioplayers_darwin.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		02A736E1BDACFC0A6CAE810130768C55 /* device_info_plus */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; name = device_info_plus; path = device_info_plus.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		0446800210223FAA81EF568A7D83587A /* multi_window_macos-dummy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; path = "multi_window_macos-dummy.m"; sourceTree = "<group>"; };
		05E64873068573B0653A4E30E29DFF2E /* ScreenRetrieverPlugin.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ScreenRetrieverPlugin.swift; path = "../../../../../../../../../../../.pub-cache/hosted/pub.dev/screen_retriever-0.1.9/macos/Classes/ScreenRetrieverPlugin.swift"; sourceTree = "<group>"; };
		0652FCE3FC19056983AABE058B3CC45B /* shared_preferences_foundation-shared_preferences_foundation_privacy */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; name = "shared_preferences_foundation-shared_preferences_foundation_privacy"; path = shared_preferences_foundation_privacy.bundle; sourceTree = BUILT_PRODUCTS_DIR; };
		071B7B679E1835C6214C9BAB07F3A9E1 /* path_provider_foundation.modulemap */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.module; path = path_provider_foundation.modulemap; sourceTree = "<group>"; };
		07977345CBDC4475B7AAFC8164296A1E /* audioplayers_darwin.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = audioplayers_darwin.debug.xcconfig; sourceTree = "<group>"; };
		099F395273F9F406732762E0F5192B79 /* window_manager-umbrella.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "window_manager-umbrella.h"; sourceTree = "<group>"; };
		0BB41BB733686681BDF933A45AADFE5B /* screen_retriever */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; name = screen_retriever; path = screen_retriever.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		0C062BEF8B6E2C80D461BA635C57594B /* device_info_plus-dummy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; path = "device_info_plus-dummy.m"; sourceTree = "<group>"; };
		0C1592B61989FC5CCE59976846E87C55 /* path_provider_foundation-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "path_provider_foundation-Info.plist"; sourceTree = "<group>"; };
		0E48C40BE5A9502B93C3EBC3544DE5D8 /* screen_retriever.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = screen_retriever.debug.xcconfig; sourceTree = "<group>"; };
		11C8021AEA3CA9734F58D09956A37458 /* EventChannelListener.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = EventChannelListener.swift; path = "../../../../../../../../../../../.pub-cache/hosted/pub.dev/multi_window_macos-0.2.0/macos/Classes/EventChannelListener.swift"; sourceTree = "<group>"; };
		12F01C42F31534B42E0FBE46BBF0DCF7 /* multi_window_macos.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = multi_window_macos.release.xcconfig; sourceTree = "<group>"; };
		15781C9BEA5DDC5734F1CEECE6EF7A14 /* LICENSE */ = {isa = PBXFileReference; includeInIndex = 1; name = LICENSE; path = "../../../../../../../../../../.pub-cache/hosted/pub.dev/audioplayers_darwin-5.0.2/LICENSE"; sourceTree = "<group>"; };
		17E1A675CF35A9437C04E6610A3A9CDD /* record_macos */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; name = record_macos; path = record_macos.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		18057D4E83D5EF72D9DF3F2979818FB4 /* file_selector_macos-dummy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; path = "file_selector_macos-dummy.m"; sourceTree = "<group>"; };
		1B4E220B78A22CE5B6CF0054A0765105 /* record_macos-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "record_macos-Info.plist"; sourceTree = "<group>"; };
		1C034ECF5A35345A15FC1BF31C7926AC /* screen_retriever.modulemap */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.module; path = screen_retriever.modulemap; sourceTree = "<group>"; };
		1DE543BB4BB414EEC87187798D0C8776 /* FlutterMacOS.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = FlutterMacOS.release.xcconfig; sourceTree = "<group>"; };
		220EE919BBBFB028E630F690EBE0B093 /* ResourceBundle-path_provider_foundation_privacy-path_provider_foundation-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "ResourceBundle-path_provider_foundation_privacy-path_provider_foundation-Info.plist"; sourceTree = "<group>"; };
		22358A15DA6E84083C5C9259D7FAF997 /* audioplayers_darwin-umbrella.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "audioplayers_darwin-umbrella.h"; sourceTree = "<group>"; };
		232ED8A5BD1DA9EB400D454AB1C26762 /* desktop_window-dummy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; path = "desktop_window-dummy.m"; sourceTree = "<group>"; };
		2697C03A5287A8DD59B095AC2DF3F465 /* MultiWindowMacosPlugin.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = MultiWindowMacosPlugin.swift; path = "../../../../../../../../../../../.pub-cache/hosted/pub.dev/multi_window_macos-0.2.0/macos/Classes/MultiWindowMacosPlugin.swift"; sourceTree = "<group>"; };
		2748B403B6DA26EA6F01F95CE675BC8B /* DeviceInfoPlusMacosPlugin.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = DeviceInfoPlusMacosPlugin.swift; path = "../../../../../../../../../../../.pub-cache/hosted/pub.dev/device_info_plus-9.1.2/macos/Classes/DeviceInfoPlusMacosPlugin.swift"; sourceTree = "<group>"; };
		2B15EF5B7FFF1CCE52D78340C20A5AEF /* SystemUUID.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = SystemUUID.swift; path = "../../../../../../../../../../../.pub-cache/hosted/pub.dev/device_info_plus-9.1.2/macos/Classes/SystemUUID.swift"; sourceTree = "<group>"; };
		2B623D7F560364D4EDD91065B2EF55C6 /* path_provider_foundation.podspec */ = {isa = PBXFileReference; explicitFileType = text.script.ruby; includeInIndex = 1; indentWidth = 2; lastKnownFileType = text; name = path_provider_foundation.podspec; path = "../../../../../../../../../../.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.0/darwin/path_provider_foundation.podspec"; sourceTree = "<group>"; tabWidth = 2; xcLanguageSpecificationIdentifier = xcode.lang.ruby; };
		2D68B5A84B528514A620F55EA5837E1A /* messages.g.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = messages.g.swift; path = "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.4.0/darwin/shared_preferences_foundation/Sources/shared_preferences_foundation/messages.g.swift"; sourceTree = "<group>"; };
		317C26B9A7CEDD34ADE8F37FAAB7AC20 /* Pods-Runner-frameworks.sh */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.script.sh; path = "Pods-Runner-frameworks.sh"; sourceTree = "<group>"; };
		33E180FFABEB4B86D1A1875B6D90D7D2 /* desktop_window */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; name = desktop_window; path = desktop_window.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		37BE4354B66FC7EF515058A7756A93DC /* window_manager.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = window_manager.release.xcconfig; sourceTree = "<group>"; };
		38C88AC31C727DD6F478C72F0FAABEB1 /* file_selector_macos-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "file_selector_macos-Info.plist"; sourceTree = "<group>"; };
		390F2751AA49E047A8C29DD2002A1E79 /* path_provider_foundation-prefix.pch */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "path_provider_foundation-prefix.pch"; sourceTree = "<group>"; };
		3B5CCEC14639E136D7DBDA1FBE088A97 /* messages.g.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = messages.g.swift; path = "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/file_selector_macos-0.9.4/macos/file_selector_macos/Sources/file_selector_macos/messages.g.swift"; sourceTree = "<group>"; };
		3DBD4BADE27F8B91024E4B4B4DD75DB5 /* path_provider_foundation-path_provider_foundation_privacy */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; name = "path_provider_foundation-path_provider_foundation_privacy"; path = path_provider_foundation_privacy.bundle; sourceTree = BUILT_PRODUCTS_DIR; };
		3E0524801E48352212EC3A6BC724341C /* window_manager-prefix.pch */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "window_manager-prefix.pch"; sourceTree = "<group>"; };
		41470D1014189616A25E031AFE76A078 /* desktop_window-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "desktop_window-Info.plist"; sourceTree = "<group>"; };
		470F1431FDA74F5164BE9BF1625C89F1 /* path_provider_foundation-umbrella.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "path_provider_foundation-umbrella.h"; sourceTree = "<group>"; };
		48E233068DABD1AF47FDCEE1CDA3EF41 /* multi_window_macos-prefix.pch */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "multi_window_macos-prefix.pch"; sourceTree = "<group>"; };
		4BB18C29B1C5BE9E6D63AC5392CAFDB9 /* screen_retriever-umbrella.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "screen_retriever-umbrella.h"; sourceTree = "<group>"; };
		514886ECC5AEC8DF111B6B26AC1B74F5 /* file_selector_macos.podspec */ = {isa = PBXFileReference; explicitFileType = text.script.ruby; includeInIndex = 1; indentWidth = 2; lastKnownFileType = text; name = file_selector_macos.podspec; path = "../../../../../../../../../../.pub-cache/hosted/pub.dev/file_selector_macos-0.9.4/macos/file_selector_macos.podspec"; sourceTree = "<group>"; tabWidth = 2; xcLanguageSpecificationIdentifier = xcode.lang.ruby; };
		51825CD8F0558EFA53D9510F0E5BFA16 /* Pods-Runner-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "Pods-Runner-Info.plist"; sourceTree = "<group>"; };
		53D4A14054B72270B254CAF362F1FC1C /* desktop_window-umbrella.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "desktop_window-umbrella.h"; sourceTree = "<group>"; };
		5416DC5D27071DE6EB0828DCF50F50F4 /* path_provider_foundation.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = path_provider_foundation.release.xcconfig; sourceTree = "<group>"; };
		54D47ECD4C522277D3C282E7EF3EEB61 /* screen_retriever-prefix.pch */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "screen_retriever-prefix.pch"; sourceTree = "<group>"; };
		579565AF9E3A4D7AFD236BA08B35E74B /* file_selector_macos-umbrella.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "file_selector_macos-umbrella.h"; sourceTree = "<group>"; };
		5ADC014B1C650CAD0D67338F38B61AE2 /* path_provider_foundation-dummy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; path = "path_provider_foundation-dummy.m"; sourceTree = "<group>"; };
		5D6418645A55E19221C4D63090F983EF /* file_selector_macos.modulemap */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.module; path = file_selector_macos.modulemap; sourceTree = "<group>"; };
		5E1E24F476907831F791B258471AB2B8 /* LICENSE */ = {isa = PBXFileReference; includeInIndex = 1; name = LICENSE; path = "../../../../../../../../../../.pub-cache/hosted/pub.dev/screen_retriever-0.1.9/LICENSE"; sourceTree = "<group>"; };
		62DBAE607D9D5DCA48F598A68486AF26 /* multi_window_macos.modulemap */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.module; path = multi_window_macos.modulemap; sourceTree = "<group>"; };
		669E8F25E1897672BDB80B7EB784DA24 /* Pods-Runner */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; name = "Pods-Runner"; path = Pods_Runner.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		68422028797216EB5417CCDF8413F20F /* device_info_plus-prefix.pch */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "device_info_plus-prefix.pch"; sourceTree = "<group>"; };
		69DED2038A4BDE38EB814C93289706E1 /* device_info_plus.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = device_info_plus.debug.xcconfig; sourceTree = "<group>"; };
		6A281B9F310704B1B4E888DFE81EE356 /* AudioContext.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = AudioContext.swift; path = "../../../../../../../../../../../.pub-cache/hosted/pub.dev/audioplayers_darwin-5.0.2/macos/Classes/AudioContext.swift"; sourceTree = "<group>"; };
		6BE630BD354A7FDF26BC4965B6DC9588 /* LICENSE */ = {isa = PBXFileReference; includeInIndex = 1; name = LICENSE; path = "../../../../../../../../../../.pub-cache/hosted/pub.dev/device_info_plus-9.1.2/LICENSE"; sourceTree = "<group>"; };
		6C1B124B0250E8E7D0189FF8BC16C2EC /* device_info_plus-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "device_info_plus-Info.plist"; sourceTree = "<group>"; };
		6C91EE4CE90B2DFFBE89163CE3E272A8 /* SwiftAudioplayersDarwinPlugin.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = SwiftAudioplayersDarwinPlugin.swift; path = "../../../../../../../../../../../.pub-cache/hosted/pub.dev/audioplayers_darwin-5.0.2/macos/Classes/SwiftAudioplayersDarwinPlugin.swift"; sourceTree = "<group>"; };
		6F9F3D886E330E0E2F0CFFA52FD7A29E /* DesktopWindowPlugin.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = DesktopWindowPlugin.swift; path = "../../../../../../../../../../../.pub-cache/hosted/pub.dev/desktop_window-0.4.0/macos/Classes/DesktopWindowPlugin.swift"; sourceTree = "<group>"; };
		71C50C2C23D78D0BB660AA1C64ACC544 /* shared_preferences_foundation.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = shared_preferences_foundation.release.xcconfig; sourceTree = "<group>"; };
		754152008D42951744086E40F70526FD /* LICENSE */ = {isa = PBXFileReference; includeInIndex = 1; name = LICENSE; path = "../../../../../../../../../../.pub-cache/hosted/pub.dev/desktop_window-0.4.0/LICENSE"; sourceTree = "<group>"; };
		75FE4E3CFDDDC8C8122C0498E2C4B973 /* LICENSE */ = {isa = PBXFileReference; includeInIndex = 1; name = LICENSE; path = "../../../../../../../../../../.pub-cache/hosted/pub.dev/file_selector_macos-0.9.4/LICENSE"; sourceTree = "<group>"; };
		77509BB5CFF2B75FEE184CFED91156CC /* ResourceBundle-shared_preferences_foundation_privacy-shared_preferences_foundation-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "ResourceBundle-shared_preferences_foundation_privacy-shared_preferences_foundation-Info.plist"; sourceTree = "<group>"; };
		77D4AD8AA0942E5957F60AB59F00B4CD /* desktop_window.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = desktop_window.debug.xcconfig; sourceTree = "<group>"; };
		78262A3FFB3865B43720930E7C504395 /* record_macos.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = record_macos.debug.xcconfig; sourceTree = "<group>"; };
		7A72BBC57029402D7B528E6875CDF5AC /* device_info_plus.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = device_info_plus.release.xcconfig; sourceTree = "<group>"; };
		7B3C932BD54DBB963102A89E0F9E3948 /* Pods-Runner-dummy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; path = "Pods-Runner-dummy.m"; sourceTree = "<group>"; };
		7B513B7B7C6A682534106DF17673EEF7 /* window_manager.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = window_manager.debug.xcconfig; sourceTree = "<group>"; };
		7D2BBEEB10ADBC290420E0C4813DA537 /* shared_preferences_foundation-dummy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; path = "shared_preferences_foundation-dummy.m"; sourceTree = "<group>"; };
		7D7913D4604BB32424A45DB4E2AF6F1C /* path_provider_foundation.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = path_provider_foundation.debug.xcconfig; sourceTree = "<group>"; };
		7F51189724BDD20304EBEA85AE74F065 /* screen_retriever-dummy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; path = "screen_retriever-dummy.m"; sourceTree = "<group>"; };
		7F64498D32120CB3D197021132458D11 /* device_info_plus-umbrella.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "device_info_plus-umbrella.h"; sourceTree = "<group>"; };
		82D2A1D9762A2EF59E1B94B98A350BC2 /* audioplayers_darwin-prefix.pch */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "audioplayers_darwin-prefix.pch"; sourceTree = "<group>"; };
		86A1C093F3EC3A8E6E003710A86BEF4C /* file_selector_macos-prefix.pch */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "file_selector_macos-prefix.pch"; sourceTree = "<group>"; };
		876C89302BDFBADC401ED344F366ED9A /* window_manager.podspec */ = {isa = PBXFileReference; explicitFileType = text.script.ruby; includeInIndex = 1; indentWidth = 2; lastKnownFileType = text; name = window_manager.podspec; path = "../../../../../../../../../../.pub-cache/hosted/pub.dev/window_manager-0.3.9/macos/window_manager.podspec"; sourceTree = "<group>"; tabWidth = 2; xcLanguageSpecificationIdentifier = xcode.lang.ruby; };
		8889357EAD319C7FECE5D3A5682E0954 /* WrappedMediaPlayer.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = WrappedMediaPlayer.swift; path = "../../../../../../../../../../../.pub-cache/hosted/pub.dev/audioplayers_darwin-5.0.2/macos/Classes/WrappedMediaPlayer.swift"; sourceTree = "<group>"; };
		88F01D12D5F48A8799CDFAC84150240E /* audioplayers_darwin.modulemap */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.module; path = audioplayers_darwin.modulemap; sourceTree = "<group>"; };
		8EEE5A7951E35DE6FB3063A1C94C12E3 /* screen_retriever-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "screen_retriever-Info.plist"; sourceTree = "<group>"; };
		900D19BE89D89C11E741B4E9A02BB348 /* shared_preferences_foundation-umbrella.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "shared_preferences_foundation-umbrella.h"; sourceTree = "<group>"; };
		90749C3D7297BC7214590E4DA5A0F45C /* shared_preferences_foundation.podspec */ = {isa = PBXFileReference; explicitFileType = text.script.ruby; includeInIndex = 1; indentWidth = 2; lastKnownFileType = text; name = shared_preferences_foundation.podspec; path = "../../../../../../../../../../.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.4.0/darwin/shared_preferences_foundation.podspec"; sourceTree = "<group>"; tabWidth = 2; xcLanguageSpecificationIdentifier = xcode.lang.ruby; };
		92E81C080F0D73FB37D105E1BF66779A /* device_info_plus.podspec */ = {isa = PBXFileReference; explicitFileType = text.script.ruby; includeInIndex = 1; indentWidth = 2; lastKnownFileType = text; name = device_info_plus.podspec; path = "../../../../../../../../../../.pub-cache/hosted/pub.dev/device_info_plus-9.1.2/macos/device_info_plus.podspec"; sourceTree = "<group>"; tabWidth = 2; xcLanguageSpecificationIdentifier = xcode.lang.ruby; };
		93C6A45C6E4792269BE9BE0073839BF0 /* shared_preferences_foundation */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; name = shared_preferences_foundation; path = shared_preferences_foundation.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		9442E0B6509A21AB1F020079FB386258 /* desktop_window-prefix.pch */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "desktop_window-prefix.pch"; sourceTree = "<group>"; };
		9537738151BB4F5EEE6BE544084FEBF2 /* record_macos-umbrella.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "record_macos-umbrella.h"; sourceTree = "<group>"; };
		957172A2B536A4AE63811CCA54285E6D /* record_macos.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = record_macos.release.xcconfig; sourceTree = "<group>"; };
		96BF45FBE2BC9AD7B2D7E56D01B5EE46 /* Pods-Runner-umbrella.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "Pods-Runner-umbrella.h"; sourceTree = "<group>"; };
		9836B7DA5E0908FAF5F5916F2CBCFE3F /* shared_preferences_foundation-prefix.pch */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "shared_preferences_foundation-prefix.pch"; sourceTree = "<group>"; };
		98C1D1061D564355C42133E215916FD4 /* WindowManager.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = WindowManager.swift; path = "../../../../../../../../../../../.pub-cache/hosted/pub.dev/window_manager-0.3.9/macos/Classes/WindowManager.swift"; sourceTree = "<group>"; };
		98D3F86882658A8C966BFF17743A0195 /* multi_window_macos-umbrella.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "multi_window_macos-umbrella.h"; sourceTree = "<group>"; };
		9BF5C17691D3F5395125C7607A396F00 /* FileSelectorPlugin.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = FileSelectorPlugin.swift; path = "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/file_selector_macos-0.9.4/macos/file_selector_macos/Sources/file_selector_macos/FileSelectorPlugin.swift"; sourceTree = "<group>"; };
		9C3BA25F0B50F862177A8514F44633A4 /* multi_window_macos-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "multi_window_macos-Info.plist"; sourceTree = "<group>"; };
		9D09B1856882684DEB8A9A13831C988E /* record_macos-dummy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; path = "record_macos-dummy.m"; sourceTree = "<group>"; };
		9D6D73E8112E35933D84EE7261653ECE /* file_selector_macos.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = file_selector_macos.release.xcconfig; sourceTree = "<group>"; };
		9D940727FF8FB9C785EB98E56350EF41 /* Podfile */ = {isa = PBXFileReference; explicitFileType = text.script.ruby; includeInIndex = 1; indentWidth = 2; lastKnownFileType = text; name = Podfile; path = ../Podfile; sourceTree = SOURCE_ROOT; tabWidth = 2; xcLanguageSpecificationIdentifier = xcode.lang.ruby; };
		9E3A7F6F393223DA683124B04FF37AE7 /* record_macos.modulemap */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.module; path = record_macos.modulemap; sourceTree = "<group>"; };
		9EAB1D8F3A5E264F8928448DBDE39776 /* device_info_plus.modulemap */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.module; path = device_info_plus.modulemap; sourceTree = "<group>"; };
		9F79F8269DC34AC56BB3D1D9652C0D86 /* Pods-Runner.profile.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = "Pods-Runner.profile.xcconfig"; sourceTree = "<group>"; };
		A285876C1B30409B225AA879FCA17996 /* record_macos-prefix.pch */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "record_macos-prefix.pch"; sourceTree = "<group>"; };
		A383D5873E138651B79B9C3A80B6DC5A /* LICENSE */ = {isa = PBXFileReference; includeInIndex = 1; name = LICENSE; path = "../../../../../../../../../../.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.4.0/LICENSE"; sourceTree = "<group>"; };
		A43BBD7EFC411EAF3D8831A7C43B2ECF /* shared_preferences_foundation.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = shared_preferences_foundation.debug.xcconfig; sourceTree = "<group>"; };
		A5A130E1EA72AF23825AEFF6DC2407FA /* LICENSE */ = {isa = PBXFileReference; includeInIndex = 1; name = LICENSE; path = "../../../../../../../../../../.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.0/LICENSE"; sourceTree = "<group>"; };
		A6E8B1B754F529483A426C3B5D25FBC8 /* PrivacyInfo.xcprivacy */ = {isa = PBXFileReference; includeInIndex = 1; name = PrivacyInfo.xcprivacy; path = "../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.4.0/darwin/shared_preferences_foundation/Sources/shared_preferences_foundation/Resources/PrivacyInfo.xcprivacy"; sourceTree = "<group>"; };
		A85BF03EE412FCA81FAA12493EC1761F /* multi_window_macos */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; name = multi_window_macos; path = multi_window_macos.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		A895C3AB5E749C4CDB8E6EDCFF046A87 /* RecordMacosPlugin.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = RecordMacosPlugin.swift; path = "../../../../../../../../../../../.pub-cache/hosted/pub.dev/record_macos-0.2.2/macos/Classes/RecordMacosPlugin.swift"; sourceTree = "<group>"; };
		ACE63FC19F524D7E8EE79E7FA92DADB9 /* FlutterMacOS.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = FlutterMacOS.debug.xcconfig; sourceTree = "<group>"; };
		ADDC190D957E27F003180EB428A9D7F8 /* AudioplayersDarwinPlugin.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = AudioplayersDarwinPlugin.m; path = "../../../../../../../../../../../.pub-cache/hosted/pub.dev/audioplayers_darwin-5.0.2/macos/Classes/AudioplayersDarwinPlugin.m"; sourceTree = "<group>"; };
		AE157A33FEF959A214796BFF348717F6 /* path_provider_foundation */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; name = path_provider_foundation; path = path_provider_foundation.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		B07A42623F72ADF474F9C19D66EF7044 /* PathProviderPlugin.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = PathProviderPlugin.swift; path = "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.0/darwin/path_provider_foundation/Sources/path_provider_foundation/PathProviderPlugin.swift"; sourceTree = "<group>"; };
		B2DD4551467702121BC01091C6578ADC /* audioplayers_darwin.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = audioplayers_darwin.release.xcconfig; sourceTree = "<group>"; };
		B46DBE94CC9F1DD99CD3C3F3B90255F9 /* LICENSE */ = {isa = PBXFileReference; includeInIndex = 1; name = LICENSE; path = "../../../../../../../../../../.pub-cache/hosted/pub.dev/record_macos-0.2.2/LICENSE"; sourceTree = "<group>"; };
		B6A32825C2C4A2B9B2B794FBDF8516AA /* LICENSE */ = {isa = PBXFileReference; includeInIndex = 1; name = LICENSE; path = "../../../../../../../../../../.pub-cache/hosted/pub.dev/window_manager-0.3.9/LICENSE"; sourceTree = "<group>"; };
		B71E8DDA70CA7881D9751F807CF75073 /* CwlSysctl.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = CwlSysctl.swift; path = "../../../../../../../../../../../.pub-cache/hosted/pub.dev/device_info_plus-9.1.2/macos/Classes/CwlSysctl.swift"; sourceTree = "<group>"; };
		BBDD4FF2AD8AEC7FF4B758D1777A6B5C /* audioplayers_darwin.podspec */ = {isa = PBXFileReference; explicitFileType = text.script.ruby; includeInIndex = 1; indentWidth = 2; lastKnownFileType = text; name = audioplayers_darwin.podspec; path = "../../../../../../../../../../.pub-cache/hosted/pub.dev/audioplayers_darwin-5.0.2/macos/audioplayers_darwin.podspec"; sourceTree = "<group>"; tabWidth = 2; xcLanguageSpecificationIdentifier = xcode.lang.ruby; };
		BF1C29643888D89DDDE345ECCD2D391E /* desktop_window.modulemap */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.module; path = desktop_window.modulemap; sourceTree = "<group>"; };
		BF8B0A102A8D44CBDDC2D2B91F2E3041 /* record_macos.podspec */ = {isa = PBXFileReference; explicitFileType = text.script.ruby; includeInIndex = 1; indentWidth = 2; lastKnownFileType = text; name = record_macos.podspec; path = "../../../../../../../../../../.pub-cache/hosted/pub.dev/record_macos-0.2.2/macos/record_macos.podspec"; sourceTree = "<group>"; tabWidth = 2; xcLanguageSpecificationIdentifier = xcode.lang.ruby; };
		C070C6DBD1C648BD1C4E449AA9B59360 /* screen_retriever.podspec */ = {isa = PBXFileReference; explicitFileType = text.script.ruby; includeInIndex = 1; indentWidth = 2; lastKnownFileType = text; name = screen_retriever.podspec; path = "../../../../../../../../../../.pub-cache/hosted/pub.dev/screen_retriever-0.1.9/macos/screen_retriever.podspec"; sourceTree = "<group>"; tabWidth = 2; xcLanguageSpecificationIdentifier = xcode.lang.ruby; };
		C1430F003D39D9906881A6AC724B7931 /* Cocoa.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Cocoa.framework; path = Platforms/MacOSX.platform/Developer/SDKs/MacOSX10.15.sdk/System/Library/Frameworks/Cocoa.framework; sourceTree = DEVELOPER_DIR; };
		C2A19B351EE3987102FF0AA4CD33F175 /* file_selector_macos */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; name = file_selector_macos; path = file_selector_macos.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		C3A575EDA494C0386C43258E5C187B6C /* multi_window_macos.podspec */ = {isa = PBXFileReference; explicitFileType = text.script.ruby; includeInIndex = 1; indentWidth = 2; lastKnownFileType = text; name = multi_window_macos.podspec; path = "../../../../../../../../../../.pub-cache/hosted/pub.dev/multi_window_macos-0.2.0/macos/multi_window_macos.podspec"; sourceTree = "<group>"; tabWidth = 2; xcLanguageSpecificationIdentifier = xcode.lang.ruby; };
		C6EA98402A94995D022D330B64B5203D /* Pods-Runner-acknowledgements.markdown */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text; path = "Pods-Runner-acknowledgements.markdown"; sourceTree = "<group>"; };
		C966997FAAB2D575C31BFE04396EF4D8 /* window_manager-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "window_manager-Info.plist"; sourceTree = "<group>"; };
		CA3475F33AEA8A47E131A55474EBD03F /* audioplayers_darwin-dummy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; path = "audioplayers_darwin-dummy.m"; sourceTree = "<group>"; };
		CC21172AC4A1445D9948B184BC91F4ED /* audioplayers_darwin-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "audioplayers_darwin-Info.plist"; sourceTree = "<group>"; };
		CDAECCF4B5E08124ED410F09FD5A5DF9 /* Pods-Runner.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = "Pods-Runner.debug.xcconfig"; sourceTree = "<group>"; };
		CFBB45B322F073F3DD81E3C9E6BB085C /* window_manager-dummy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; path = "window_manager-dummy.m"; sourceTree = "<group>"; };
		D1F3864E5F87B5F5D65322EE84E77D26 /* file_selector_macos.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = file_selector_macos.debug.xcconfig; sourceTree = "<group>"; };
		D2FA70CA298C392CB8332ADEEDD1CE85 /* Pods-Runner.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = "Pods-Runner.release.xcconfig"; sourceTree = "<group>"; };
		D623C4B9C375D2341F7B3E50B78B65AD /* desktop_window.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = desktop_window.release.xcconfig; sourceTree = "<group>"; };
		D6D9D5169EFCC8E787910762D9B12DFF /* desktop_window.podspec */ = {isa = PBXFileReference; explicitFileType = text.script.ruby; includeInIndex = 1; indentWidth = 2; lastKnownFileType = text; name = desktop_window.podspec; path = "../../../../../../../../../../.pub-cache/hosted/pub.dev/desktop_window-0.4.0/macos/desktop_window.podspec"; sourceTree = "<group>"; tabWidth = 2; xcLanguageSpecificationIdentifier = xcode.lang.ruby; };
		D8F24F1A2CBC21242093C70AB6C80192 /* shared_preferences_foundation.modulemap */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.module; path = shared_preferences_foundation.modulemap; sourceTree = "<group>"; };
		D9F2B4CB813B4BDC4D164C6E0868930A /* Pods-Runner-acknowledgements.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "Pods-Runner-acknowledgements.plist"; sourceTree = "<group>"; };
		DA9046750A8B1DE2D827ACC8AE529BBB /* messages.g.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = messages.g.swift; path = "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.0/darwin/path_provider_foundation/Sources/path_provider_foundation/messages.g.swift"; sourceTree = "<group>"; };
		EBA52E7390F4944C62BD77EFEE735447 /* MultiWindowViewController.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = MultiWindowViewController.swift; path = "../../../../../../../../../../../.pub-cache/hosted/pub.dev/multi_window_macos-0.2.0/macos/Classes/MultiWindowViewController.swift"; sourceTree = "<group>"; };
		EEB40DFF8C7EFBEAB59D7AE711120B7F /* Utils.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = Utils.swift; path = "../../../../../../../../../../../.pub-cache/hosted/pub.dev/audioplayers_darwin-5.0.2/macos/Classes/Utils.swift"; sourceTree = "<group>"; };
		F07EA2824A3B75CA3E588A6770C81381 /* multi_window_macos.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = multi_window_macos.debug.xcconfig; sourceTree = "<group>"; };
		F4335A1185E2FCF61CF4A844555B706F /* shared_preferences_foundation-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "shared_preferences_foundation-Info.plist"; sourceTree = "<group>"; };
		F49C32B3B8CF59AB437BFD7314674868 /* Pods-Runner.modulemap */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.module; path = "Pods-Runner.modulemap"; sourceTree = "<group>"; };
		F4E8BCBE7387081908245ED1F253AA5F /* window_manager */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; name = window_manager; path = window_manager.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		F5518DFA488F5986586AA99160D9E913 /* FlutterMacOS.podspec */ = {isa = PBXFileReference; explicitFileType = text.script.ruby; includeInIndex = 1; indentWidth = 2; lastKnownFileType = text; path = FlutterMacOS.podspec; sourceTree = "<group>"; tabWidth = 2; xcLanguageSpecificationIdentifier = xcode.lang.ruby; };
		F70532D3456A3A4C813353CD0919395E /* window_manager.modulemap */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.module; path = window_manager.modulemap; sourceTree = "<group>"; };
		F770A3C232123E03D36982C96131604B /* SharedPreferencesPlugin.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = SharedPreferencesPlugin.swift; path = "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.4.0/darwin/shared_preferences_foundation/Sources/shared_preferences_foundation/SharedPreferencesPlugin.swift"; sourceTree = "<group>"; };
		F78C7F12EF81AE51ADD9BA93D950D33A /* screen_retriever.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = screen_retriever.release.xcconfig; sourceTree = "<group>"; };
		F88B1676BDD69C562B383E65599BD6BA /* AudioplayersDarwinPlugin.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = AudioplayersDarwinPlugin.h; path = "../../../../../../../../../../../.pub-cache/hosted/pub.dev/audioplayers_darwin-5.0.2/macos/Classes/AudioplayersDarwinPlugin.h"; sourceTree = "<group>"; };
		F905C0527912C18B12F2DB53C10CEE78 /* PrivacyInfo.xcprivacy */ = {isa = PBXFileReference; includeInIndex = 1; name = PrivacyInfo.xcprivacy; path = "../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.0/darwin/path_provider_foundation/Sources/path_provider_foundation/Resources/PrivacyInfo.xcprivacy"; sourceTree = "<group>"; };
		FD16CC156506F46993BF8DAACDD5E9A3 /* WindowManagerPlugin.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = WindowManagerPlugin.swift; path = "../../../../../../../../../../../.pub-cache/hosted/pub.dev/window_manager-0.3.9/macos/Classes/WindowManagerPlugin.swift"; sourceTree = "<group>"; };
		FD7570CACBD82F313B1B1C13A577CA2A /* LICENSE */ = {isa = PBXFileReference; includeInIndex = 1; name = LICENSE; path = "../../../../../../../../../../.pub-cache/hosted/pub.dev/multi_window_macos-0.2.0/LICENSE"; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		05E3ECACAAB2AF4E5C2EBCF0165EBB59 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				E306B7310F09277D1947C4FE93C7F68A /* Cocoa.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		26812204F9E66782F63AE60743733A27 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				1E15E4F7400C067A1C273655830C16D1 /* Cocoa.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		4E682B7F1F7EF9F508BCE85BD9C32E21 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				54BD6B8CBD748FE19B52A97A3D0B9204 /* Cocoa.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		5BFC84C5491CD0C5896340DEBF6DB851 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		7C9CFA1F134286F648EEA7BF6DBEDD6B /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				4C9A0BE44F743E65E995D2795AF758DF /* Cocoa.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		886B3F98AA5EE7F294D069C559280652 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				EAE262A111384256AB9CD27997EF79B4 /* Cocoa.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		9D9122B70348509CB0FC326C15E26B42 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		9FDD05BEFA0C0AE3CF8D1ECC4564D074 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				BFC152EE7FD9BD306562CC276AD8928B /* Cocoa.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		B2BB0BAFAE7ED172744D217F61DF02D6 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				9DFF5A94CF86ABB7EB5ADCFE1FC0EA5C /* Cocoa.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		B7A2DF989374EB504BA80C5BD83A7176 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				180913764CC4035B807CC92BE642A653 /* Cocoa.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		ECBA0A38E77985CEFAF57EAFD4875E7E /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				C9ADB42A30D9548ED1E3C80B1831D589 /* Cocoa.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		EF0D34834B0FDCFFA3365D7C2714D862 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				A55DC48879B2FA2DE8EDED0772F58CCF /* Cocoa.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		EFF1033DA0068F15D19CFF5BF309DCB6 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				947E8648A0E8848CDB07DD50662028F4 /* Cocoa.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		015DBDED0353EEE88BCDDEE0921121B9 /* audioplayers_darwin */ = {
			isa = PBXGroup;
			children = (
				664058C54466F4EACF4E09A428994770 /* macos */,
			);
			name = audioplayers_darwin;
			path = audioplayers_darwin;
			sourceTree = "<group>";
		};
		01DBA58AD8A27CFFD4DE2E549B2F5F0E /* .. */ = {
			isa = PBXGroup;
			children = (
				DBC7D8F9F563D777F6AF4F9FC60E4430 /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		042C0C616582025A4CF89DD67C0DEB46 /* macos */ = {
			isa = PBXGroup;
			children = (
				57A24E67B22F0E6D7CD0F139C7F212A2 /* Flutter */,
			);
			name = macos;
			path = macos;
			sourceTree = "<group>";
		};
		05498B061942D0C25D9631E2509AE0E3 /* dart */ = {
			isa = PBXGroup;
			children = (
				70B903B58A9F047C471AE2F46AE30306 /* flutter */,
			);
			name = dart;
			path = dart;
			sourceTree = "<group>";
		};
		072DE1B1193682D628394BDF537711F7 /* .. */ = {
			isa = PBXGroup;
			children = (
				A05A64FFC2AE7E38344CBE730CBB6F77 /* .. */,
			);
			name = ..;
			path = "../../../../../../../../../../.pub-cache/hosted/pub.dev/desktop_window-0.4.0/macos";
			sourceTree = "<group>";
		};
		080F50E0B947B7CF1EE3593AA8BDC69A /* .. */ = {
			isa = PBXGroup;
			children = (
				C86FE0E65C5A8EBE427972185DEAE087 /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		08CF68A424D0A805B651A76783AD88ED /* Classes */ = {
			isa = PBXGroup;
			children = (
				B71E8DDA70CA7881D9751F807CF75073 /* CwlSysctl.swift */,
				2748B403B6DA26EA6F01F95CE675BC8B /* DeviceInfoPlusMacosPlugin.swift */,
				2B15EF5B7FFF1CCE52D78340C20A5AEF /* SystemUUID.swift */,
			);
			name = Classes;
			path = Classes;
			sourceTree = "<group>";
		};
		08F0ED94F19F167E2BF8739448BA0DE8 /* Flutter */ = {
			isa = PBXGroup;
			children = (
				337695CC92B1FF6069EFC6057DF99682 /* ephemeral */,
			);
			name = Flutter;
			path = Flutter;
			sourceTree = "<group>";
		};
		096B784CF82D1B52D7F469CAC59ED7FF /* .. */ = {
			isa = PBXGroup;
			children = (
				3F0E1716F734FF320BF511097B1E207C /* dart */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		0A4E92E55266BA735894153AE314FCC6 /* ephemeral */ = {
			isa = PBXGroup;
			children = (
				F6D562BADDEECAC2068FC9FAAF8B35BF /* .symlinks */,
			);
			name = ephemeral;
			path = ephemeral;
			sourceTree = "<group>";
		};
		0AD06668FF495D88AFAAA9F7D4360101 /* multi_window_macos */ = {
			isa = PBXGroup;
			children = (
				FAC355FE9D3D9FB817196C2533534041 /* macos */,
			);
			name = multi_window_macos;
			path = multi_window_macos;
			sourceTree = "<group>";
		};
		0C882EE755D2B34BB51F374CB58A2F99 /* Support Files */ = {
			isa = PBXGroup;
			children = (
				88F01D12D5F48A8799CDFAC84150240E /* audioplayers_darwin.modulemap */,
				CA3475F33AEA8A47E131A55474EBD03F /* audioplayers_darwin-dummy.m */,
				CC21172AC4A1445D9948B184BC91F4ED /* audioplayers_darwin-Info.plist */,
				82D2A1D9762A2EF59E1B94B98A350BC2 /* audioplayers_darwin-prefix.pch */,
				22358A15DA6E84083C5C9259D7FAF997 /* audioplayers_darwin-umbrella.h */,
				07977345CBDC4475B7AAFC8164296A1E /* audioplayers_darwin.debug.xcconfig */,
				B2DD4551467702121BC01091C6578ADC /* audioplayers_darwin.release.xcconfig */,
			);
			name = "Support Files";
			path = "../../../../../../Pods/Target Support Files/audioplayers_darwin";
			sourceTree = "<group>";
		};
		0CEC000F5E7EF5748DE215E7801099BA /* Support Files */ = {
			isa = PBXGroup;
			children = (
				77509BB5CFF2B75FEE184CFED91156CC /* ResourceBundle-shared_preferences_foundation_privacy-shared_preferences_foundation-Info.plist */,
				D8F24F1A2CBC21242093C70AB6C80192 /* shared_preferences_foundation.modulemap */,
				7D2BBEEB10ADBC290420E0C4813DA537 /* shared_preferences_foundation-dummy.m */,
				F4335A1185E2FCF61CF4A844555B706F /* shared_preferences_foundation-Info.plist */,
				9836B7DA5E0908FAF5F5916F2CBCFE3F /* shared_preferences_foundation-prefix.pch */,
				900D19BE89D89C11E741B4E9A02BB348 /* shared_preferences_foundation-umbrella.h */,
				A43BBD7EFC411EAF3D8831A7C43B2ECF /* shared_preferences_foundation.debug.xcconfig */,
				71C50C2C23D78D0BB660AA1C64ACC544 /* shared_preferences_foundation.release.xcconfig */,
			);
			name = "Support Files";
			path = "../../../../../../Pods/Target Support Files/shared_preferences_foundation";
			sourceTree = "<group>";
		};
		0E4FB55926F7D1291B207FA30BC89F4B /* ephemeral */ = {
			isa = PBXGroup;
			children = (
				9CB8377FBCDAE561DA0B0B964E688F5A /* .symlinks */,
			);
			name = ephemeral;
			path = ephemeral;
			sourceTree = "<group>";
		};
		0E58F6C3410E18A8BEC37B50F5B8D8D5 /* .. */ = {
			isa = PBXGroup;
			children = (
				996B1629984ED0E2CC5D364C703FE83F /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		0EFD4AB8909EA1F7356C476A78BB87B5 /* dart */ = {
			isa = PBXGroup;
			children = (
				4E153F87DA9C3C0AFA526783AFA37A9E /* flutter */,
			);
			name = dart;
			path = dart;
			sourceTree = "<group>";
		};
		0F154B22C0F20579B156C026E3CDBE5A /* record_macos */ = {
			isa = PBXGroup;
			children = (
				8678CF3DA675035DD430658B930C70A7 /* macos */,
			);
			name = record_macos;
			path = record_macos;
			sourceTree = "<group>";
		};
		105B28EC7CCB1718D42F66301F02986A /* .. */ = {
			isa = PBXGroup;
			children = (
				DA333AFF17D1DBE0362741418D4469A4 /* dart */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		1106F3137E565F1F6CB7F46E85AC7599 /* .. */ = {
			isa = PBXGroup;
			children = (
				0E58F6C3410E18A8BEC37B50F5B8D8D5 /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		132E8792125578352F9A56ACC57B844C /* Pod */ = {
			isa = PBXGroup;
			children = (
				5E1E24F476907831F791B258471AB2B8 /* LICENSE */,
				C070C6DBD1C648BD1C4E449AA9B59360 /* screen_retriever.podspec */,
			);
			name = Pod;
			sourceTree = "<group>";
		};
		139B2BB880BFBE803BBA47A14E6448FB /* shared_preferences_foundation */ = {
			isa = PBXGroup;
			children = (
				B9FFA040A75A095F7DD616D99ABDD849 /* darwin */,
			);
			name = shared_preferences_foundation;
			path = shared_preferences_foundation;
			sourceTree = "<group>";
		};
		14FA89703B449399CD0C45754EFC9E83 /* Flutter */ = {
			isa = PBXGroup;
			children = (
				2B687F890C6F9DCFCD990B02557B8DC7 /* ephemeral */,
			);
			name = Flutter;
			path = Flutter;
			sourceTree = "<group>";
		};
		1514581750F5A6EF522FF7EC09EB65CB /* .. */ = {
			isa = PBXGroup;
			children = (
				4CD4A9592E7816DAB4E23F7A4D28A4C0 /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		175E40B20893A4B8B8058E7299FACA6A /* darwin */ = {
			isa = PBXGroup;
			children = (
				74699668AF859EAFA873E814C86B540A /* path_provider_foundation */,
			);
			name = darwin;
			path = darwin;
			sourceTree = "<group>";
		};
		19BE7E48C5AE499A5ECB12DE34206378 /* .. */ = {
			isa = PBXGroup;
			children = (
				9E611814D5901E41404B944D8D7E534E /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		19FBD261747CC0EF9D04A29A0856276D /* dart */ = {
			isa = PBXGroup;
			children = (
				1DCFBD8543504E3FA334F43913BDEADE /* flutter */,
			);
			name = dart;
			path = dart;
			sourceTree = "<group>";
		};
		1A0FAA3B3E7E1A5374AA36AB4ABD9AE3 /* path_provider_foundation */ = {
			isa = PBXGroup;
			children = (
				DA9046750A8B1DE2D827ACC8AE529BBB /* messages.g.swift */,
				B07A42623F72ADF474F9C19D66EF7044 /* PathProviderPlugin.swift */,
			);
			name = path_provider_foundation;
			path = path_provider_foundation;
			sourceTree = "<group>";
		};
		1C4A580302B6307E3B2673593EB879A3 /* simsushare_player */ = {
			isa = PBXGroup;
			children = (
				B12C84E650C18D87D3C8DBC03DC2A0D1 /* macos */,
			);
			name = simsushare_player;
			path = simsushare_player;
			sourceTree = "<group>";
		};
		1CA556E755A1328AFEFBB1B3FC660830 /* ephemeral */ = {
			isa = PBXGroup;
			children = (
				D781FEE1AA5279455FD2A339EA3ECD71 /* .symlinks */,
			);
			name = ephemeral;
			path = ephemeral;
			sourceTree = "<group>";
		};
		1D140214D69DF9A3128086D78C7AD89E /* .. */ = {
			isa = PBXGroup;
			children = (
				1106F3137E565F1F6CB7F46E85AC7599 /* .. */,
			);
			name = ..;
			path = "../../../../../../../../../../.pub-cache/hosted/pub.dev/screen_retriever-0.1.9/macos";
			sourceTree = "<group>";
		};
		1DCFBD8543504E3FA334F43913BDEADE /* flutter */ = {
			isa = PBXGroup;
			children = (
				C5613D9CC9A7421167C714EEB02D4FBF /* simsushare_player */,
			);
			name = flutter;
			path = flutter;
			sourceTree = "<group>";
		};
		1E229C0ABFD91F2A3CBA789AF2767625 /* .. */ = {
			isa = PBXGroup;
			children = (
				3BAA089A87748F548253CEC3AC1CBE54 /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		1FC383E36BC63AD4C4A2209A6C9170B0 /* file_selector_macos */ = {
			isa = PBXGroup;
			children = (
				497DDFA68CE55FA3E0BA8587E6D6A2F9 /* Sources */,
			);
			name = file_selector_macos;
			path = file_selector_macos;
			sourceTree = "<group>";
		};
		232B6BDDE7F27DAEDF2F0D0EADCA82D8 /* .. */ = {
			isa = PBXGroup;
			children = (
				3D458DC60E981D9CFFF8434C0A19B2A0 /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		23E40870B135D795F3D3E290B635ABA3 /* Flutter */ = {
			isa = PBXGroup;
			children = (
				1CA556E755A1328AFEFBB1B3FC660830 /* ephemeral */,
			);
			name = Flutter;
			path = Flutter;
			sourceTree = "<group>";
		};
		246DB407D041A3467C62E98F40A27947 /* Pod */ = {
			isa = PBXGroup;
			children = (
				BBDD4FF2AD8AEC7FF4B758D1777A6B5C /* audioplayers_darwin.podspec */,
				15781C9BEA5DDC5734F1CEECE6EF7A14 /* LICENSE */,
			);
			name = Pod;
			sourceTree = "<group>";
		};
		24B583F56F2D6DB00D77AB63691F8B71 /* file_selector_macos */ = {
			isa = PBXGroup;
			children = (
				9BF5C17691D3F5395125C7607A396F00 /* FileSelectorPlugin.swift */,
				3B5CCEC14639E136D7DBDA1FBE088A97 /* messages.g.swift */,
			);
			name = file_selector_macos;
			path = file_selector_macos;
			sourceTree = "<group>";
		};
		258A4B82B97069ED66DA0C3094EA60CA /* simsushare_player */ = {
			isa = PBXGroup;
			children = (
				ACD7C7F87CD3AE492BF2FB172B9013D4 /* macos */,
			);
			name = simsushare_player;
			path = simsushare_player;
			sourceTree = "<group>";
		};
		267A4193C70E192371F6238CB9F4BA2F /* .. */ = {
			isa = PBXGroup;
			children = (
				30B344E86D04EF81E86F9CEC8CA38B94 /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		26DDFAC336BFD40F131D82EC95200B6E /* Flutter */ = {
			isa = PBXGroup;
			children = (
				31F2EAF4420985B59708075BB1DF2804 /* ephemeral */,
			);
			name = Flutter;
			path = Flutter;
			sourceTree = "<group>";
		};
		26ED9F70A33819531DB7F95A13627E63 /* ephemeral */ = {
			isa = PBXGroup;
			children = (
				A62CE63EE54F317B947E4A95DEA7412F /* .symlinks */,
			);
			name = ephemeral;
			path = ephemeral;
			sourceTree = "<group>";
		};
		2762B2917E9660554FDD1E1C758DA3E3 /* .symlinks */ = {
			isa = PBXGroup;
			children = (
				47247B79A82E8753C49DD13C1A28AFA1 /* plugins */,
			);
			name = .symlinks;
			path = .symlinks;
			sourceTree = "<group>";
		};
		278E636F425B33DF6550950FF97062F3 /* macos */ = {
			isa = PBXGroup;
			children = (
				1FC383E36BC63AD4C4A2209A6C9170B0 /* file_selector_macos */,
			);
			name = macos;
			path = macos;
			sourceTree = "<group>";
		};
		27EF107D2A366CB812AA866F86F11402 /* .. */ = {
			isa = PBXGroup;
			children = (
				949B9D22369F28B38FA08D8965F82001 /* dart */,
			);
			name = ..;
			path = .;
			sourceTree = "<group>";
		};
		2A04AB8A0B286910188172033FC2D00D /* Classes */ = {
			isa = PBXGroup;
			children = (
				A895C3AB5E749C4CDB8E6EDCFF046A87 /* RecordMacosPlugin.swift */,
			);
			name = Classes;
			path = Classes;
			sourceTree = "<group>";
		};
		2A7E17B19693EA3D69C09C56C8C72397 /* window_manager */ = {
			isa = PBXGroup;
			children = (
				47B55D1C43951E07D93E19B10F5AC925 /* macos */,
			);
			name = window_manager;
			path = window_manager;
			sourceTree = "<group>";
		};
		2AA4EA24A89BD92BA43B5213F6AA4785 /* Pod */ = {
			isa = PBXGroup;
			children = (
				B46DBE94CC9F1DD99CD3C3F3B90255F9 /* LICENSE */,
				BF8B0A102A8D44CBDDC2D2B91F2E3041 /* record_macos.podspec */,
			);
			name = Pod;
			sourceTree = "<group>";
		};
		2AFEC9D0B784697DB771796B71EDD2BC /* .. */ = {
			isa = PBXGroup;
			children = (
				0EFD4AB8909EA1F7356C476A78BB87B5 /* dart */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		2B687F890C6F9DCFCD990B02557B8DC7 /* ephemeral */ = {
			isa = PBXGroup;
			children = (
				E88F004D04A16175FC8E130E2F48FE44 /* .symlinks */,
			);
			name = ephemeral;
			path = ephemeral;
			sourceTree = "<group>";
		};
		2D1571815A518E4B151EAC8D9AEE8D09 /* simsushare_player */ = {
			isa = PBXGroup;
			children = (
				9F44459ED8849D368587811B2205017C /* macos */,
			);
			name = simsushare_player;
			path = simsushare_player;
			sourceTree = "<group>";
		};
		2E00EDF57BD02FADA9C07171FA3F7B64 /* .symlinks */ = {
			isa = PBXGroup;
			children = (
				EB6D13E243849B6AE6ECEFF48F7D79B4 /* plugins */,
			);
			name = .symlinks;
			path = .symlinks;
			sourceTree = "<group>";
		};
		30B344E86D04EF81E86F9CEC8CA38B94 /* .. */ = {
			isa = PBXGroup;
			children = (
				2AFEC9D0B784697DB771796B71EDD2BC /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		31F2EAF4420985B59708075BB1DF2804 /* ephemeral */ = {
			isa = PBXGroup;
			children = (
				2E00EDF57BD02FADA9C07171FA3F7B64 /* .symlinks */,
			);
			name = ephemeral;
			path = ephemeral;
			sourceTree = "<group>";
		};
		32216A80E7BC2F21A2B06CE0916EC23D /* plugins */ = {
			isa = PBXGroup;
			children = (
				7C42930271340F0585F77CD0DB42BCC2 /* file_selector_macos */,
			);
			name = plugins;
			path = plugins;
			sourceTree = "<group>";
		};
		32E2B41EBF7EE6430A1BA649A4379476 /* macos */ = {
			isa = PBXGroup;
			children = (
				A6624C93E72F2FF714A31C8A7B4A5C99 /* Flutter */,
			);
			name = macos;
			path = macos;
			sourceTree = "<group>";
		};
		337695CC92B1FF6069EFC6057DF99682 /* ephemeral */ = {
			isa = PBXGroup;
			children = (
				41D492F19422BEACD2D4D9CC596D907B /* .symlinks */,
			);
			name = ephemeral;
			path = ephemeral;
			sourceTree = "<group>";
		};
		341259B859701C593FB2AF9091ED1853 /* .. */ = {
			isa = PBXGroup;
			children = (
				96C9D7481EF80483ACED4B2F2DBEBA33 /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		36ED723DE9BFCC8F479A1E7191164717 /* macos */ = {
			isa = PBXGroup;
			children = (
				08F0ED94F19F167E2BF8739448BA0DE8 /* Flutter */,
			);
			name = macos;
			path = macos;
			sourceTree = "<group>";
		};
		37A8AE5E4EA9D57D8BA5DA871BC5CB71 /* Development Pods */ = {
			isa = PBXGroup;
			children = (
				BC4AA2FBC5DA17A4B810F130F8F3BBE4 /* audioplayers_darwin */,
				F621D1B39A8A2018A2F635198C6C58B6 /* desktop_window */,
				4943DDE3C839F7A8FBF87ECA118A19CC /* device_info_plus */,
				CF5164E84B37F3E78F2DA1D69D866681 /* file_selector_macos */,
				A4DF37423716C44F463FCB94CF948EF1 /* FlutterMacOS */,
				C024DF3957461D45B61FCF1994DEAD69 /* multi_window_macos */,
				69611981EE0BA8B26C0C24C563B9E408 /* path_provider_foundation */,
				80778616A9D041B00E5C7E7709D31E3D /* record_macos */,
				A56B9E47FE87B5AF20D60F29404F90BD /* screen_retriever */,
				BAA4BF1D54FDD3823885890027A06B85 /* shared_preferences_foundation */,
				85D9931C6F50130A9673A88ADB12ACD1 /* window_manager */,
			);
			name = "Development Pods";
			sourceTree = "<group>";
		};
		38091DF1D800CC7C8374A3E359D3F2C5 /* dart */ = {
			isa = PBXGroup;
			children = (
				748C144A987CCC25FADE3810F99F6B4E /* flutter */,
			);
			name = dart;
			path = dart;
			sourceTree = "<group>";
		};
		388C6DBA06F4B41A3EA4FCC8C2034038 /* dart */ = {
			isa = PBXGroup;
			children = (
				9F03699FA3A1C8000DC9044CEF49A813 /* flutter */,
			);
			name = dart;
			path = dart;
			sourceTree = "<group>";
		};
		39940FBFE9A7EAE34940010E219E89C8 /* .. */ = {
			isa = PBXGroup;
			children = (
				7A6111B8932E6972897F2F43E09E06DE /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		3BAA089A87748F548253CEC3AC1CBE54 /* .. */ = {
			isa = PBXGroup;
			children = (
				096B784CF82D1B52D7F469CAC59ED7FF /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		3D458DC60E981D9CFFF8434C0A19B2A0 /* .. */ = {
			isa = PBXGroup;
			children = (
				C9EDA6A836AD66AC74632322C05EBDD1 /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		3D62B2D1FC925F1135E925C045E86BB0 /* flutter */ = {
			isa = PBXGroup;
			children = (
				82D08A5AA0786EC67B74968DEB6514A2 /* simsushare_player */,
			);
			name = flutter;
			path = flutter;
			sourceTree = "<group>";
		};
		3DEA93E71CEE289964DD635A07217D8D /* plugins */ = {
			isa = PBXGroup;
			children = (
				139B2BB880BFBE803BBA47A14E6448FB /* shared_preferences_foundation */,
			);
			name = plugins;
			path = plugins;
			sourceTree = "<group>";
		};
		3F0E1716F734FF320BF511097B1E207C /* dart */ = {
			isa = PBXGroup;
			children = (
				6A3310207FC8F7510B0E9E73C6EFE89C /* flutter */,
			);
			name = dart;
			path = dart;
			sourceTree = "<group>";
		};
		3FED3B2E931929391DFB62716F4111AE /* .. */ = {
			isa = PBXGroup;
			children = (
				8365434E0238E4DBF34309806248EAB1 /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		41D492F19422BEACD2D4D9CC596D907B /* .symlinks */ = {
			isa = PBXGroup;
			children = (
				3DEA93E71CEE289964DD635A07217D8D /* plugins */,
			);
			name = .symlinks;
			path = .symlinks;
			sourceTree = "<group>";
		};
		42F91D30693787FD9ADFCC972197A79F /* plugins */ = {
			isa = PBXGroup;
			children = (
				015DBDED0353EEE88BCDDEE0921121B9 /* audioplayers_darwin */,
			);
			name = plugins;
			path = plugins;
			sourceTree = "<group>";
		};
		44BE1DAB426C61870577BAE4CAD5A5D6 /* darwin */ = {
			isa = PBXGroup;
			children = (
				8C458E78FB504FBBEA4574F27125C31C /* path_provider_foundation */,
			);
			name = darwin;
			path = darwin;
			sourceTree = "<group>";
		};
		459693837366EB151FF86EDA4BD65FCC /* Flutter */ = {
			isa = PBXGroup;
			children = (
				D9E7A92613B2E4684184220341A63168 /* ephemeral */,
			);
			name = Flutter;
			path = Flutter;
			sourceTree = "<group>";
		};
		47247B79A82E8753C49DD13C1A28AFA1 /* plugins */ = {
			isa = PBXGroup;
			children = (
				0AD06668FF495D88AFAAA9F7D4360101 /* multi_window_macos */,
			);
			name = plugins;
			path = plugins;
			sourceTree = "<group>";
		};
		47B55D1C43951E07D93E19B10F5AC925 /* macos */ = {
			isa = PBXGroup;
			children = (
				FD9ADC2E92EAEEF8D5D6725CD44E3C7C /* Classes */,
			);
			name = macos;
			path = macos;
			sourceTree = "<group>";
		};
		48044984D6E24DA5559A28110BAC5D62 /* simsushare_player */ = {
			isa = PBXGroup;
			children = (
				042C0C616582025A4CF89DD67C0DEB46 /* macos */,
			);
			name = simsushare_player;
			path = simsushare_player;
			sourceTree = "<group>";
		};
		480A0B3C7E302F0040F06AED6BEF9B0D /* Support Files */ = {
			isa = PBXGroup;
			children = (
				5D6418645A55E19221C4D63090F983EF /* file_selector_macos.modulemap */,
				18057D4E83D5EF72D9DF3F2979818FB4 /* file_selector_macos-dummy.m */,
				38C88AC31C727DD6F478C72F0FAABEB1 /* file_selector_macos-Info.plist */,
				86A1C093F3EC3A8E6E003710A86BEF4C /* file_selector_macos-prefix.pch */,
				579565AF9E3A4D7AFD236BA08B35E74B /* file_selector_macos-umbrella.h */,
				D1F3864E5F87B5F5D65322EE84E77D26 /* file_selector_macos.debug.xcconfig */,
				9D6D73E8112E35933D84EE7261653ECE /* file_selector_macos.release.xcconfig */,
			);
			name = "Support Files";
			path = "../../../../../../Pods/Target Support Files/file_selector_macos";
			sourceTree = "<group>";
		};
		4943DDE3C839F7A8FBF87ECA118A19CC /* device_info_plus */ = {
			isa = PBXGroup;
			children = (
				7D184BB918264E8B4A424920573F1BE7 /* .. */,
				50AEBD2ED421314D90BB86C67A773E84 /* Pod */,
				A6ABDA7BA1E128FD8C767F5513658E6F /* Support Files */,
			);
			name = device_info_plus;
			path = ../Flutter/ephemeral/.symlinks/plugins/device_info_plus/macos;
			sourceTree = "<group>";
		};
		497DDFA68CE55FA3E0BA8587E6D6A2F9 /* Sources */ = {
			isa = PBXGroup;
			children = (
				24B583F56F2D6DB00D77AB63691F8B71 /* file_selector_macos */,
			);
			name = Sources;
			path = Sources;
			sourceTree = "<group>";
		};
		4B06F3D6508C391E77E72751B5BC2311 /* Sources */ = {
			isa = PBXGroup;
			children = (
				1A0FAA3B3E7E1A5374AA36AB4ABD9AE3 /* path_provider_foundation */,
			);
			name = Sources;
			path = Sources;
			sourceTree = "<group>";
		};
		4CD4A9592E7816DAB4E23F7A4D28A4C0 /* .. */ = {
			isa = PBXGroup;
			children = (
				5FD54B06E5482D99948A5FC7304180E3 /* dart */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		4DFE6471DEE7BF74152D3966267596FF /* Pod */ = {
			isa = PBXGroup;
			children = (
				D6D9D5169EFCC8E787910762D9B12DFF /* desktop_window.podspec */,
				754152008D42951744086E40F70526FD /* LICENSE */,
			);
			name = Pod;
			sourceTree = "<group>";
		};
		4E153F87DA9C3C0AFA526783AFA37A9E /* flutter */ = {
			isa = PBXGroup;
			children = (
				48044984D6E24DA5559A28110BAC5D62 /* simsushare_player */,
			);
			name = flutter;
			path = flutter;
			sourceTree = "<group>";
		};
		50AEBD2ED421314D90BB86C67A773E84 /* Pod */ = {
			isa = PBXGroup;
			children = (
				92E81C080F0D73FB37D105E1BF66779A /* device_info_plus.podspec */,
				6BE630BD354A7FDF26BC4965B6DC9588 /* LICENSE */,
			);
			name = Pod;
			sourceTree = "<group>";
		};
		53FFC3F666933BAB0F1CA43D323AEA3E /* macos */ = {
			isa = PBXGroup;
			children = (
				08CF68A424D0A805B651A76783AD88ED /* Classes */,
			);
			name = macos;
			path = macos;
			sourceTree = "<group>";
		};
		551167499EA262A191F056D914682BA4 /* Classes */ = {
			isa = PBXGroup;
			children = (
				11C8021AEA3CA9734F58D09956A37458 /* EventChannelListener.swift */,
				2697C03A5287A8DD59B095AC2DF3F465 /* MultiWindowMacosPlugin.swift */,
				EBA52E7390F4944C62BD77EFEE735447 /* MultiWindowViewController.swift */,
			);
			name = Classes;
			path = Classes;
			sourceTree = "<group>";
		};
		553152B049A2CB653ACC766E5C9EA3DE /* Flutter */ = {
			isa = PBXGroup;
			children = (
				D8F7F45F5D8DC8D0239925AE5A1B54FB /* ephemeral */,
			);
			name = Flutter;
			path = Flutter;
			sourceTree = "<group>";
		};
		57A24E67B22F0E6D7CD0F139C7F212A2 /* Flutter */ = {
			isa = PBXGroup;
			children = (
				26ED9F70A33819531DB7F95A13627E63 /* ephemeral */,
			);
			name = Flutter;
			path = Flutter;
			sourceTree = "<group>";
		};
		594C28DCE9CF4981FADAFF7C3CCE7CA6 /* .symlinks */ = {
			isa = PBXGroup;
			children = (
				C5EC5D353E36A06871C5A7F068491605 /* plugins */,
			);
			name = .symlinks;
			path = .symlinks;
			sourceTree = "<group>";
		};
		5A1E7EF8C268CAE2DE6A3472A595D4B8 /* device_info_plus */ = {
			isa = PBXGroup;
			children = (
				53FFC3F666933BAB0F1CA43D323AEA3E /* macos */,
			);
			name = device_info_plus;
			path = device_info_plus;
			sourceTree = "<group>";
		};
		5C6A142155ED3F283DC8673F4809A851 /* plugins */ = {
			isa = PBXGroup;
			children = (
				0F154B22C0F20579B156C026E3CDBE5A /* record_macos */,
			);
			name = plugins;
			path = plugins;
			sourceTree = "<group>";
		};
		5D76C7A236ED1CE9E948255EB8FB16E4 /* shared_preferences_foundation */ = {
			isa = PBXGroup;
			children = (
				2D68B5A84B528514A620F55EA5837E1A /* messages.g.swift */,
				F770A3C232123E03D36982C96131604B /* SharedPreferencesPlugin.swift */,
			);
			name = shared_preferences_foundation;
			path = shared_preferences_foundation;
			sourceTree = "<group>";
		};
		5D98F017DF76DC6B639B6417E3735A39 /* .. */ = {
			isa = PBXGroup;
			children = (
				C1C62964AFAD751B7943E36DC6A80595 /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		5DC660AF87C19ACF9DAA6E50E8C108D6 /* .. */ = {
			isa = PBXGroup;
			children = (
				D9AEAD5A8EEA960D7F81B13C59AFE4FA /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		5F34895245D51BEB4FEBF75894F72A4C /* ephemeral */ = {
			isa = PBXGroup;
			children = (
				E6CD240F9C9F4AE714C38B2A8CEC5F3B /* .symlinks */,
			);
			name = ephemeral;
			path = ephemeral;
			sourceTree = "<group>";
		};
		5FD54B06E5482D99948A5FC7304180E3 /* dart */ = {
			isa = PBXGroup;
			children = (
				7C36B0676C8B8FFC8F2AF51685F38124 /* flutter */,
			);
			name = dart;
			path = dart;
			sourceTree = "<group>";
		};
		6117E7F5011B5289ADCBE831F9E2B585 /* Pod */ = {
			isa = PBXGroup;
			children = (
				514886ECC5AEC8DF111B6B26AC1B74F5 /* file_selector_macos.podspec */,
				75FE4E3CFDDDC8C8122C0498E2C4B973 /* LICENSE */,
			);
			name = Pod;
			sourceTree = "<group>";
		};
		6631F7FC8A84DEEC23113E1260B543F3 /* Flutter */ = {
			isa = PBXGroup;
			children = (
				0A4E92E55266BA735894153AE314FCC6 /* ephemeral */,
			);
			name = Flutter;
			path = Flutter;
			sourceTree = "<group>";
		};
		664058C54466F4EACF4E09A428994770 /* macos */ = {
			isa = PBXGroup;
			children = (
				D8A2836BB29AD2450AF8E8EEA6A4F74A /* Classes */,
			);
			name = macos;
			path = macos;
			sourceTree = "<group>";
		};
		66D83FC00C9EB9039E654BD8A820FC55 /* flutter */ = {
			isa = PBXGroup;
			children = (
				2D1571815A518E4B151EAC8D9AEE8D09 /* simsushare_player */,
			);
			name = flutter;
			path = flutter;
			sourceTree = "<group>";
		};
		674C6546E237C5780FDE9C0AD38FA0EB /* macos */ = {
			isa = PBXGroup;
			children = (
				23E40870B135D795F3D3E290B635ABA3 /* Flutter */,
			);
			name = macos;
			path = macos;
			sourceTree = "<group>";
		};
		682811C30DD73DDB0F676CC726D6EA39 /* .. */ = {
			isa = PBXGroup;
			children = (
				75DF28C4D9C3616D1B06DE1DABB62B55 /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		691B0335166B03A3D160A2C86B6C48C7 /* macos */ = {
			isa = PBXGroup;
			children = (
				553152B049A2CB653ACC766E5C9EA3DE /* Flutter */,
			);
			name = macos;
			path = macos;
			sourceTree = "<group>";
		};
		69611981EE0BA8B26C0C24C563B9E408 /* path_provider_foundation */ = {
			isa = PBXGroup;
			children = (
				B2F1C2F49A0D89BF366D3B9A7E165DF8 /* .. */,
				E675ABFCAB898A683C6B2673F1F3394E /* Pod */,
				C4ADD44FD0A5C5CEE45D5F6E261C5918 /* Support Files */,
			);
			name = path_provider_foundation;
			path = ../Flutter/ephemeral/.symlinks/plugins/path_provider_foundation/darwin;
			sourceTree = "<group>";
		};
		6A3310207FC8F7510B0E9E73C6EFE89C /* flutter */ = {
			isa = PBXGroup;
			children = (
				FEAAD19AD5F72E3D44E948B43EF2B1F6 /* simsushare_player */,
			);
			name = flutter;
			path = flutter;
			sourceTree = "<group>";
		};
		6C6065238BA1C8F76C6F386D3DE32CA5 /* .. */ = {
			isa = PBXGroup;
			children = (
				388C6DBA06F4B41A3EA4FCC8C2034038 /* dart */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		6D73830B66BC0BF004D103AEBA9D50FC /* plugins */ = {
			isa = PBXGroup;
			children = (
				F60BBBD1D8692DA904FDBEFA613B3859 /* shared_preferences_foundation */,
			);
			name = plugins;
			path = plugins;
			sourceTree = "<group>";
		};
		6D91DFADEC689B06257F1214679A3D92 /* plugins */ = {
			isa = PBXGroup;
			children = (
				CDB2009473082757605217F137FE7B36 /* path_provider_foundation */,
			);
			name = plugins;
			path = plugins;
			sourceTree = "<group>";
		};
		6ECD14B4656F9716D0759F8D2F14483A /* macos */ = {
			isa = PBXGroup;
			children = (
				AF0FA26072B4C5903003514907A4E571 /* Classes */,
			);
			name = macos;
			path = macos;
			sourceTree = "<group>";
		};
		6ED30C8149D62D6E323222C5C05D9773 /* simsushare_player */ = {
			isa = PBXGroup;
			children = (
				8B90A9FA64D0385533504D73FB36322D /* macos */,
			);
			name = simsushare_player;
			path = simsushare_player;
			sourceTree = "<group>";
		};
		70B903B58A9F047C471AE2F46AE30306 /* flutter */ = {
			isa = PBXGroup;
			children = (
				BAF2F408843646002018217BF4ECB6A8 /* simsushare_player */,
			);
			name = flutter;
			path = flutter;
			sourceTree = "<group>";
		};
		74699668AF859EAFA873E814C86B540A /* path_provider_foundation */ = {
			isa = PBXGroup;
			children = (
				4B06F3D6508C391E77E72751B5BC2311 /* Sources */,
			);
			name = path_provider_foundation;
			path = path_provider_foundation;
			sourceTree = "<group>";
		};
		748C144A987CCC25FADE3810F99F6B4E /* flutter */ = {
			isa = PBXGroup;
			children = (
				6ED30C8149D62D6E323222C5C05D9773 /* simsushare_player */,
			);
			name = flutter;
			path = flutter;
			sourceTree = "<group>";
		};
		74D019539ADB7287C34652BEC6B246BE /* path_provider_foundation */ = {
			isa = PBXGroup;
			children = (
				44BE1DAB426C61870577BAE4CAD5A5D6 /* darwin */,
			);
			name = path_provider_foundation;
			path = path_provider_foundation;
			sourceTree = "<group>";
		};
		75DF28C4D9C3616D1B06DE1DABB62B55 /* .. */ = {
			isa = PBXGroup;
			children = (
				9C331BAE131FEB046BC123C466B525B3 /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		781945707F97F2331B204AC13EBE5600 /* .. */ = {
			isa = PBXGroup;
			children = (
				267A4193C70E192371F6238CB9F4BA2F /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		78BE9D9231AA016270290EBBB931AD3A /* Products */ = {
			isa = PBXGroup;
			children = (
				01E45A15AB8BA1B99F116F1BE1C66960 /* audioplayers_darwin */,
				33E180FFABEB4B86D1A1875B6D90D7D2 /* desktop_window */,
				02A736E1BDACFC0A6CAE810130768C55 /* device_info_plus */,
				C2A19B351EE3987102FF0AA4CD33F175 /* file_selector_macos */,
				A85BF03EE412FCA81FAA12493EC1761F /* multi_window_macos */,
				AE157A33FEF959A214796BFF348717F6 /* path_provider_foundation */,
				3DBD4BADE27F8B91024E4B4B4DD75DB5 /* path_provider_foundation-path_provider_foundation_privacy */,
				669E8F25E1897672BDB80B7EB784DA24 /* Pods-Runner */,
				17E1A675CF35A9437C04E6610A3A9CDD /* record_macos */,
				0BB41BB733686681BDF933A45AADFE5B /* screen_retriever */,
				93C6A45C6E4792269BE9BE0073839BF0 /* shared_preferences_foundation */,
				0652FCE3FC19056983AABE058B3CC45B /* shared_preferences_foundation-shared_preferences_foundation_privacy */,
				F4E8BCBE7387081908245ED1F253AA5F /* window_manager */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		791C6E4B424AF06279F8918FA67B8ED6 /* Targets Support Files */ = {
			isa = PBXGroup;
			children = (
				7B0D67477903E1EB8D6A9F9AF51BC5CC /* Pods-Runner */,
			);
			name = "Targets Support Files";
			sourceTree = "<group>";
		};
		7A6111B8932E6972897F2F43E09E06DE /* .. */ = {
			isa = PBXGroup;
			children = (
				1514581750F5A6EF522FF7EC09EB65CB /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		7B0D67477903E1EB8D6A9F9AF51BC5CC /* Pods-Runner */ = {
			isa = PBXGroup;
			children = (
				F49C32B3B8CF59AB437BFD7314674868 /* Pods-Runner.modulemap */,
				C6EA98402A94995D022D330B64B5203D /* Pods-Runner-acknowledgements.markdown */,
				D9F2B4CB813B4BDC4D164C6E0868930A /* Pods-Runner-acknowledgements.plist */,
				7B3C932BD54DBB963102A89E0F9E3948 /* Pods-Runner-dummy.m */,
				317C26B9A7CEDD34ADE8F37FAAB7AC20 /* Pods-Runner-frameworks.sh */,
				51825CD8F0558EFA53D9510F0E5BFA16 /* Pods-Runner-Info.plist */,
				96BF45FBE2BC9AD7B2D7E56D01B5EE46 /* Pods-Runner-umbrella.h */,
				CDAECCF4B5E08124ED410F09FD5A5DF9 /* Pods-Runner.debug.xcconfig */,
				9F79F8269DC34AC56BB3D1D9652C0D86 /* Pods-Runner.profile.xcconfig */,
				D2FA70CA298C392CB8332ADEEDD1CE85 /* Pods-Runner.release.xcconfig */,
			);
			name = "Pods-Runner";
			path = "Target Support Files/Pods-Runner";
			sourceTree = "<group>";
		};
		7B3AE6C49061A26533F48227184E6D3C /* plugins */ = {
			isa = PBXGroup;
			children = (
				74D019539ADB7287C34652BEC6B246BE /* path_provider_foundation */,
			);
			name = plugins;
			path = plugins;
			sourceTree = "<group>";
		};
		7B3E72F1224F45377F56428981C509EC /* .. */ = {
			isa = PBXGroup;
			children = (
				27EF107D2A366CB812AA866F86F11402 /* .. */,
				F3A34B30F47C16BAD5515E6CF311A113 /* dart */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		7C36B0676C8B8FFC8F2AF51685F38124 /* flutter */ = {
			isa = PBXGroup;
			children = (
				7F033D7861FDC7FACD5E3DBC5CB9690F /* simsushare_player */,
			);
			name = flutter;
			path = flutter;
			sourceTree = "<group>";
		};
		7C42930271340F0585F77CD0DB42BCC2 /* file_selector_macos */ = {
			isa = PBXGroup;
			children = (
				278E636F425B33DF6550950FF97062F3 /* macos */,
			);
			name = file_selector_macos;
			path = file_selector_macos;
			sourceTree = "<group>";
		};
		7D184BB918264E8B4A424920573F1BE7 /* .. */ = {
			isa = PBXGroup;
			children = (
				E7DE0D828D1935AD844499B58C841B5E /* .. */,
			);
			name = ..;
			path = "../../../../../../../../../../.pub-cache/hosted/pub.dev/device_info_plus-9.1.2/macos";
			sourceTree = "<group>";
		};
		7F033D7861FDC7FACD5E3DBC5CB9690F /* simsushare_player */ = {
			isa = PBXGroup;
			children = (
				691B0335166B03A3D160A2C86B6C48C7 /* macos */,
			);
			name = simsushare_player;
			path = simsushare_player;
			sourceTree = "<group>";
		};
		80778616A9D041B00E5C7E7709D31E3D /* record_macos */ = {
			isa = PBXGroup;
			children = (
				8469A8F16483AC3E48E774D6C6DEA372 /* .. */,
				2AA4EA24A89BD92BA43B5213F6AA4785 /* Pod */,
				F03846ECF6A28A3FD19A463C715AE214 /* Support Files */,
			);
			name = record_macos;
			path = ../Flutter/ephemeral/.symlinks/plugins/record_macos/macos;
			sourceTree = "<group>";
		};
		815AC9B9B2542B45A6735DAD54B97AC9 /* .symlinks */ = {
			isa = PBXGroup;
			children = (
				5C6A142155ED3F283DC8673F4809A851 /* plugins */,
			);
			name = .symlinks;
			path = .symlinks;
			sourceTree = "<group>";
		};
		82CA5CF67663853DD5A2FFE353361F1D /* desktop_window */ = {
			isa = PBXGroup;
			children = (
				85D73ABE83892E03451E8B978124AF12 /* macos */,
			);
			name = desktop_window;
			path = desktop_window;
			sourceTree = "<group>";
		};
		82D08A5AA0786EC67B74968DEB6514A2 /* simsushare_player */ = {
			isa = PBXGroup;
			children = (
				36ED723DE9BFCC8F479A1E7191164717 /* macos */,
			);
			name = simsushare_player;
			path = simsushare_player;
			sourceTree = "<group>";
		};
		8365434E0238E4DBF34309806248EAB1 /* .. */ = {
			isa = PBXGroup;
			children = (
				951FDF4F31713060655BB9CE5AB40D99 /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		8469A8F16483AC3E48E774D6C6DEA372 /* .. */ = {
			isa = PBXGroup;
			children = (
				232B6BDDE7F27DAEDF2F0D0EADCA82D8 /* .. */,
			);
			name = ..;
			path = "../../../../../../../../../../.pub-cache/hosted/pub.dev/record_macos-0.2.2/macos";
			sourceTree = "<group>";
		};
		8471370D39AD6FB0322A01B05111E4BA /* Classes */ = {
			isa = PBXGroup;
			children = (
				6F9F3D886E330E0E2F0CFFA52FD7A29E /* DesktopWindowPlugin.swift */,
			);
			name = Classes;
			path = Classes;
			sourceTree = "<group>";
		};
		85D73ABE83892E03451E8B978124AF12 /* macos */ = {
			isa = PBXGroup;
			children = (
				8471370D39AD6FB0322A01B05111E4BA /* Classes */,
			);
			name = macos;
			path = macos;
			sourceTree = "<group>";
		};
		85D9931C6F50130A9673A88ADB12ACD1 /* window_manager */ = {
			isa = PBXGroup;
			children = (
				D4984F722DEE69F142119F4A754CB1E6 /* .. */,
				B8781776E998B861185A137C4CA69765 /* Pod */,
				8EA56D02736EBD540ED9DE27AB93073F /* Support Files */,
			);
			name = window_manager;
			path = ../Flutter/ephemeral/.symlinks/plugins/window_manager/macos;
			sourceTree = "<group>";
		};
		8678CF3DA675035DD430658B930C70A7 /* macos */ = {
			isa = PBXGroup;
			children = (
				2A04AB8A0B286910188172033FC2D00D /* Classes */,
			);
			name = macos;
			path = macos;
			sourceTree = "<group>";
		};
		8692A4BBD933E2F8AA7361412D51B2CD /* macos */ = {
			isa = PBXGroup;
			children = (
				A65A2DECC4ED47D99FB4F4E2B5CB5F51 /* Flutter */,
			);
			name = macos;
			path = macos;
			sourceTree = "<group>";
		};
		8A359DBF4D802FC7A79C7D2298A82D27 /* .. */ = {
			isa = PBXGroup;
			children = (
				341259B859701C593FB2AF9091ED1853 /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		8A5439F1E13483797598F4609C25C80A /* macos */ = {
			isa = PBXGroup;
			children = (
				14FA89703B449399CD0C45754EFC9E83 /* Flutter */,
			);
			name = macos;
			path = macos;
			sourceTree = "<group>";
		};
		8B90A9FA64D0385533504D73FB36322D /* macos */ = {
			isa = PBXGroup;
			children = (
				F730B786C9D4AE44909FE6D917C28D88 /* Flutter */,
			);
			name = macos;
			path = macos;
			sourceTree = "<group>";
		};
		8C458E78FB504FBBEA4574F27125C31C /* path_provider_foundation */ = {
			isa = PBXGroup;
			children = (
				CA80F78777194A13CEC51FF81F062481 /* Sources */,
			);
			name = path_provider_foundation;
			path = path_provider_foundation;
			sourceTree = "<group>";
		};
		8C904A13E70EF5500688F368D71D9E82 /* .. */ = {
			isa = PBXGroup;
			children = (
				F010E3D7E37F0289FC9830BD2C75EFBF /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		8EA56D02736EBD540ED9DE27AB93073F /* Support Files */ = {
			isa = PBXGroup;
			children = (
				F70532D3456A3A4C813353CD0919395E /* window_manager.modulemap */,
				CFBB45B322F073F3DD81E3C9E6BB085C /* window_manager-dummy.m */,
				C966997FAAB2D575C31BFE04396EF4D8 /* window_manager-Info.plist */,
				3E0524801E48352212EC3A6BC724341C /* window_manager-prefix.pch */,
				099F395273F9F406732762E0F5192B79 /* window_manager-umbrella.h */,
				7B513B7B7C6A682534106DF17673EEF7 /* window_manager.debug.xcconfig */,
				37BE4354B66FC7EF515058A7756A93DC /* window_manager.release.xcconfig */,
			);
			name = "Support Files";
			path = "../../../../../../Pods/Target Support Files/window_manager";
			sourceTree = "<group>";
		};
		8ED2E28CD1EF5EDBB041437D92EC97D2 /* shared_preferences_foundation */ = {
			isa = PBXGroup;
			children = (
				9067859AC58407F10165B0054D5642C1 /* Sources */,
			);
			name = shared_preferences_foundation;
			path = shared_preferences_foundation;
			sourceTree = "<group>";
		};
		8EFB330BC86AD085320CD8CED943984B /* Support Files */ = {
			isa = PBXGroup;
			children = (
				BF1C29643888D89DDDE345ECCD2D391E /* desktop_window.modulemap */,
				232ED8A5BD1DA9EB400D454AB1C26762 /* desktop_window-dummy.m */,
				41470D1014189616A25E031AFE76A078 /* desktop_window-Info.plist */,
				9442E0B6509A21AB1F020079FB386258 /* desktop_window-prefix.pch */,
				53D4A14054B72270B254CAF362F1FC1C /* desktop_window-umbrella.h */,
				77D4AD8AA0942E5957F60AB59F00B4CD /* desktop_window.debug.xcconfig */,
				D623C4B9C375D2341F7B3E50B78B65AD /* desktop_window.release.xcconfig */,
			);
			name = "Support Files";
			path = "../../../../../../Pods/Target Support Files/desktop_window";
			sourceTree = "<group>";
		};
		8F5E2145CDE16DCDC124FEF59620CC9E /* flutter */ = {
			isa = PBXGroup;
			children = (
				ED6C06C72EF23847E5CC3096B77C511F /* simsushare_player */,
			);
			name = flutter;
			path = flutter;
			sourceTree = "<group>";
		};
		9067859AC58407F10165B0054D5642C1 /* Sources */ = {
			isa = PBXGroup;
			children = (
				CAD796F699158D20BE4EB1C51711431F /* shared_preferences_foundation */,
			);
			name = Sources;
			path = Sources;
			sourceTree = "<group>";
		};
		906BEDFA6BD5426B9939B48B96278AFD /* Pod */ = {
			isa = PBXGroup;
			children = (
				FD7570CACBD82F313B1B1C13A577CA2A /* LICENSE */,
				C3A575EDA494C0386C43258E5C187B6C /* multi_window_macos.podspec */,
			);
			name = Pod;
			sourceTree = "<group>";
		};
		91E506D5EF1A21020322FEE892DB5767 /* .. */ = {
			isa = PBXGroup;
			children = (
				CCE0652003077DA379661EB44C5A614F /* .. */,
			);
			name = ..;
			path = "../../../../../../../../../../.pub-cache/hosted/pub.dev/multi_window_macos-0.2.0/macos";
			sourceTree = "<group>";
		};
		949B9D22369F28B38FA08D8965F82001 /* dart */ = {
			isa = PBXGroup;
			children = (
				D24CAD86795D2EFD525A9D22FC1A4C31 /* flutter */,
			);
			name = dart;
			path = dart;
			sourceTree = "<group>";
		};
		94F6D3A2BCC382B7C75F665FA9B1074A /* darwin */ = {
			isa = PBXGroup;
			children = (
				8ED2E28CD1EF5EDBB041437D92EC97D2 /* shared_preferences_foundation */,
			);
			name = darwin;
			path = darwin;
			sourceTree = "<group>";
		};
		951FDF4F31713060655BB9CE5AB40D99 /* .. */ = {
			isa = PBXGroup;
			children = (
				F21B20924D4C7316F5FC7B40ECE26B59 /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		96C9D7481EF80483ACED4B2F2DBEBA33 /* .. */ = {
			isa = PBXGroup;
			children = (
				C114DA1CE23CD3E5A609A5CC02A34FF5 /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		996B1629984ED0E2CC5D364C703FE83F /* .. */ = {
			isa = PBXGroup;
			children = (
				A9E34513C02B1348A64E238A1DEA751B /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		9A1E543AC8D0E459FFDA6906E05FEC37 /* Support Files */ = {
			isa = PBXGroup;
			children = (
				62DBAE607D9D5DCA48F598A68486AF26 /* multi_window_macos.modulemap */,
				0446800210223FAA81EF568A7D83587A /* multi_window_macos-dummy.m */,
				9C3BA25F0B50F862177A8514F44633A4 /* multi_window_macos-Info.plist */,
				48E233068DABD1AF47FDCEE1CDA3EF41 /* multi_window_macos-prefix.pch */,
				98D3F86882658A8C966BFF17743A0195 /* multi_window_macos-umbrella.h */,
				F07EA2824A3B75CA3E588A6770C81381 /* multi_window_macos.debug.xcconfig */,
				12F01C42F31534B42E0FBE46BBF0DCF7 /* multi_window_macos.release.xcconfig */,
			);
			name = "Support Files";
			path = "../../../../../../Pods/Target Support Files/multi_window_macos";
			sourceTree = "<group>";
		};
		9C331BAE131FEB046BC123C466B525B3 /* .. */ = {
			isa = PBXGroup;
			children = (
				19BE7E48C5AE499A5ECB12DE34206378 /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		9CB8377FBCDAE561DA0B0B964E688F5A /* .symlinks */ = {
			isa = PBXGroup;
			children = (
				6D73830B66BC0BF004D103AEBA9D50FC /* plugins */,
			);
			name = .symlinks;
			path = .symlinks;
			sourceTree = "<group>";
		};
		9E611814D5901E41404B944D8D7E534E /* .. */ = {
			isa = PBXGroup;
			children = (
				19FBD261747CC0EF9D04A29A0856276D /* dart */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		9E9D532F79C279027F0FC3BC0F9079D3 /* dart */ = {
			isa = PBXGroup;
			children = (
				A68E46F02ED2FB68B0812B9816EFF9D9 /* flutter */,
			);
			name = dart;
			path = dart;
			sourceTree = "<group>";
		};
		9F03699FA3A1C8000DC9044CEF49A813 /* flutter */ = {
			isa = PBXGroup;
			children = (
				F7F96EE2F738BF7142AF3B052BD55844 /* simsushare_player */,
			);
			name = flutter;
			path = flutter;
			sourceTree = "<group>";
		};
		9F44459ED8849D368587811B2205017C /* macos */ = {
			isa = PBXGroup;
			children = (
				D84EB0F25EA300A1F64D6D0513B685EE /* Flutter */,
			);
			name = macos;
			path = macos;
			sourceTree = "<group>";
		};
		A05A64FFC2AE7E38344CBE730CBB6F77 /* .. */ = {
			isa = PBXGroup;
			children = (
				39940FBFE9A7EAE34940010E219E89C8 /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		A11BDE604AE73450527175543846E7CD /* .. */ = {
			isa = PBXGroup;
			children = (
				080F50E0B947B7CF1EE3593AA8BDC69A /* .. */,
			);
			name = ..;
			path = "../../../../../../../../../../.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.4.0/darwin/shared_preferences_foundation/Sources";
			sourceTree = "<group>";
		};
		A21B7AEA57F1040D43FB033AA89BD94E /* shared_preferences_foundation */ = {
			isa = PBXGroup;
			children = (
				CDE9074654593CD25B8AD53CC61DDB53 /* Sources */,
			);
			name = shared_preferences_foundation;
			path = shared_preferences_foundation;
			sourceTree = "<group>";
		};
		A4DF37423716C44F463FCB94CF948EF1 /* FlutterMacOS */ = {
			isa = PBXGroup;
			children = (
				F8D8D8B4998CAF27EB3DBB0EDC57B8E5 /* Pod */,
				C4FB4E9482F9CBD1677144C7FAA7C5D3 /* Support Files */,
			);
			name = FlutterMacOS;
			path = ../Flutter/ephemeral;
			sourceTree = "<group>";
		};
		A56B9E47FE87B5AF20D60F29404F90BD /* screen_retriever */ = {
			isa = PBXGroup;
			children = (
				1D140214D69DF9A3128086D78C7AD89E /* .. */,
				132E8792125578352F9A56ACC57B844C /* Pod */,
				B77CA79EFF253D847533338D4DC03A4A /* Support Files */,
			);
			name = screen_retriever;
			path = ../Flutter/ephemeral/.symlinks/plugins/screen_retriever/macos;
			sourceTree = "<group>";
		};
		A62CE63EE54F317B947E4A95DEA7412F /* .symlinks */ = {
			isa = PBXGroup;
			children = (
				D5F419DE8EAECB854A0B2C07E8BA207F /* plugins */,
			);
			name = .symlinks;
			path = .symlinks;
			sourceTree = "<group>";
		};
		A65A2DECC4ED47D99FB4F4E2B5CB5F51 /* Flutter */ = {
			isa = PBXGroup;
			children = (
				0E4FB55926F7D1291B207FA30BC89F4B /* ephemeral */,
			);
			name = Flutter;
			path = Flutter;
			sourceTree = "<group>";
		};
		A6624C93E72F2FF714A31C8A7B4A5C99 /* Flutter */ = {
			isa = PBXGroup;
			children = (
				C330991DF0D5BE878C67FBE589720423 /* ephemeral */,
			);
			name = Flutter;
			path = Flutter;
			sourceTree = "<group>";
		};
		A68E46F02ED2FB68B0812B9816EFF9D9 /* flutter */ = {
			isa = PBXGroup;
			children = (
				1C4A580302B6307E3B2673593EB879A3 /* simsushare_player */,
			);
			name = flutter;
			path = flutter;
			sourceTree = "<group>";
		};
		A6ABDA7BA1E128FD8C767F5513658E6F /* Support Files */ = {
			isa = PBXGroup;
			children = (
				9EAB1D8F3A5E264F8928448DBDE39776 /* device_info_plus.modulemap */,
				0C062BEF8B6E2C80D461BA635C57594B /* device_info_plus-dummy.m */,
				6C1B124B0250E8E7D0189FF8BC16C2EC /* device_info_plus-Info.plist */,
				68422028797216EB5417CCDF8413F20F /* device_info_plus-prefix.pch */,
				7F64498D32120CB3D197021132458D11 /* device_info_plus-umbrella.h */,
				69DED2038A4BDE38EB814C93289706E1 /* device_info_plus.debug.xcconfig */,
				7A72BBC57029402D7B528E6875CDF5AC /* device_info_plus.release.xcconfig */,
			);
			name = "Support Files";
			path = "../../../../../../Pods/Target Support Files/device_info_plus";
			sourceTree = "<group>";
		};
		A9E34513C02B1348A64E238A1DEA751B /* .. */ = {
			isa = PBXGroup;
			children = (
				B7A9E777F18B00FA8C428637E63FE02D /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		ACD7C7F87CD3AE492BF2FB172B9013D4 /* macos */ = {
			isa = PBXGroup;
			children = (
				6631F7FC8A84DEEC23113E1260B543F3 /* Flutter */,
			);
			name = macos;
			path = macos;
			sourceTree = "<group>";
		};
		AF0FA26072B4C5903003514907A4E571 /* Classes */ = {
			isa = PBXGroup;
			children = (
				05E64873068573B0653A4E30E29DFF2E /* ScreenRetrieverPlugin.swift */,
			);
			name = Classes;
			path = Classes;
			sourceTree = "<group>";
		};
		B12C84E650C18D87D3C8DBC03DC2A0D1 /* macos */ = {
			isa = PBXGroup;
			children = (
				459693837366EB151FF86EDA4BD65FCC /* Flutter */,
			);
			name = macos;
			path = macos;
			sourceTree = "<group>";
		};
		B2F1C2F49A0D89BF366D3B9A7E165DF8 /* .. */ = {
			isa = PBXGroup;
			children = (
				8A359DBF4D802FC7A79C7D2298A82D27 /* .. */,
			);
			name = ..;
			path = "../../../../../../../../../../.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.0/darwin/path_provider_foundation/Sources";
			sourceTree = "<group>";
		};
		B77CA79EFF253D847533338D4DC03A4A /* Support Files */ = {
			isa = PBXGroup;
			children = (
				1C034ECF5A35345A15FC1BF31C7926AC /* screen_retriever.modulemap */,
				7F51189724BDD20304EBEA85AE74F065 /* screen_retriever-dummy.m */,
				8EEE5A7951E35DE6FB3063A1C94C12E3 /* screen_retriever-Info.plist */,
				54D47ECD4C522277D3C282E7EF3EEB61 /* screen_retriever-prefix.pch */,
				4BB18C29B1C5BE9E6D63AC5392CAFDB9 /* screen_retriever-umbrella.h */,
				0E48C40BE5A9502B93C3EBC3544DE5D8 /* screen_retriever.debug.xcconfig */,
				F78C7F12EF81AE51ADD9BA93D950D33A /* screen_retriever.release.xcconfig */,
			);
			name = "Support Files";
			path = "../../../../../../Pods/Target Support Files/screen_retriever";
			sourceTree = "<group>";
		};
		B7A9E777F18B00FA8C428637E63FE02D /* .. */ = {
			isa = PBXGroup;
			children = (
				38091DF1D800CC7C8374A3E359D3F2C5 /* dart */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		B867C565BA58918C51F6F1E7899E51E3 /* .symlinks */ = {
			isa = PBXGroup;
			children = (
				D22E8FFF4A7AFD147F8EB4E8D74ADC1F /* plugins */,
			);
			name = .symlinks;
			path = .symlinks;
			sourceTree = "<group>";
		};
		B8781776E998B861185A137C4CA69765 /* Pod */ = {
			isa = PBXGroup;
			children = (
				B6A32825C2C4A2B9B2B794FBDF8516AA /* LICENSE */,
				876C89302BDFBADC401ED344F366ED9A /* window_manager.podspec */,
			);
			name = Pod;
			sourceTree = "<group>";
		};
		B8E310F91E3632E936D44D55621DC3CA /* macos */ = {
			isa = PBXGroup;
			children = (
				26DDFAC336BFD40F131D82EC95200B6E /* Flutter */,
			);
			name = macos;
			path = macos;
			sourceTree = "<group>";
		};
		B9FFA040A75A095F7DD616D99ABDD849 /* darwin */ = {
			isa = PBXGroup;
			children = (
				A21B7AEA57F1040D43FB033AA89BD94E /* shared_preferences_foundation */,
			);
			name = darwin;
			path = darwin;
			sourceTree = "<group>";
		};
		BAA4BF1D54FDD3823885890027A06B85 /* shared_preferences_foundation */ = {
			isa = PBXGroup;
			children = (
				A11BDE604AE73450527175543846E7CD /* .. */,
				C87FBBBC000DCE856A43F2CAFB90AD78 /* Pod */,
				0CEC000F5E7EF5748DE215E7801099BA /* Support Files */,
			);
			name = shared_preferences_foundation;
			path = ../Flutter/ephemeral/.symlinks/plugins/shared_preferences_foundation/darwin;
			sourceTree = "<group>";
		};
		BAF2F408843646002018217BF4ECB6A8 /* simsushare_player */ = {
			isa = PBXGroup;
			children = (
				8692A4BBD933E2F8AA7361412D51B2CD /* macos */,
			);
			name = simsushare_player;
			path = simsushare_player;
			sourceTree = "<group>";
		};
		BC4AA2FBC5DA17A4B810F130F8F3BBE4 /* audioplayers_darwin */ = {
			isa = PBXGroup;
			children = (
				E7852B320FD8C302F9AD4F42DC2BE3D3 /* .. */,
				246DB407D041A3467C62E98F40A27947 /* Pod */,
				0C882EE755D2B34BB51F374CB58A2F99 /* Support Files */,
			);
			name = audioplayers_darwin;
			path = ../Flutter/ephemeral/.symlinks/plugins/audioplayers_darwin/macos;
			sourceTree = "<group>";
		};
		C024DF3957461D45B61FCF1994DEAD69 /* multi_window_macos */ = {
			isa = PBXGroup;
			children = (
				91E506D5EF1A21020322FEE892DB5767 /* .. */,
				906BEDFA6BD5426B9939B48B96278AFD /* Pod */,
				9A1E543AC8D0E459FFDA6906E05FEC37 /* Support Files */,
			);
			name = multi_window_macos;
			path = ../Flutter/ephemeral/.symlinks/plugins/multi_window_macos/macos;
			sourceTree = "<group>";
		};
		C114DA1CE23CD3E5A609A5CC02A34FF5 /* .. */ = {
			isa = PBXGroup;
			children = (
				8C904A13E70EF5500688F368D71D9E82 /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		C1C62964AFAD751B7943E36DC6A80595 /* .. */ = {
			isa = PBXGroup;
			children = (
				6C6065238BA1C8F76C6F386D3DE32CA5 /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		C1F8B28F0ED4CFBD97CC5E1AA946CBF9 /* .. */ = {
			isa = PBXGroup;
			children = (
				DA0F72E863EAC7C4DE9EC42D79E8B25B /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		C2ADB3E597293E6C54CD8F0C52184641 /* dart */ = {
			isa = PBXGroup;
			children = (
				3D62B2D1FC925F1135E925C045E86BB0 /* flutter */,
			);
			name = dart;
			path = dart;
			sourceTree = "<group>";
		};
		C330991DF0D5BE878C67FBE589720423 /* ephemeral */ = {
			isa = PBXGroup;
			children = (
				2762B2917E9660554FDD1E1C758DA3E3 /* .symlinks */,
			);
			name = ephemeral;
			path = ephemeral;
			sourceTree = "<group>";
		};
		C494908823920E3C8A507D3B40DA1EBA /* Resources */ = {
			isa = PBXGroup;
			children = (
				F905C0527912C18B12F2DB53C10CEE78 /* PrivacyInfo.xcprivacy */,
			);
			name = Resources;
			path = Resources;
			sourceTree = "<group>";
		};
		C4ADD44FD0A5C5CEE45D5F6E261C5918 /* Support Files */ = {
			isa = PBXGroup;
			children = (
				071B7B679E1835C6214C9BAB07F3A9E1 /* path_provider_foundation.modulemap */,
				5ADC014B1C650CAD0D67338F38B61AE2 /* path_provider_foundation-dummy.m */,
				0C1592B61989FC5CCE59976846E87C55 /* path_provider_foundation-Info.plist */,
				390F2751AA49E047A8C29DD2002A1E79 /* path_provider_foundation-prefix.pch */,
				470F1431FDA74F5164BE9BF1625C89F1 /* path_provider_foundation-umbrella.h */,
				7D7913D4604BB32424A45DB4E2AF6F1C /* path_provider_foundation.debug.xcconfig */,
				5416DC5D27071DE6EB0828DCF50F50F4 /* path_provider_foundation.release.xcconfig */,
				220EE919BBBFB028E630F690EBE0B093 /* ResourceBundle-path_provider_foundation_privacy-path_provider_foundation-Info.plist */,
			);
			name = "Support Files";
			path = "../../../../../../Pods/Target Support Files/path_provider_foundation";
			sourceTree = "<group>";
		};
		C4FB4E9482F9CBD1677144C7FAA7C5D3 /* Support Files */ = {
			isa = PBXGroup;
			children = (
				ACE63FC19F524D7E8EE79E7FA92DADB9 /* FlutterMacOS.debug.xcconfig */,
				1DE543BB4BB414EEC87187798D0C8776 /* FlutterMacOS.release.xcconfig */,
			);
			name = "Support Files";
			path = "../../Pods/Target Support Files/FlutterMacOS";
			sourceTree = "<group>";
		};
		C5613D9CC9A7421167C714EEB02D4FBF /* simsushare_player */ = {
			isa = PBXGroup;
			children = (
				B8E310F91E3632E936D44D55621DC3CA /* macos */,
			);
			name = simsushare_player;
			path = simsushare_player;
			sourceTree = "<group>";
		};
		C5EC5D353E36A06871C5A7F068491605 /* plugins */ = {
			isa = PBXGroup;
			children = (
				C9128E94E903F889574DA520B55F39D4 /* screen_retriever */,
			);
			name = plugins;
			path = plugins;
			sourceTree = "<group>";
		};
		C86FE0E65C5A8EBE427972185DEAE087 /* .. */ = {
			isa = PBXGroup;
			children = (
				3FED3B2E931929391DFB62716F4111AE /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		C87FBBBC000DCE856A43F2CAFB90AD78 /* Pod */ = {
			isa = PBXGroup;
			children = (
				A383D5873E138651B79B9C3A80B6DC5A /* LICENSE */,
				90749C3D7297BC7214590E4DA5A0F45C /* shared_preferences_foundation.podspec */,
			);
			name = Pod;
			sourceTree = "<group>";
		};
		C9128E94E903F889574DA520B55F39D4 /* screen_retriever */ = {
			isa = PBXGroup;
			children = (
				6ECD14B4656F9716D0759F8D2F14483A /* macos */,
			);
			name = screen_retriever;
			path = screen_retriever;
			sourceTree = "<group>";
		};
		C9EDA6A836AD66AC74632322C05EBDD1 /* .. */ = {
			isa = PBXGroup;
			children = (
				D198CD1081AC4592BE4C629B2BFA5253 /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		CA80F78777194A13CEC51FF81F062481 /* Sources */ = {
			isa = PBXGroup;
			children = (
				E3BB420C9B170D3815C1C5060661959B /* path_provider_foundation */,
			);
			name = Sources;
			path = Sources;
			sourceTree = "<group>";
		};
		CAD796F699158D20BE4EB1C51711431F /* shared_preferences_foundation */ = {
			isa = PBXGroup;
			children = (
				D1D615E27C8A15118F73AB9742360E1F /* Resources */,
			);
			name = shared_preferences_foundation;
			path = shared_preferences_foundation;
			sourceTree = "<group>";
		};
		CBDF5212A1B657101D40A5FC34EAE8E3 /* ephemeral */ = {
			isa = PBXGroup;
			children = (
				594C28DCE9CF4981FADAFF7C3CCE7CA6 /* .symlinks */,
			);
			name = ephemeral;
			path = ephemeral;
			sourceTree = "<group>";
		};
		CCE0652003077DA379661EB44C5A614F /* .. */ = {
			isa = PBXGroup;
			children = (
				F7D3D611A8D817021DC4848DC3747C02 /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		CCF60418C3A3F66ACDE72A42836B50A4 /* .. */ = {
			isa = PBXGroup;
			children = (
				9E9D532F79C279027F0FC3BC0F9079D3 /* dart */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		CDB2009473082757605217F137FE7B36 /* path_provider_foundation */ = {
			isa = PBXGroup;
			children = (
				175E40B20893A4B8B8058E7299FACA6A /* darwin */,
			);
			name = path_provider_foundation;
			path = path_provider_foundation;
			sourceTree = "<group>";
		};
		CDE9074654593CD25B8AD53CC61DDB53 /* Sources */ = {
			isa = PBXGroup;
			children = (
				5D76C7A236ED1CE9E948255EB8FB16E4 /* shared_preferences_foundation */,
			);
			name = Sources;
			path = Sources;
			sourceTree = "<group>";
		};
		CF1408CF629C7361332E53B88F7BD30C = {
			isa = PBXGroup;
			children = (
				9D940727FF8FB9C785EB98E56350EF41 /* Podfile */,
				37A8AE5E4EA9D57D8BA5DA871BC5CB71 /* Development Pods */,
				E0A1E60606E0BF6E2E10F1F01350DFE8 /* Frameworks */,
				78BE9D9231AA016270290EBBB931AD3A /* Products */,
				791C6E4B424AF06279F8918FA67B8ED6 /* Targets Support Files */,
			);
			sourceTree = "<group>";
		};
		CF5164E84B37F3E78F2DA1D69D866681 /* file_selector_macos */ = {
			isa = PBXGroup;
			children = (
				F47C6B5F10FA2E25866CF8A50F65C157 /* .. */,
				6117E7F5011B5289ADCBE831F9E2B585 /* Pod */,
				480A0B3C7E302F0040F06AED6BEF9B0D /* Support Files */,
			);
			name = file_selector_macos;
			path = ../Flutter/ephemeral/.symlinks/plugins/file_selector_macos/macos;
			sourceTree = "<group>";
		};
		D198CD1081AC4592BE4C629B2BFA5253 /* .. */ = {
			isa = PBXGroup;
			children = (
				CCF60418C3A3F66ACDE72A42836B50A4 /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		D1D615E27C8A15118F73AB9742360E1F /* Resources */ = {
			isa = PBXGroup;
			children = (
				A6E8B1B754F529483A426C3B5D25FBC8 /* PrivacyInfo.xcprivacy */,
			);
			name = Resources;
			path = Resources;
			sourceTree = "<group>";
		};
		D22E8FFF4A7AFD147F8EB4E8D74ADC1F /* plugins */ = {
			isa = PBXGroup;
			children = (
				82CA5CF67663853DD5A2FFE353361F1D /* desktop_window */,
			);
			name = plugins;
			path = plugins;
			sourceTree = "<group>";
		};
		D24CAD86795D2EFD525A9D22FC1A4C31 /* flutter */ = {
			isa = PBXGroup;
			children = (
				258A4B82B97069ED66DA0C3094EA60CA /* simsushare_player */,
			);
			name = flutter;
			path = flutter;
			sourceTree = "<group>";
		};
		D4984F722DEE69F142119F4A754CB1E6 /* .. */ = {
			isa = PBXGroup;
			children = (
				682811C30DD73DDB0F676CC726D6EA39 /* .. */,
			);
			name = ..;
			path = "../../../../../../../../../../.pub-cache/hosted/pub.dev/window_manager-0.3.9/macos";
			sourceTree = "<group>";
		};
		D5F419DE8EAECB854A0B2C07E8BA207F /* plugins */ = {
			isa = PBXGroup;
			children = (
				5A1E7EF8C268CAE2DE6A3472A595D4B8 /* device_info_plus */,
			);
			name = plugins;
			path = plugins;
			sourceTree = "<group>";
		};
		D781FEE1AA5279455FD2A339EA3ECD71 /* .symlinks */ = {
			isa = PBXGroup;
			children = (
				42F91D30693787FD9ADFCC972197A79F /* plugins */,
			);
			name = .symlinks;
			path = .symlinks;
			sourceTree = "<group>";
		};
		D84EB0F25EA300A1F64D6D0513B685EE /* Flutter */ = {
			isa = PBXGroup;
			children = (
				5F34895245D51BEB4FEBF75894F72A4C /* ephemeral */,
			);
			name = Flutter;
			path = Flutter;
			sourceTree = "<group>";
		};
		D8A2836BB29AD2450AF8E8EEA6A4F74A /* Classes */ = {
			isa = PBXGroup;
			children = (
				6A281B9F310704B1B4E888DFE81EE356 /* AudioContext.swift */,
				F88B1676BDD69C562B383E65599BD6BA /* AudioplayersDarwinPlugin.h */,
				ADDC190D957E27F003180EB428A9D7F8 /* AudioplayersDarwinPlugin.m */,
				6C91EE4CE90B2DFFBE89163CE3E272A8 /* SwiftAudioplayersDarwinPlugin.swift */,
				EEB40DFF8C7EFBEAB59D7AE711120B7F /* Utils.swift */,
				8889357EAD319C7FECE5D3A5682E0954 /* WrappedMediaPlayer.swift */,
			);
			name = Classes;
			path = Classes;
			sourceTree = "<group>";
		};
		D8F7F45F5D8DC8D0239925AE5A1B54FB /* ephemeral */ = {
			isa = PBXGroup;
			children = (
				B867C565BA58918C51F6F1E7899E51E3 /* .symlinks */,
			);
			name = ephemeral;
			path = ephemeral;
			sourceTree = "<group>";
		};
		D9AEAD5A8EEA960D7F81B13C59AFE4FA /* .. */ = {
			isa = PBXGroup;
			children = (
				F33138B4255B7B91252DC2ABC536C3D3 /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		D9E7A92613B2E4684184220341A63168 /* ephemeral */ = {
			isa = PBXGroup;
			children = (
				815AC9B9B2542B45A6735DAD54B97AC9 /* .symlinks */,
			);
			name = ephemeral;
			path = ephemeral;
			sourceTree = "<group>";
		};
		DA0F72E863EAC7C4DE9EC42D79E8B25B /* .. */ = {
			isa = PBXGroup;
			children = (
				5DC660AF87C19ACF9DAA6E50E8C108D6 /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		DA333AFF17D1DBE0362741418D4469A4 /* dart */ = {
			isa = PBXGroup;
			children = (
				66D83FC00C9EB9039E654BD8A820FC55 /* flutter */,
			);
			name = dart;
			path = dart;
			sourceTree = "<group>";
		};
		DBC7D8F9F563D777F6AF4F9FC60E4430 /* .. */ = {
			isa = PBXGroup;
			children = (
				5D98F017DF76DC6B639B6417E3735A39 /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		E0A1E60606E0BF6E2E10F1F01350DFE8 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				E2A8756CA1FF5258A0344D53C5C60EC1 /* OS X */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		E167EB7EF11DEDE734C7770CAAFDDE67 /* .. */ = {
			isa = PBXGroup;
			children = (
				05498B061942D0C25D9631E2509AE0E3 /* dart */,
			);
			name = ..;
			path = .;
			sourceTree = "<group>";
		};
		E2A8756CA1FF5258A0344D53C5C60EC1 /* OS X */ = {
			isa = PBXGroup;
			children = (
				C1430F003D39D9906881A6AC724B7931 /* Cocoa.framework */,
			);
			name = "OS X";
			sourceTree = "<group>";
		};
		E3BB420C9B170D3815C1C5060661959B /* path_provider_foundation */ = {
			isa = PBXGroup;
			children = (
				C494908823920E3C8A507D3B40DA1EBA /* Resources */,
			);
			name = path_provider_foundation;
			path = path_provider_foundation;
			sourceTree = "<group>";
		};
		E675ABFCAB898A683C6B2673F1F3394E /* Pod */ = {
			isa = PBXGroup;
			children = (
				A5A130E1EA72AF23825AEFF6DC2407FA /* LICENSE */,
				2B623D7F560364D4EDD91065B2EF55C6 /* path_provider_foundation.podspec */,
			);
			name = Pod;
			sourceTree = "<group>";
		};
		E6CD240F9C9F4AE714C38B2A8CEC5F3B /* .symlinks */ = {
			isa = PBXGroup;
			children = (
				32216A80E7BC2F21A2B06CE0916EC23D /* plugins */,
			);
			name = .symlinks;
			path = .symlinks;
			sourceTree = "<group>";
		};
		E7852B320FD8C302F9AD4F42DC2BE3D3 /* .. */ = {
			isa = PBXGroup;
			children = (
				01DBA58AD8A27CFFD4DE2E549B2F5F0E /* .. */,
			);
			name = ..;
			path = "../../../../../../../../../../.pub-cache/hosted/pub.dev/audioplayers_darwin-5.0.2/macos";
			sourceTree = "<group>";
		};
		E7DE0D828D1935AD844499B58C841B5E /* .. */ = {
			isa = PBXGroup;
			children = (
				781945707F97F2331B204AC13EBE5600 /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		E88F004D04A16175FC8E130E2F48FE44 /* .symlinks */ = {
			isa = PBXGroup;
			children = (
				6D91DFADEC689B06257F1214679A3D92 /* plugins */,
			);
			name = .symlinks;
			path = .symlinks;
			sourceTree = "<group>";
		};
		EB6D13E243849B6AE6ECEFF48F7D79B4 /* plugins */ = {
			isa = PBXGroup;
			children = (
				2A7E17B19693EA3D69C09C56C8C72397 /* window_manager */,
			);
			name = plugins;
			path = plugins;
			sourceTree = "<group>";
		};
		ED6C06C72EF23847E5CC3096B77C511F /* simsushare_player */ = {
			isa = PBXGroup;
			children = (
				8A5439F1E13483797598F4609C25C80A /* macos */,
			);
			name = simsushare_player;
			path = simsushare_player;
			sourceTree = "<group>";
		};
		F010E3D7E37F0289FC9830BD2C75EFBF /* .. */ = {
			isa = PBXGroup;
			children = (
				7B3E72F1224F45377F56428981C509EC /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		F03846ECF6A28A3FD19A463C715AE214 /* Support Files */ = {
			isa = PBXGroup;
			children = (
				9E3A7F6F393223DA683124B04FF37AE7 /* record_macos.modulemap */,
				9D09B1856882684DEB8A9A13831C988E /* record_macos-dummy.m */,
				1B4E220B78A22CE5B6CF0054A0765105 /* record_macos-Info.plist */,
				A285876C1B30409B225AA879FCA17996 /* record_macos-prefix.pch */,
				9537738151BB4F5EEE6BE544084FEBF2 /* record_macos-umbrella.h */,
				78262A3FFB3865B43720930E7C504395 /* record_macos.debug.xcconfig */,
				957172A2B536A4AE63811CCA54285E6D /* record_macos.release.xcconfig */,
			);
			name = "Support Files";
			path = "../../../../../../Pods/Target Support Files/record_macos";
			sourceTree = "<group>";
		};
		F21B20924D4C7316F5FC7B40ECE26B59 /* .. */ = {
			isa = PBXGroup;
			children = (
				F681FECE2F6C6BF7A4F525D6ECF7F2B0 /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		F33138B4255B7B91252DC2ABC536C3D3 /* .. */ = {
			isa = PBXGroup;
			children = (
				105B28EC7CCB1718D42F66301F02986A /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		F3A34B30F47C16BAD5515E6CF311A113 /* dart */ = {
			isa = PBXGroup;
			children = (
				8F5E2145CDE16DCDC124FEF59620CC9E /* flutter */,
			);
			name = dart;
			path = dart;
			sourceTree = "<group>";
		};
		F47C6B5F10FA2E25866CF8A50F65C157 /* .. */ = {
			isa = PBXGroup;
			children = (
				F58EBA6AE0AF4EB50740CE5CBBD14962 /* .. */,
			);
			name = ..;
			path = "../../../../../../../../../../.pub-cache/hosted/pub.dev/file_selector_macos-0.9.4/macos/file_selector_macos/Sources";
			sourceTree = "<group>";
		};
		F58EBA6AE0AF4EB50740CE5CBBD14962 /* .. */ = {
			isa = PBXGroup;
			children = (
				C1F8B28F0ED4CFBD97CC5E1AA946CBF9 /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		F60BBBD1D8692DA904FDBEFA613B3859 /* shared_preferences_foundation */ = {
			isa = PBXGroup;
			children = (
				94F6D3A2BCC382B7C75F665FA9B1074A /* darwin */,
			);
			name = shared_preferences_foundation;
			path = shared_preferences_foundation;
			sourceTree = "<group>";
		};
		F621D1B39A8A2018A2F635198C6C58B6 /* desktop_window */ = {
			isa = PBXGroup;
			children = (
				072DE1B1193682D628394BDF537711F7 /* .. */,
				4DFE6471DEE7BF74152D3966267596FF /* Pod */,
				8EFB330BC86AD085320CD8CED943984B /* Support Files */,
			);
			name = desktop_window;
			path = ../Flutter/ephemeral/.symlinks/plugins/desktop_window/macos;
			sourceTree = "<group>";
		};
		F681FECE2F6C6BF7A4F525D6ECF7F2B0 /* .. */ = {
			isa = PBXGroup;
			children = (
				E167EB7EF11DEDE734C7770CAAFDDE67 /* .. */,
				C2ADB3E597293E6C54CD8F0C52184641 /* dart */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		F6D562BADDEECAC2068FC9FAAF8B35BF /* .symlinks */ = {
			isa = PBXGroup;
			children = (
				7B3AE6C49061A26533F48227184E6D3C /* plugins */,
			);
			name = .symlinks;
			path = .symlinks;
			sourceTree = "<group>";
		};
		F730B786C9D4AE44909FE6D917C28D88 /* Flutter */ = {
			isa = PBXGroup;
			children = (
				CBDF5212A1B657101D40A5FC34EAE8E3 /* ephemeral */,
			);
			name = Flutter;
			path = Flutter;
			sourceTree = "<group>";
		};
		F7D3D611A8D817021DC4848DC3747C02 /* .. */ = {
			isa = PBXGroup;
			children = (
				1E229C0ABFD91F2A3CBA789AF2767625 /* .. */,
			);
			name = ..;
			path = ..;
			sourceTree = "<group>";
		};
		F7F96EE2F738BF7142AF3B052BD55844 /* simsushare_player */ = {
			isa = PBXGroup;
			children = (
				674C6546E237C5780FDE9C0AD38FA0EB /* macos */,
			);
			name = simsushare_player;
			path = simsushare_player;
			sourceTree = "<group>";
		};
		F8D8D8B4998CAF27EB3DBB0EDC57B8E5 /* Pod */ = {
			isa = PBXGroup;
			children = (
				F5518DFA488F5986586AA99160D9E913 /* FlutterMacOS.podspec */,
			);
			name = Pod;
			sourceTree = "<group>";
		};
		FAC355FE9D3D9FB817196C2533534041 /* macos */ = {
			isa = PBXGroup;
			children = (
				551167499EA262A191F056D914682BA4 /* Classes */,
			);
			name = macos;
			path = macos;
			sourceTree = "<group>";
		};
		FD9ADC2E92EAEEF8D5D6725CD44E3C7C /* Classes */ = {
			isa = PBXGroup;
			children = (
				98C1D1061D564355C42133E215916FD4 /* WindowManager.swift */,
				FD16CC156506F46993BF8DAACDD5E9A3 /* WindowManagerPlugin.swift */,
			);
			name = Classes;
			path = Classes;
			sourceTree = "<group>";
		};
		FEAAD19AD5F72E3D44E948B43EF2B1F6 /* simsushare_player */ = {
			isa = PBXGroup;
			children = (
				32E2B41EBF7EE6430A1BA649A4379476 /* macos */,
			);
			name = simsushare_player;
			path = simsushare_player;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXHeadersBuildPhase section */
		201DB07B366A0315526ED2119DC29D7C /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = **********;
			files = (
				FE5A3CE2DE3DE273D4B60A1FC8534BBD /* record_macos-umbrella.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		31AD02A85297089EBF2FD09156A0E3E7 /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = **********;
			files = (
				04C7AAFF254DE945358CC972D1BF09A0 /* window_manager-umbrella.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		3AC748D737B40B8D9EDEAC89BC2D756B /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = **********;
			files = (
				436F2E76D8BA662F7DD9A34D700F9E2B /* shared_preferences_foundation-umbrella.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		9156CAA148C19E76E4CA3E08772D0AA9 /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = **********;
			files = (
				487A8FD168E17AD3EE3A9C414898FD61 /* Pods-Runner-umbrella.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		97C502E4C2B80582B26E007A0542915C /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = **********;
			files = (
				44F3CE91ABC0E0015619072C038210BB /* device_info_plus-umbrella.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		A784CDBCD7C81402017687AE68DBC1CF /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = **********;
			files = (
				F1E7C2244FFFE807BFC6FBBF5D1B744C /* path_provider_foundation-umbrella.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		C33D43063F09D33960F8BFDB94EBDD12 /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = **********;
			files = (
				15250E8EEE6867D1DBF830F451CD1CB9 /* multi_window_macos-umbrella.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		C59BE5376E798FEEA4DD1603163E0D56 /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = **********;
			files = (
				CD14C40F02F316B96868EA1465138606 /* audioplayers_darwin-umbrella.h in Headers */,
				EC3CF062BD0AC3E2D62E10783896DEEC /* AudioplayersDarwinPlugin.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		D0CB42E63BD1867D77558917FAB560F5 /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = **********;
			files = (
				FFD1C771E6E4789E2310DC74B6FD99CC /* desktop_window-umbrella.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		F3BF1763BCC5E0FA9BFD84F959948C3F /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = **********;
			files = (
				AA63379DD5CC1A84A9B632F04E8D6E0F /* file_selector_macos-umbrella.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		FDD5FEB8ABB4AD80783A0D64E9446EB2 /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = **********;
			files = (
				6201B0DB8EB2F3AC96BAA072B91D2AF5 /* screen_retriever-umbrella.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXHeadersBuildPhase section */

/* Begin PBXNativeTarget section */
		1B7F7E3C261A66F29E18C818E182267C /* desktop_window */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = C6997AA2C88E6B1E04ED9DFA5F87A44A /* Build configuration list for PBXNativeTarget "desktop_window" */;
			buildPhases = (
				D0CB42E63BD1867D77558917FAB560F5 /* Headers */,
				E8FB5AEAACCA851D3E51913075C6B9DA /* Sources */,
				886B3F98AA5EE7F294D069C559280652 /* Frameworks */,
				5F71817640AEDF175A428EEA530B83B8 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				9B97572D7D637FB2100AFFE73C4C5EFF /* PBXTargetDependency */,
			);
			name = desktop_window;
			productName = desktop_window;
			productReference = 33E180FFABEB4B86D1A1875B6D90D7D2 /* desktop_window */;
			productType = "com.apple.product-type.framework";
		};
		29DBB609D5AD628C13D43247863E2C75 /* screen_retriever */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 947DFA75529E210046DFA19CE2BBED17 /* Build configuration list for PBXNativeTarget "screen_retriever" */;
			buildPhases = (
				FDD5FEB8ABB4AD80783A0D64E9446EB2 /* Headers */,
				FE64E47C209C0D17D4D5793D34C9724D /* Sources */,
				05E3ECACAAB2AF4E5C2EBCF0165EBB59 /* Frameworks */,
				1ED15A1DE2B555AAAE06F14A29246868 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				3B375FD07068EE6E6BA0AF0E1EB5AA1C /* PBXTargetDependency */,
			);
			name = screen_retriever;
			productName = screen_retriever;
			productReference = 0BB41BB733686681BDF933A45AADFE5B /* screen_retriever */;
			productType = "com.apple.product-type.framework";
		};
		56F581DDCB0A032454E604885E17AE3C /* path_provider_foundation */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 9BE2E94BFE4C924B683A82AC505494B7 /* Build configuration list for PBXNativeTarget "path_provider_foundation" */;
			buildPhases = (
				A784CDBCD7C81402017687AE68DBC1CF /* Headers */,
				E5F38189A463192632F895E606CF7093 /* Sources */,
				7C9CFA1F134286F648EEA7BF6DBEDD6B /* Frameworks */,
				3CD2A2D9F4A02255843027C9E1551D1B /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				173050DE904D5D283B995E17440F1E25 /* PBXTargetDependency */,
				A16E9809521751D5644BD65E0FF0542B /* PBXTargetDependency */,
			);
			name = path_provider_foundation;
			productName = path_provider_foundation;
			productReference = AE157A33FEF959A214796BFF348717F6 /* path_provider_foundation */;
			productType = "com.apple.product-type.framework";
		};
		67C6B567311D7E870E2D1A0DA187FE3B /* audioplayers_darwin */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 934A983382D0A2204AEEE933D38034FB /* Build configuration list for PBXNativeTarget "audioplayers_darwin" */;
			buildPhases = (
				C59BE5376E798FEEA4DD1603163E0D56 /* Headers */,
				6A102F1377331408F61A487FAC550521 /* Sources */,
				9FDD05BEFA0C0AE3CF8D1ECC4564D074 /* Frameworks */,
				B6E64346E3AB6EFAABCA5892DEB2E455 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				5C77053B2E269D168FDF49FEC693A1ED /* PBXTargetDependency */,
			);
			name = audioplayers_darwin;
			productName = audioplayers_darwin;
			productReference = 01E45A15AB8BA1B99F116F1BE1C66960 /* audioplayers_darwin */;
			productType = "com.apple.product-type.framework";
		};
		6A8E7C76CC52400427E368907F578553 /* record_macos */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = E6FACCFE5374CDAD22D3057980C43315 /* Build configuration list for PBXNativeTarget "record_macos" */;
			buildPhases = (
				201DB07B366A0315526ED2119DC29D7C /* Headers */,
				EF6F491A986B0D06501DEFE9930B489E /* Sources */,
				EFF1033DA0068F15D19CFF5BF309DCB6 /* Frameworks */,
				D214EC47263255B496613763D0E1B7C9 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				BB0276E097248AB64F233B94D2FA07C8 /* PBXTargetDependency */,
			);
			name = record_macos;
			productName = record_macos;
			productReference = 17E1A675CF35A9437C04E6610A3A9CDD /* record_macos */;
			productType = "com.apple.product-type.framework";
		};
		73AC765CD3B58203E603C3188F337EAF /* window_manager */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 201994AEDDFCF5DDDE70FFB5BABD1FC8 /* Build configuration list for PBXNativeTarget "window_manager" */;
			buildPhases = (
				31AD02A85297089EBF2FD09156A0E3E7 /* Headers */,
				373E62D48E52E5DDAC226A187823D147 /* Sources */,
				ECBA0A38E77985CEFAF57EAFD4875E7E /* Frameworks */,
				13E1481E1F5D72A9D7D42788437D4850 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				85CF2520AA9B5F797001BE4ED2D59EF1 /* PBXTargetDependency */,
			);
			name = window_manager;
			productName = window_manager;
			productReference = F4E8BCBE7387081908245ED1F253AA5F /* window_manager */;
			productType = "com.apple.product-type.framework";
		};
		773D4A7C7FA9CAB9B055EBF0A53821FB /* file_selector_macos */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 40D89E4919EEC2E7D1BD1B154FB8EC19 /* Build configuration list for PBXNativeTarget "file_selector_macos" */;
			buildPhases = (
				F3BF1763BCC5E0FA9BFD84F959948C3F /* Headers */,
				CE1E3716CAB16AF6DB4BF272960C84B5 /* Sources */,
				26812204F9E66782F63AE60743733A27 /* Frameworks */,
				36F6297C73D8C9240119FAB4AC09D231 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				BDBB1F93B1C3E128B45D1B7EBB3F07D1 /* PBXTargetDependency */,
			);
			name = file_selector_macos;
			productName = file_selector_macos;
			productReference = C2A19B351EE3987102FF0AA4CD33F175 /* file_selector_macos */;
			productType = "com.apple.product-type.framework";
		};
		77A51E6814DE611A5B73D37FF0402B9A /* device_info_plus */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 9117DDCDE929C633A2FBE77C414A8FE0 /* Build configuration list for PBXNativeTarget "device_info_plus" */;
			buildPhases = (
				97C502E4C2B80582B26E007A0542915C /* Headers */,
				52C95D6C4596AE3A8CD97E36858E8A51 /* Sources */,
				B7A2DF989374EB504BA80C5BD83A7176 /* Frameworks */,
				3733AFF884DC31F5E47FD3647956C3C8 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				AD32521FF309F28FC83FB552D92CC015 /* PBXTargetDependency */,
			);
			name = device_info_plus;
			productName = device_info_plus;
			productReference = 02A736E1BDACFC0A6CAE810130768C55 /* device_info_plus */;
			productType = "com.apple.product-type.framework";
		};
		8B74B458B450D74B75744B87BD747314 /* Pods-Runner */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 01F75B0EB87FD91DD058EBC7A0DC9F76 /* Build configuration list for PBXNativeTarget "Pods-Runner" */;
			buildPhases = (
				9156CAA148C19E76E4CA3E08772D0AA9 /* Headers */,
				AEB6DCC10BED8A22A11E7AE59BF32F10 /* Sources */,
				B2BB0BAFAE7ED172744D217F61DF02D6 /* Frameworks */,
				AE13D886F3756076FB0BB57634F63DA4 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				D6D9651A1D8C9E98E78E1E519DF6BD62 /* PBXTargetDependency */,
				5DB2FC5D7761D746F6D47C3A958BFC9A /* PBXTargetDependency */,
				BDD573469EE7B995EA3044917A752430 /* PBXTargetDependency */,
				4E1A08EF07F28709E1E9FDF15579DF75 /* PBXTargetDependency */,
				48A5877D8C5F2A28939F4B1E7442F180 /* PBXTargetDependency */,
				3DD97E08FCD0335583130EDC4759F579 /* PBXTargetDependency */,
				B619777ED6A067960BCAF1B2883236D9 /* PBXTargetDependency */,
				0AD88616C78C7548D8552D69DCD4509D /* PBXTargetDependency */,
				E9D9F6B38E0C100872E10E0FCC59F885 /* PBXTargetDependency */,
				EC5EFA5D65227BFAB7EEAC623A89A80A /* PBXTargetDependency */,
				DE2D3758BAE5DEC2D8BE492A18AF8DCF /* PBXTargetDependency */,
			);
			name = "Pods-Runner";
			productName = Pods_Runner;
			productReference = 669E8F25E1897672BDB80B7EB784DA24 /* Pods-Runner */;
			productType = "com.apple.product-type.framework";
		};
		AB5EE685B22D01885ADD930538E8DD3C /* shared_preferences_foundation */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 0F2183457B1DAFB5D6CB653F35D68427 /* Build configuration list for PBXNativeTarget "shared_preferences_foundation" */;
			buildPhases = (
				3AC748D737B40B8D9EDEAC89BC2D756B /* Headers */,
				207FCC669D36C80DB1722920CCDDD51E /* Sources */,
				EF0D34834B0FDCFFA3365D7C2714D862 /* Frameworks */,
				2D43A671FBDC0F753083D12C3690A0E8 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				8D84733B869304C3D7AEF15C9ADEDC17 /* PBXTargetDependency */,
				D22A106A62277891FF9A7189B67B51E1 /* PBXTargetDependency */,
			);
			name = shared_preferences_foundation;
			productName = shared_preferences_foundation;
			productReference = 93C6A45C6E4792269BE9BE0073839BF0 /* shared_preferences_foundation */;
			productType = "com.apple.product-type.framework";
		};
		B6AF8B7CEAF6321719ABBC7E770624DA /* shared_preferences_foundation-shared_preferences_foundation_privacy */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 5B4D1DAC5888498B1D9D39C1753EDF91 /* Build configuration list for PBXNativeTarget "shared_preferences_foundation-shared_preferences_foundation_privacy" */;
			buildPhases = (
				5E3CBF461A285B0926BE0CAFF324E8C3 /* Sources */,
				5BFC84C5491CD0C5896340DEBF6DB851 /* Frameworks */,
				1750107A4DE74E74D0A8695D8A7C3C57 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = "shared_preferences_foundation-shared_preferences_foundation_privacy";
			productName = shared_preferences_foundation_privacy;
			productReference = 0652FCE3FC19056983AABE058B3CC45B /* shared_preferences_foundation-shared_preferences_foundation_privacy */;
			productType = "com.apple.product-type.bundle";
		};
		CACE6618E7996464E38687E13F67D945 /* path_provider_foundation-path_provider_foundation_privacy */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 11B8F64E7F07AE14D8A120FFFB04FE52 /* Build configuration list for PBXNativeTarget "path_provider_foundation-path_provider_foundation_privacy" */;
			buildPhases = (
				8C3D1292873919062C4C1A26F3598B11 /* Sources */,
				9D9122B70348509CB0FC326C15E26B42 /* Frameworks */,
				0803C2C2302113C5C038F145B243123F /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = "path_provider_foundation-path_provider_foundation_privacy";
			productName = path_provider_foundation_privacy;
			productReference = 3DBD4BADE27F8B91024E4B4B4DD75DB5 /* path_provider_foundation-path_provider_foundation_privacy */;
			productType = "com.apple.product-type.bundle";
		};
		F43E72AFDFDC89259492104B959EC9F2 /* multi_window_macos */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 104DB6226DCF4020B269AB371F539E01 /* Build configuration list for PBXNativeTarget "multi_window_macos" */;
			buildPhases = (
				C33D43063F09D33960F8BFDB94EBDD12 /* Headers */,
				74CCD8BD4C463508B91BE92C3A3AD71C /* Sources */,
				4E682B7F1F7EF9F508BCE85BD9C32E21 /* Frameworks */,
				F69404EB0615E6956CA0CB4466981E7B /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				27B1E451FCE63114665C3D677FD489A5 /* PBXTargetDependency */,
			);
			name = multi_window_macos;
			productName = multi_window_macos;
			productReference = A85BF03EE412FCA81FAA12493EC1761F /* multi_window_macos */;
			productType = "com.apple.product-type.framework";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		BFDFE7DC352907FC980B868725387E98 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastSwiftUpdateCheck = 1500;
				LastUpgradeCheck = 1500;
			};
			buildConfigurationList = 4821239608C13582E20E6DA73FD5F1F9 /* Build configuration list for PBXProject "Pods" */;
			compatibilityVersion = "Xcode 12.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				Base,
				en,
			);
			mainGroup = CF1408CF629C7361332E53B88F7BD30C;
			productRefGroup = 78BE9D9231AA016270290EBBB931AD3A /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				67C6B567311D7E870E2D1A0DA187FE3B /* audioplayers_darwin */,
				1B7F7E3C261A66F29E18C818E182267C /* desktop_window */,
				77A51E6814DE611A5B73D37FF0402B9A /* device_info_plus */,
				773D4A7C7FA9CAB9B055EBF0A53821FB /* file_selector_macos */,
				CA272E8348BAB4CE0B0C804FB7B818C4 /* FlutterMacOS */,
				F43E72AFDFDC89259492104B959EC9F2 /* multi_window_macos */,
				56F581DDCB0A032454E604885E17AE3C /* path_provider_foundation */,
				CACE6618E7996464E38687E13F67D945 /* path_provider_foundation-path_provider_foundation_privacy */,
				8B74B458B450D74B75744B87BD747314 /* Pods-Runner */,
				6A8E7C76CC52400427E368907F578553 /* record_macos */,
				29DBB609D5AD628C13D43247863E2C75 /* screen_retriever */,
				AB5EE685B22D01885ADD930538E8DD3C /* shared_preferences_foundation */,
				B6AF8B7CEAF6321719ABBC7E770624DA /* shared_preferences_foundation-shared_preferences_foundation_privacy */,
				73AC765CD3B58203E603C3188F337EAF /* window_manager */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		0803C2C2302113C5C038F145B243123F /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
				5FEE48A50E5066C58F1393213BCDEA77 /* PrivacyInfo.xcprivacy in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		13E1481E1F5D72A9D7D42788437D4850 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		1750107A4DE74E74D0A8695D8A7C3C57 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
				A200FC7F0F229C17DD41F380D318718D /* PrivacyInfo.xcprivacy in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		1ED15A1DE2B555AAAE06F14A29246868 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		2D43A671FBDC0F753083D12C3690A0E8 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
				D6B0759EB0B7810B717B1A8AD92EB4AF /* shared_preferences_foundation-shared_preferences_foundation_privacy in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		36F6297C73D8C9240119FAB4AC09D231 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		3733AFF884DC31F5E47FD3647956C3C8 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		3CD2A2D9F4A02255843027C9E1551D1B /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
				68DD428154EE48C88AA2567DD5313830 /* path_provider_foundation-path_provider_foundation_privacy in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		5F71817640AEDF175A428EEA530B83B8 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		AE13D886F3756076FB0BB57634F63DA4 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		B6E64346E3AB6EFAABCA5892DEB2E455 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		D214EC47263255B496613763D0E1B7C9 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		F69404EB0615E6956CA0CB4466981E7B /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		207FCC669D36C80DB1722920CCDDD51E /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				CAC9560FAFDF50AA15C13FD1D56F3E5A /* messages.g.swift in Sources */,
				76D0455F85D87B8A2ABA7D922465A29D /* shared_preferences_foundation-dummy.m in Sources */,
				A765223F898A5B657BD44B857DFB5720 /* SharedPreferencesPlugin.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		373E62D48E52E5DDAC226A187823D147 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				EF7AA2401C9C3188DC138BEBDFAF2407 /* window_manager-dummy.m in Sources */,
				07EC06F281E68056E0771E653F8F4242 /* WindowManager.swift in Sources */,
				07AA0A61A4704612086224E36DE379AB /* WindowManagerPlugin.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		52C95D6C4596AE3A8CD97E36858E8A51 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				2F9F6D19A76B0556FC7354FBFC859A9A /* CwlSysctl.swift in Sources */,
				6C98832E0F8A1C617DCF536465D063AB /* device_info_plus-dummy.m in Sources */,
				55C4043BEA65AFBDE9D722C6351DA4D6 /* DeviceInfoPlusMacosPlugin.swift in Sources */,
				2D069586367A5CA2767A1358D3FC4D79 /* SystemUUID.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		5E3CBF461A285B0926BE0CAFF324E8C3 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		6A102F1377331408F61A487FAC550521 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				84998710A9A5DEFEA83B593899210332 /* AudioContext.swift in Sources */,
				E3A99C11729C575D4FE1C28284D18189 /* audioplayers_darwin-dummy.m in Sources */,
				21C0D72E936B08B479F9C6057ECFCFCB /* AudioplayersDarwinPlugin.m in Sources */,
				F16AD2E26F4A37D995D76EB108F80C11 /* SwiftAudioplayersDarwinPlugin.swift in Sources */,
				FFB5707A42B2419794141FFFC8348D40 /* Utils.swift in Sources */,
				C97AC3AA45083063A03A0EE22B7253F4 /* WrappedMediaPlayer.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		74CCD8BD4C463508B91BE92C3A3AD71C /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				6F72C1DC1A82858FDA5F7CC268A4DDA0 /* EventChannelListener.swift in Sources */,
				C5266D741824A56C87869B30AB99424D /* multi_window_macos-dummy.m in Sources */,
				C1F97623C2ACA30FCB952166C985ABA4 /* MultiWindowMacosPlugin.swift in Sources */,
				1558DEC4A8D4A0950F842306E8C95442 /* MultiWindowViewController.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		8C3D1292873919062C4C1A26F3598B11 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		AEB6DCC10BED8A22A11E7AE59BF32F10 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				0281C92FE93262465DB955E0E4BFD0AE /* Pods-Runner-dummy.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		CE1E3716CAB16AF6DB4BF272960C84B5 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				9CFB6D13187650953A9A4E4E58198B9A /* file_selector_macos-dummy.m in Sources */,
				50CB3C7D94A747F6FC8CF1D334CF1714 /* FileSelectorPlugin.swift in Sources */,
				B28BBDD61EFA65C9FA590D580723E50D /* messages.g.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		E5F38189A463192632F895E606CF7093 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				0E5A97906C5A1DEC4B90F12BDA982F10 /* messages.g.swift in Sources */,
				3F1709D0A30CFA36FDF89F94654E26B7 /* path_provider_foundation-dummy.m in Sources */,
				ED7AD705694A02558DD2DDAE917DE2D3 /* PathProviderPlugin.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		E8FB5AEAACCA851D3E51913075C6B9DA /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				BF8B65AD745297048479AB07CB9F41DA /* desktop_window-dummy.m in Sources */,
				EF1D0BC4394B8E1BEBA0D6A5D4367A6C /* DesktopWindowPlugin.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		EF6F491A986B0D06501DEFE9930B489E /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				B04DE9FDFC04DD63298DBEEA5FC6D6FF /* record_macos-dummy.m in Sources */,
				E2DDA62B8FD58FA51749BF57DF30A03B /* RecordMacosPlugin.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		FE64E47C209C0D17D4D5793D34C9724D /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				7AA11278E70264F1FC7B95DD86892F38 /* screen_retriever-dummy.m in Sources */,
				2B46654967FBBF3D52AC6740DCFCE9A9 /* ScreenRetrieverPlugin.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		0AD88616C78C7548D8552D69DCD4509D /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = record_macos;
			target = 6A8E7C76CC52400427E368907F578553 /* record_macos */;
			targetProxy = 433DD455DA18ADDBD9EBE6D857A08F4C /* PBXContainerItemProxy */;
		};
		173050DE904D5D283B995E17440F1E25 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = FlutterMacOS;
			target = CA272E8348BAB4CE0B0C804FB7B818C4 /* FlutterMacOS */;
			targetProxy = 62469C1046B243DF2B7C720102ADCAD3 /* PBXContainerItemProxy */;
		};
		27B1E451FCE63114665C3D677FD489A5 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = FlutterMacOS;
			target = CA272E8348BAB4CE0B0C804FB7B818C4 /* FlutterMacOS */;
			targetProxy = A758CD2D993E0DBF11BF6EA58C0069FD /* PBXContainerItemProxy */;
		};
		3B375FD07068EE6E6BA0AF0E1EB5AA1C /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = FlutterMacOS;
			target = CA272E8348BAB4CE0B0C804FB7B818C4 /* FlutterMacOS */;
			targetProxy = 8C516A59B5737BC669141D0A54AFCB71 /* PBXContainerItemProxy */;
		};
		3DD97E08FCD0335583130EDC4759F579 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = multi_window_macos;
			target = F43E72AFDFDC89259492104B959EC9F2 /* multi_window_macos */;
			targetProxy = E3FBBC177D15A0A6E1EB78D34E1A1CF2 /* PBXContainerItemProxy */;
		};
		48A5877D8C5F2A28939F4B1E7442F180 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = file_selector_macos;
			target = 773D4A7C7FA9CAB9B055EBF0A53821FB /* file_selector_macos */;
			targetProxy = ABAC67FCC558BB85F6AE76183B234AB7 /* PBXContainerItemProxy */;
		};
		4E1A08EF07F28709E1E9FDF15579DF75 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = device_info_plus;
			target = 77A51E6814DE611A5B73D37FF0402B9A /* device_info_plus */;
			targetProxy = 21DB6736BD6B131F749F212C6329929D /* PBXContainerItemProxy */;
		};
		5C77053B2E269D168FDF49FEC693A1ED /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = FlutterMacOS;
			target = CA272E8348BAB4CE0B0C804FB7B818C4 /* FlutterMacOS */;
			targetProxy = F2F9996F5746113E70A0D1A9EFB0800D /* PBXContainerItemProxy */;
		};
		5DB2FC5D7761D746F6D47C3A958BFC9A /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = audioplayers_darwin;
			target = 67C6B567311D7E870E2D1A0DA187FE3B /* audioplayers_darwin */;
			targetProxy = 4C29B2BDCFD54D3B89BC8FF02463728C /* PBXContainerItemProxy */;
		};
		85CF2520AA9B5F797001BE4ED2D59EF1 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = FlutterMacOS;
			target = CA272E8348BAB4CE0B0C804FB7B818C4 /* FlutterMacOS */;
			targetProxy = B9EF19AC0AB20139E95AFEBE7FD7C34C /* PBXContainerItemProxy */;
		};
		8D84733B869304C3D7AEF15C9ADEDC17 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = FlutterMacOS;
			target = CA272E8348BAB4CE0B0C804FB7B818C4 /* FlutterMacOS */;
			targetProxy = 55EB1DFFF22C33A2667A3AFFA8A327B7 /* PBXContainerItemProxy */;
		};
		9B97572D7D637FB2100AFFE73C4C5EFF /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = FlutterMacOS;
			target = CA272E8348BAB4CE0B0C804FB7B818C4 /* FlutterMacOS */;
			targetProxy = A46E15F178298CFCAAE548A8D86D5EEE /* PBXContainerItemProxy */;
		};
		A16E9809521751D5644BD65E0FF0542B /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = "path_provider_foundation-path_provider_foundation_privacy";
			target = CACE6618E7996464E38687E13F67D945 /* path_provider_foundation-path_provider_foundation_privacy */;
			targetProxy = 09AE3BE4B2E23AAC0796419A9D9BE1D5 /* PBXContainerItemProxy */;
		};
		AD32521FF309F28FC83FB552D92CC015 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = FlutterMacOS;
			target = CA272E8348BAB4CE0B0C804FB7B818C4 /* FlutterMacOS */;
			targetProxy = B952214B3B3D5AE43381B011CFB5D5AF /* PBXContainerItemProxy */;
		};
		B619777ED6A067960BCAF1B2883236D9 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = path_provider_foundation;
			target = 56F581DDCB0A032454E604885E17AE3C /* path_provider_foundation */;
			targetProxy = FF1A4AA9A40CA19B371262701FD4F6C1 /* PBXContainerItemProxy */;
		};
		BB0276E097248AB64F233B94D2FA07C8 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = FlutterMacOS;
			target = CA272E8348BAB4CE0B0C804FB7B818C4 /* FlutterMacOS */;
			targetProxy = 5EC1EFC8B1941B4150CA37E8251284A3 /* PBXContainerItemProxy */;
		};
		BDBB1F93B1C3E128B45D1B7EBB3F07D1 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = FlutterMacOS;
			target = CA272E8348BAB4CE0B0C804FB7B818C4 /* FlutterMacOS */;
			targetProxy = 97A56264649E68C4D952270B5F4261CC /* PBXContainerItemProxy */;
		};
		BDD573469EE7B995EA3044917A752430 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = desktop_window;
			target = 1B7F7E3C261A66F29E18C818E182267C /* desktop_window */;
			targetProxy = 50C328735939B9AA2F2AECB0DEA34E7E /* PBXContainerItemProxy */;
		};
		D22A106A62277891FF9A7189B67B51E1 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = "shared_preferences_foundation-shared_preferences_foundation_privacy";
			target = B6AF8B7CEAF6321719ABBC7E770624DA /* shared_preferences_foundation-shared_preferences_foundation_privacy */;
			targetProxy = 9D0536614E2BEA1FEA433B7DED1D0094 /* PBXContainerItemProxy */;
		};
		D6D9651A1D8C9E98E78E1E519DF6BD62 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = FlutterMacOS;
			target = CA272E8348BAB4CE0B0C804FB7B818C4 /* FlutterMacOS */;
			targetProxy = 36C263F1CD303F1845B4E2A19388AE3D /* PBXContainerItemProxy */;
		};
		DE2D3758BAE5DEC2D8BE492A18AF8DCF /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = window_manager;
			target = 73AC765CD3B58203E603C3188F337EAF /* window_manager */;
			targetProxy = 0C51AD193DEF3AA16AB40D8CEDBBA832 /* PBXContainerItemProxy */;
		};
		E9D9F6B38E0C100872E10E0FCC59F885 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = screen_retriever;
			target = 29DBB609D5AD628C13D43247863E2C75 /* screen_retriever */;
			targetProxy = 0C6C520CAF1EA07E3E3C6E51C1A97889 /* PBXContainerItemProxy */;
		};
		EC5EFA5D65227BFAB7EEAC623A89A80A /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = shared_preferences_foundation;
			target = AB5EE685B22D01885ADD930538E8DD3C /* shared_preferences_foundation */;
			targetProxy = 53A8B57A49FEDA5BC2EFCEA85663735B /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		03C4DAF46671167832926248FA7255EF /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 0E48C40BE5A9502B93C3EBC3544DE5D8 /* screen_retriever.debug.xcconfig */;
			buildSettings = {
				ARCHS = "$(ARCHS_STANDARD_64_BIT)";
				CLANG_ENABLE_OBJC_WEAK = NO;
				CODE_SIGNING_ALLOWED = NO;
				CODE_SIGNING_REQUIRED = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				COMBINE_HIDPI_IMAGES = YES;
				CURRENT_PROJECT_VERSION = 1;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				FRAMEWORK_SEARCH_PATHS = (
					"\"/Users/<USER>/flutter/bin/cache/artifacts/engine/darwin-x64/FlutterMacOS.xcframework/macos-arm64_x86_64\"",
					"$(inherited)",
				);
				GCC_PREFIX_HEADER = "Target Support Files/screen_retriever/screen_retriever-prefix.pch";
				INFOPLIST_FILE = "Target Support Files/screen_retriever/screen_retriever-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
					"@loader_path/Frameworks",
				);
				MODULEMAP_FILE = "Target Support Files/screen_retriever/screen_retriever.modulemap";
				PRODUCT_MODULE_NAME = screen_retriever;
				PRODUCT_NAME = screen_retriever;
				SDKROOT = macosx;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		047A6B53B864DB8EEA4C9FC281D68CA7 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = A43BBD7EFC411EAF3D8831A7C43B2ECF /* shared_preferences_foundation.debug.xcconfig */;
			buildSettings = {
				ARCHS = "$(ARCHS_STANDARD_64_BIT)";
				CLANG_ENABLE_OBJC_WEAK = NO;
				CODE_SIGNING_ALLOWED = NO;
				CODE_SIGNING_REQUIRED = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				COMBINE_HIDPI_IMAGES = YES;
				CURRENT_PROJECT_VERSION = 1;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				FRAMEWORK_SEARCH_PATHS = (
					"\"/Users/<USER>/flutter/bin/cache/artifacts/engine/darwin-x64/FlutterMacOS.xcframework/macos-arm64_x86_64\"",
					"$(inherited)",
				);
				GCC_PREFIX_HEADER = "Target Support Files/shared_preferences_foundation/shared_preferences_foundation-prefix.pch";
				INFOPLIST_FILE = "Target Support Files/shared_preferences_foundation/shared_preferences_foundation-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				MACOSX_DEPLOYMENT_TARGET = 10.14;
				MODULEMAP_FILE = "Target Support Files/shared_preferences_foundation/shared_preferences_foundation.modulemap";
				PRODUCT_MODULE_NAME = shared_preferences_foundation;
				PRODUCT_NAME = shared_preferences_foundation;
				SDKROOT = macosx;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		0AE172D8F2A4843BFACAD21A0ED26541 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"POD_CONFIGURATION_RELEASE=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				MACOSX_DEPLOYMENT_TARGET = 10.15;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				PRODUCT_NAME = "$(TARGET_NAME)";
				STRIP_INSTALLED_PRODUCT = NO;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_OPTIMIZATION_LEVEL = "-O";
				SWIFT_VERSION = 5.0;
				SYMROOT = "${SRCROOT}/../build";
			};
			name = Release;
		};
		1D6BBE705B4E14F77C21AB0FECA78CC3 /* Profile */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 1DE543BB4BB414EEC87187798D0C8776 /* FlutterMacOS.release.xcconfig */;
			buildSettings = {
				ARCHS = "$(ARCHS_STANDARD_64_BIT)";
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CLANG_ENABLE_OBJC_WEAK = NO;
				COMBINE_HIDPI_IMAGES = YES;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
				);
				MACOSX_DEPLOYMENT_TARGET = 10.14;
				SDKROOT = macosx;
			};
			name = Profile;
		};
		269DF87F48D9EBF92C52DAA1BD43CFAE /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 957172A2B536A4AE63811CCA54285E6D /* record_macos.release.xcconfig */;
			buildSettings = {
				ARCHS = "$(ARCHS_STANDARD_64_BIT)";
				CLANG_ENABLE_OBJC_WEAK = NO;
				CODE_SIGNING_ALLOWED = NO;
				CODE_SIGNING_REQUIRED = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				COMBINE_HIDPI_IMAGES = YES;
				CURRENT_PROJECT_VERSION = 1;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				FRAMEWORK_SEARCH_PATHS = (
					"\"/Users/<USER>/flutter/bin/cache/artifacts/engine/darwin-x64-release/FlutterMacOS.xcframework/macos-arm64_x86_64\"",
					"$(inherited)",
				);
				GCC_PREFIX_HEADER = "Target Support Files/record_macos/record_macos-prefix.pch";
				INFOPLIST_FILE = "Target Support Files/record_macos/record_macos-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
					"@loader_path/Frameworks",
				);
				MACOSX_DEPLOYMENT_TARGET = 10.15;
				MODULEMAP_FILE = "Target Support Files/record_macos/record_macos.modulemap";
				PRODUCT_MODULE_NAME = record_macos;
				PRODUCT_NAME = record_macos;
				SDKROOT = macosx;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
		296582F37FB1F307FD4118D752A80CF0 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = B2DD4551467702121BC01091C6578ADC /* audioplayers_darwin.release.xcconfig */;
			buildSettings = {
				ARCHS = "$(ARCHS_STANDARD_64_BIT)";
				CLANG_ENABLE_OBJC_WEAK = NO;
				CODE_SIGNING_ALLOWED = NO;
				CODE_SIGNING_REQUIRED = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				COMBINE_HIDPI_IMAGES = YES;
				CURRENT_PROJECT_VERSION = 1;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				FRAMEWORK_SEARCH_PATHS = (
					"\"/Users/<USER>/flutter/bin/cache/artifacts/engine/darwin-x64-release/FlutterMacOS.xcframework/macos-arm64_x86_64\"",
					"$(inherited)",
				);
				GCC_PREFIX_HEADER = "Target Support Files/audioplayers_darwin/audioplayers_darwin-prefix.pch";
				INFOPLIST_FILE = "Target Support Files/audioplayers_darwin/audioplayers_darwin-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
					"@loader_path/Frameworks",
				);
				MODULEMAP_FILE = "Target Support Files/audioplayers_darwin/audioplayers_darwin.modulemap";
				PRODUCT_MODULE_NAME = audioplayers_darwin;
				PRODUCT_NAME = audioplayers_darwin;
				SDKROOT = macosx;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
		31EFF54624563223F5F565FB16E93B7F /* Profile */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 957172A2B536A4AE63811CCA54285E6D /* record_macos.release.xcconfig */;
			buildSettings = {
				ARCHS = "$(ARCHS_STANDARD_64_BIT)";
				CLANG_ENABLE_OBJC_WEAK = NO;
				CODE_SIGNING_ALLOWED = NO;
				CODE_SIGNING_REQUIRED = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				COMBINE_HIDPI_IMAGES = YES;
				CURRENT_PROJECT_VERSION = 1;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				FRAMEWORK_SEARCH_PATHS = (
					"\"/Users/<USER>/flutter/bin/cache/artifacts/engine/darwin-x64-release/FlutterMacOS.xcframework/macos-arm64_x86_64\"",
					"$(inherited)",
				);
				GCC_PREFIX_HEADER = "Target Support Files/record_macos/record_macos-prefix.pch";
				INFOPLIST_FILE = "Target Support Files/record_macos/record_macos-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
					"@loader_path/Frameworks",
				);
				MACOSX_DEPLOYMENT_TARGET = 10.15;
				MODULEMAP_FILE = "Target Support Files/record_macos/record_macos.modulemap";
				PRODUCT_MODULE_NAME = record_macos;
				PRODUCT_NAME = record_macos;
				SDKROOT = macosx;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Profile;
		};
		33119897E0AD455DF867C9E13C771819 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 7D7913D4604BB32424A45DB4E2AF6F1C /* path_provider_foundation.debug.xcconfig */;
			buildSettings = {
				ARCHS = "$(ARCHS_STANDARD_64_BIT)";
				CLANG_ENABLE_OBJC_WEAK = NO;
				CODE_SIGNING_ALLOWED = NO;
				CODE_SIGNING_REQUIRED = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				COMBINE_HIDPI_IMAGES = YES;
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				FRAMEWORK_SEARCH_PATHS = (
					"\"/Users/<USER>/flutter/bin/cache/artifacts/engine/darwin-x64/FlutterMacOS.xcframework/macos-arm64_x86_64\"",
					"$(inherited)",
				);
				GCC_PREFIX_HEADER = "Target Support Files/path_provider_foundation/path_provider_foundation-prefix.pch";
				INFOPLIST_FILE = "Target Support Files/path_provider_foundation/path_provider_foundation-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
					"@loader_path/Frameworks",
				);
				MACOSX_DEPLOYMENT_TARGET = 10.14;
				MODULEMAP_FILE = "Target Support Files/path_provider_foundation/path_provider_foundation.modulemap";
				PRODUCT_MODULE_NAME = path_provider_foundation;
				PRODUCT_NAME = path_provider_foundation;
				SDKROOT = macosx;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		376901DDB874F6DB1B196DE7072B0934 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 5416DC5D27071DE6EB0828DCF50F50F4 /* path_provider_foundation.release.xcconfig */;
			buildSettings = {
				ARCHS = "$(ARCHS_STANDARD_64_BIT)";
				CLANG_ENABLE_OBJC_WEAK = NO;
				CODE_SIGNING_ALLOWED = NO;
				CODE_SIGNING_REQUIRED = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				COMBINE_HIDPI_IMAGES = YES;
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				FRAMEWORK_SEARCH_PATHS = (
					"\"/Users/<USER>/flutter/bin/cache/artifacts/engine/darwin-x64-release/FlutterMacOS.xcframework/macos-arm64_x86_64\"",
					"$(inherited)",
				);
				GCC_PREFIX_HEADER = "Target Support Files/path_provider_foundation/path_provider_foundation-prefix.pch";
				INFOPLIST_FILE = "Target Support Files/path_provider_foundation/path_provider_foundation-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
					"@loader_path/Frameworks",
				);
				MACOSX_DEPLOYMENT_TARGET = 10.14;
				MODULEMAP_FILE = "Target Support Files/path_provider_foundation/path_provider_foundation.modulemap";
				PRODUCT_MODULE_NAME = path_provider_foundation;
				PRODUCT_NAME = path_provider_foundation;
				SDKROOT = macosx;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
		39209A3F9ED03ADA6915BE4FBC26334C /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 07977345CBDC4475B7AAFC8164296A1E /* audioplayers_darwin.debug.xcconfig */;
			buildSettings = {
				ARCHS = "$(ARCHS_STANDARD_64_BIT)";
				CLANG_ENABLE_OBJC_WEAK = NO;
				CODE_SIGNING_ALLOWED = NO;
				CODE_SIGNING_REQUIRED = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				COMBINE_HIDPI_IMAGES = YES;
				CURRENT_PROJECT_VERSION = 1;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				FRAMEWORK_SEARCH_PATHS = (
					"\"/Users/<USER>/flutter/bin/cache/artifacts/engine/darwin-x64/FlutterMacOS.xcframework/macos-arm64_x86_64\"",
					"$(inherited)",
				);
				GCC_PREFIX_HEADER = "Target Support Files/audioplayers_darwin/audioplayers_darwin-prefix.pch";
				INFOPLIST_FILE = "Target Support Files/audioplayers_darwin/audioplayers_darwin-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
					"@loader_path/Frameworks",
				);
				MODULEMAP_FILE = "Target Support Files/audioplayers_darwin/audioplayers_darwin.modulemap";
				PRODUCT_MODULE_NAME = audioplayers_darwin;
				PRODUCT_NAME = audioplayers_darwin;
				SDKROOT = macosx;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		442BCE0CAFB4C5689B159534C2076082 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = CDAECCF4B5E08124ED410F09FD5A5DF9 /* Pods-Runner.debug.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = NO;
				ARCHS = "$(ARCHS_STANDARD_64_BIT)";
				CLANG_ENABLE_OBJC_WEAK = NO;
				CODE_SIGNING_ALLOWED = NO;
				CODE_SIGNING_REQUIRED = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				COMBINE_HIDPI_IMAGES = YES;
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				FRAMEWORK_SEARCH_PATHS = (
					"\"/Users/<USER>/flutter/bin/cache/artifacts/engine/darwin-x64/FlutterMacOS.xcframework/macos-arm64_x86_64\"",
					"$(inherited)",
				);
				INFOPLIST_FILE = "Target Support Files/Pods-Runner/Pods-Runner-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
					"@loader_path/Frameworks",
				);
				MACH_O_TYPE = staticlib;
				MACOSX_DEPLOYMENT_TARGET = 10.15;
				MODULEMAP_FILE = "Target Support Files/Pods-Runner/Pods-Runner.modulemap";
				OTHER_LDFLAGS = "";
				OTHER_LIBTOOLFLAGS = "";
				PODS_ROOT = "$(SRCROOT)";
				PRODUCT_BUNDLE_IDENTIFIER = "org.cocoapods.${PRODUCT_NAME:rfc1034identifier}";
				PRODUCT_NAME = "$(TARGET_NAME:c99extidentifier)";
				SDKROOT = macosx;
				SKIP_INSTALL = YES;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		443C22CC51DAC201D71D7E997A3E500D /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 5416DC5D27071DE6EB0828DCF50F50F4 /* path_provider_foundation.release.xcconfig */;
			buildSettings = {
				CODE_SIGNING_ALLOWED = NO;
				COMBINE_HIDPI_IMAGES = YES;
				CONFIGURATION_BUILD_DIR = "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/path_provider_foundation";
				IBSC_MODULE = path_provider_foundation;
				INFOPLIST_FILE = "Target Support Files/path_provider_foundation/ResourceBundle-path_provider_foundation_privacy-path_provider_foundation-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Bundles";
				MACOSX_DEPLOYMENT_TARGET = 10.14;
				PRODUCT_NAME = path_provider_foundation_privacy;
				SDKROOT = macosx;
				SKIP_INSTALL = YES;
				WRAPPER_EXTENSION = bundle;
			};
			name = Release;
		};
		48357C0EFE20EE0490FA7E48C10C62AE /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 69DED2038A4BDE38EB814C93289706E1 /* device_info_plus.debug.xcconfig */;
			buildSettings = {
				ARCHS = "$(ARCHS_STANDARD_64_BIT)";
				CLANG_ENABLE_OBJC_WEAK = NO;
				CODE_SIGNING_ALLOWED = NO;
				CODE_SIGNING_REQUIRED = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				COMBINE_HIDPI_IMAGES = YES;
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				FRAMEWORK_SEARCH_PATHS = (
					"\"/Users/<USER>/flutter/bin/cache/artifacts/engine/darwin-x64/FlutterMacOS.xcframework/macos-arm64_x86_64\"",
					"$(inherited)",
				);
				GCC_PREFIX_HEADER = "Target Support Files/device_info_plus/device_info_plus-prefix.pch";
				INFOPLIST_FILE = "Target Support Files/device_info_plus/device_info_plus-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
					"@loader_path/Frameworks",
				);
				MODULEMAP_FILE = "Target Support Files/device_info_plus/device_info_plus.modulemap";
				PRODUCT_MODULE_NAME = device_info_plus;
				PRODUCT_NAME = device_info_plus;
				SDKROOT = macosx;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		4E2B0103A2A5E1EBA6AF3BFFF5A01BDB /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = D623C4B9C375D2341F7B3E50B78B65AD /* desktop_window.release.xcconfig */;
			buildSettings = {
				ARCHS = "$(ARCHS_STANDARD_64_BIT)";
				CLANG_ENABLE_OBJC_WEAK = NO;
				CODE_SIGNING_ALLOWED = NO;
				CODE_SIGNING_REQUIRED = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				COMBINE_HIDPI_IMAGES = YES;
				CURRENT_PROJECT_VERSION = 1;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				FRAMEWORK_SEARCH_PATHS = (
					"\"/Users/<USER>/flutter/bin/cache/artifacts/engine/darwin-x64-release/FlutterMacOS.xcframework/macos-arm64_x86_64\"",
					"$(inherited)",
				);
				GCC_PREFIX_HEADER = "Target Support Files/desktop_window/desktop_window-prefix.pch";
				INFOPLIST_FILE = "Target Support Files/desktop_window/desktop_window-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
					"@loader_path/Frameworks",
				);
				MODULEMAP_FILE = "Target Support Files/desktop_window/desktop_window.modulemap";
				PRODUCT_MODULE_NAME = desktop_window;
				PRODUCT_NAME = desktop_window;
				SDKROOT = macosx;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
		504B36922CF2C96775FDB9CD58292EC0 /* Profile */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 7A72BBC57029402D7B528E6875CDF5AC /* device_info_plus.release.xcconfig */;
			buildSettings = {
				ARCHS = "$(ARCHS_STANDARD_64_BIT)";
				CLANG_ENABLE_OBJC_WEAK = NO;
				CODE_SIGNING_ALLOWED = NO;
				CODE_SIGNING_REQUIRED = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				COMBINE_HIDPI_IMAGES = YES;
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				FRAMEWORK_SEARCH_PATHS = (
					"\"/Users/<USER>/flutter/bin/cache/artifacts/engine/darwin-x64-release/FlutterMacOS.xcframework/macos-arm64_x86_64\"",
					"$(inherited)",
				);
				GCC_PREFIX_HEADER = "Target Support Files/device_info_plus/device_info_plus-prefix.pch";
				INFOPLIST_FILE = "Target Support Files/device_info_plus/device_info_plus-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
					"@loader_path/Frameworks",
				);
				MODULEMAP_FILE = "Target Support Files/device_info_plus/device_info_plus.modulemap";
				PRODUCT_MODULE_NAME = device_info_plus;
				PRODUCT_NAME = device_info_plus;
				SDKROOT = macosx;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Profile;
		};
		57AF8E001ABF465EFEA43D67032E69E6 /* Profile */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 71C50C2C23D78D0BB660AA1C64ACC544 /* shared_preferences_foundation.release.xcconfig */;
			buildSettings = {
				ARCHS = "$(ARCHS_STANDARD_64_BIT)";
				CLANG_ENABLE_OBJC_WEAK = NO;
				CODE_SIGNING_ALLOWED = NO;
				CODE_SIGNING_REQUIRED = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				COMBINE_HIDPI_IMAGES = YES;
				CURRENT_PROJECT_VERSION = 1;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				FRAMEWORK_SEARCH_PATHS = (
					"\"/Users/<USER>/flutter/bin/cache/artifacts/engine/darwin-x64-release/FlutterMacOS.xcframework/macos-arm64_x86_64\"",
					"$(inherited)",
				);
				GCC_PREFIX_HEADER = "Target Support Files/shared_preferences_foundation/shared_preferences_foundation-prefix.pch";
				INFOPLIST_FILE = "Target Support Files/shared_preferences_foundation/shared_preferences_foundation-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				MACOSX_DEPLOYMENT_TARGET = 10.14;
				MODULEMAP_FILE = "Target Support Files/shared_preferences_foundation/shared_preferences_foundation.modulemap";
				PRODUCT_MODULE_NAME = shared_preferences_foundation;
				PRODUCT_NAME = shared_preferences_foundation;
				SDKROOT = macosx;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Profile;
		};
		5BFB812ACA18FBBCBEA303D80A6EBE00 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = A43BBD7EFC411EAF3D8831A7C43B2ECF /* shared_preferences_foundation.debug.xcconfig */;
			buildSettings = {
				CODE_SIGNING_ALLOWED = NO;
				COMBINE_HIDPI_IMAGES = YES;
				CONFIGURATION_BUILD_DIR = "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/shared_preferences_foundation";
				IBSC_MODULE = shared_preferences_foundation;
				INFOPLIST_FILE = "Target Support Files/shared_preferences_foundation/ResourceBundle-shared_preferences_foundation_privacy-shared_preferences_foundation-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Bundles";
				MACOSX_DEPLOYMENT_TARGET = 10.14;
				PRODUCT_NAME = shared_preferences_foundation_privacy;
				SDKROOT = macosx;
				SKIP_INSTALL = YES;
				WRAPPER_EXTENSION = bundle;
			};
			name = Debug;
		};
		6304CA9D3F6C23CB7283745DEF044804 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 71C50C2C23D78D0BB660AA1C64ACC544 /* shared_preferences_foundation.release.xcconfig */;
			buildSettings = {
				ARCHS = "$(ARCHS_STANDARD_64_BIT)";
				CLANG_ENABLE_OBJC_WEAK = NO;
				CODE_SIGNING_ALLOWED = NO;
				CODE_SIGNING_REQUIRED = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				COMBINE_HIDPI_IMAGES = YES;
				CURRENT_PROJECT_VERSION = 1;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				FRAMEWORK_SEARCH_PATHS = (
					"\"/Users/<USER>/flutter/bin/cache/artifacts/engine/darwin-x64-release/FlutterMacOS.xcframework/macos-arm64_x86_64\"",
					"$(inherited)",
				);
				GCC_PREFIX_HEADER = "Target Support Files/shared_preferences_foundation/shared_preferences_foundation-prefix.pch";
				INFOPLIST_FILE = "Target Support Files/shared_preferences_foundation/shared_preferences_foundation-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				MACOSX_DEPLOYMENT_TARGET = 10.14;
				MODULEMAP_FILE = "Target Support Files/shared_preferences_foundation/shared_preferences_foundation.modulemap";
				PRODUCT_MODULE_NAME = shared_preferences_foundation;
				PRODUCT_NAME = shared_preferences_foundation;
				SDKROOT = macosx;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
		694CACD890AAC61AD20D00951FE087F6 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = D2FA70CA298C392CB8332ADEEDD1CE85 /* Pods-Runner.release.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = NO;
				ARCHS = "$(ARCHS_STANDARD_64_BIT)";
				CLANG_ENABLE_OBJC_WEAK = NO;
				CODE_SIGNING_ALLOWED = NO;
				CODE_SIGNING_REQUIRED = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				COMBINE_HIDPI_IMAGES = YES;
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				FRAMEWORK_SEARCH_PATHS = (
					"\"/Users/<USER>/flutter/bin/cache/artifacts/engine/darwin-x64-release/FlutterMacOS.xcframework/macos-arm64_x86_64\"",
					"$(inherited)",
				);
				INFOPLIST_FILE = "Target Support Files/Pods-Runner/Pods-Runner-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
					"@loader_path/Frameworks",
				);
				MACH_O_TYPE = staticlib;
				MACOSX_DEPLOYMENT_TARGET = 10.15;
				MODULEMAP_FILE = "Target Support Files/Pods-Runner/Pods-Runner.modulemap";
				OTHER_LDFLAGS = "";
				OTHER_LIBTOOLFLAGS = "";
				PODS_ROOT = "$(SRCROOT)";
				PRODUCT_BUNDLE_IDENTIFIER = "org.cocoapods.${PRODUCT_NAME:rfc1034identifier}";
				PRODUCT_NAME = "$(TARGET_NAME:c99extidentifier)";
				SDKROOT = macosx;
				SKIP_INSTALL = YES;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
		6ABF826C1DE3435CD7CC2135E4ACFB88 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 7D7913D4604BB32424A45DB4E2AF6F1C /* path_provider_foundation.debug.xcconfig */;
			buildSettings = {
				CODE_SIGNING_ALLOWED = NO;
				COMBINE_HIDPI_IMAGES = YES;
				CONFIGURATION_BUILD_DIR = "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/path_provider_foundation";
				IBSC_MODULE = path_provider_foundation;
				INFOPLIST_FILE = "Target Support Files/path_provider_foundation/ResourceBundle-path_provider_foundation_privacy-path_provider_foundation-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Bundles";
				MACOSX_DEPLOYMENT_TARGET = 10.14;
				PRODUCT_NAME = path_provider_foundation_privacy;
				SDKROOT = macosx;
				SKIP_INSTALL = YES;
				WRAPPER_EXTENSION = bundle;
			};
			name = Debug;
		};
		6D5F00090E634D6D50F1084337CDEE94 /* Profile */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 12F01C42F31534B42E0FBE46BBF0DCF7 /* multi_window_macos.release.xcconfig */;
			buildSettings = {
				ARCHS = "$(ARCHS_STANDARD_64_BIT)";
				CLANG_ENABLE_OBJC_WEAK = NO;
				CODE_SIGNING_ALLOWED = NO;
				CODE_SIGNING_REQUIRED = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				COMBINE_HIDPI_IMAGES = YES;
				CURRENT_PROJECT_VERSION = 1;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				FRAMEWORK_SEARCH_PATHS = (
					"\"/Users/<USER>/flutter/bin/cache/artifacts/engine/darwin-x64-release/FlutterMacOS.xcframework/macos-arm64_x86_64\"",
					"$(inherited)",
				);
				GCC_PREFIX_HEADER = "Target Support Files/multi_window_macos/multi_window_macos-prefix.pch";
				INFOPLIST_FILE = "Target Support Files/multi_window_macos/multi_window_macos-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
					"@loader_path/Frameworks",
				);
				MODULEMAP_FILE = "Target Support Files/multi_window_macos/multi_window_macos.modulemap";
				PRODUCT_MODULE_NAME = multi_window_macos;
				PRODUCT_NAME = multi_window_macos;
				SDKROOT = macosx;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Profile;
		};
		6DBCB4F753EB2C366EC0B794FD84CBF6 /* Profile */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 71C50C2C23D78D0BB660AA1C64ACC544 /* shared_preferences_foundation.release.xcconfig */;
			buildSettings = {
				CLANG_ENABLE_OBJC_WEAK = NO;
				CODE_SIGNING_ALLOWED = NO;
				COMBINE_HIDPI_IMAGES = YES;
				CONFIGURATION_BUILD_DIR = "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/shared_preferences_foundation";
				IBSC_MODULE = shared_preferences_foundation;
				INFOPLIST_FILE = "Target Support Files/shared_preferences_foundation/ResourceBundle-shared_preferences_foundation_privacy-shared_preferences_foundation-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Bundles";
				MACOSX_DEPLOYMENT_TARGET = 10.14;
				PRODUCT_NAME = shared_preferences_foundation_privacy;
				SDKROOT = macosx;
				SKIP_INSTALL = YES;
				WRAPPER_EXTENSION = bundle;
			};
			name = Profile;
		};
		71458CD0B5E8B07BF6318E5127AD261A /* Profile */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = D623C4B9C375D2341F7B3E50B78B65AD /* desktop_window.release.xcconfig */;
			buildSettings = {
				ARCHS = "$(ARCHS_STANDARD_64_BIT)";
				CLANG_ENABLE_OBJC_WEAK = NO;
				CODE_SIGNING_ALLOWED = NO;
				CODE_SIGNING_REQUIRED = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				COMBINE_HIDPI_IMAGES = YES;
				CURRENT_PROJECT_VERSION = 1;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				FRAMEWORK_SEARCH_PATHS = (
					"\"/Users/<USER>/flutter/bin/cache/artifacts/engine/darwin-x64-release/FlutterMacOS.xcframework/macos-arm64_x86_64\"",
					"$(inherited)",
				);
				GCC_PREFIX_HEADER = "Target Support Files/desktop_window/desktop_window-prefix.pch";
				INFOPLIST_FILE = "Target Support Files/desktop_window/desktop_window-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
					"@loader_path/Frameworks",
				);
				MODULEMAP_FILE = "Target Support Files/desktop_window/desktop_window.modulemap";
				PRODUCT_MODULE_NAME = desktop_window;
				PRODUCT_NAME = desktop_window;
				SDKROOT = macosx;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Profile;
		};
		7A1B8EDCAE47C423C710C6B8E267C5AB /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 12F01C42F31534B42E0FBE46BBF0DCF7 /* multi_window_macos.release.xcconfig */;
			buildSettings = {
				ARCHS = "$(ARCHS_STANDARD_64_BIT)";
				CLANG_ENABLE_OBJC_WEAK = NO;
				CODE_SIGNING_ALLOWED = NO;
				CODE_SIGNING_REQUIRED = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				COMBINE_HIDPI_IMAGES = YES;
				CURRENT_PROJECT_VERSION = 1;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				FRAMEWORK_SEARCH_PATHS = (
					"\"/Users/<USER>/flutter/bin/cache/artifacts/engine/darwin-x64-release/FlutterMacOS.xcframework/macos-arm64_x86_64\"",
					"$(inherited)",
				);
				GCC_PREFIX_HEADER = "Target Support Files/multi_window_macos/multi_window_macos-prefix.pch";
				INFOPLIST_FILE = "Target Support Files/multi_window_macos/multi_window_macos-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
					"@loader_path/Frameworks",
				);
				MODULEMAP_FILE = "Target Support Files/multi_window_macos/multi_window_macos.modulemap";
				PRODUCT_MODULE_NAME = multi_window_macos;
				PRODUCT_NAME = multi_window_macos;
				SDKROOT = macosx;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
		808E544862838153672EA12FCC127D9A /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"POD_CONFIGURATION_DEBUG=1",
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				MACOSX_DEPLOYMENT_TARGET = 10.15;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				PRODUCT_NAME = "$(TARGET_NAME)";
				STRIP_INSTALLED_PRODUCT = NO;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = DEBUG;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				SYMROOT = "${SRCROOT}/../build";
			};
			name = Debug;
		};
		865BCEFEA5E95DF99F8E8D7C1320CE2B /* Profile */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 9F79F8269DC34AC56BB3D1D9652C0D86 /* Pods-Runner.profile.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = NO;
				ARCHS = "$(ARCHS_STANDARD_64_BIT)";
				CLANG_ENABLE_OBJC_WEAK = NO;
				CODE_SIGNING_ALLOWED = NO;
				CODE_SIGNING_REQUIRED = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				COMBINE_HIDPI_IMAGES = YES;
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				FRAMEWORK_SEARCH_PATHS = (
					"\"/Users/<USER>/flutter/bin/cache/artifacts/engine/darwin-x64-release/FlutterMacOS.xcframework/macos-arm64_x86_64\"",
					"$(inherited)",
				);
				INFOPLIST_FILE = "Target Support Files/Pods-Runner/Pods-Runner-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
					"@loader_path/Frameworks",
				);
				MACH_O_TYPE = staticlib;
				MACOSX_DEPLOYMENT_TARGET = 10.15;
				MODULEMAP_FILE = "Target Support Files/Pods-Runner/Pods-Runner.modulemap";
				OTHER_LDFLAGS = "";
				OTHER_LIBTOOLFLAGS = "";
				PODS_ROOT = "$(SRCROOT)";
				PRODUCT_BUNDLE_IDENTIFIER = "org.cocoapods.${PRODUCT_NAME:rfc1034identifier}";
				PRODUCT_NAME = "$(TARGET_NAME:c99extidentifier)";
				SDKROOT = macosx;
				SKIP_INSTALL = YES;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Profile;
		};
		8DA40B30B720EFA62A732AE1DAB3FF02 /* Profile */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 9D6D73E8112E35933D84EE7261653ECE /* file_selector_macos.release.xcconfig */;
			buildSettings = {
				ARCHS = "$(ARCHS_STANDARD_64_BIT)";
				CLANG_ENABLE_OBJC_WEAK = NO;
				CODE_SIGNING_ALLOWED = NO;
				CODE_SIGNING_REQUIRED = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				COMBINE_HIDPI_IMAGES = YES;
				CURRENT_PROJECT_VERSION = 1;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				FRAMEWORK_SEARCH_PATHS = (
					"\"/Users/<USER>/flutter/bin/cache/artifacts/engine/darwin-x64-release/FlutterMacOS.xcframework/macos-arm64_x86_64\"",
					"$(inherited)",
				);
				GCC_PREFIX_HEADER = "Target Support Files/file_selector_macos/file_selector_macos-prefix.pch";
				INFOPLIST_FILE = "Target Support Files/file_selector_macos/file_selector_macos-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
					"@loader_path/Frameworks",
				);
				MACOSX_DEPLOYMENT_TARGET = 10.14;
				MODULEMAP_FILE = "Target Support Files/file_selector_macos/file_selector_macos.modulemap";
				PRODUCT_MODULE_NAME = file_selector_macos;
				PRODUCT_NAME = file_selector_macos;
				SDKROOT = macosx;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Profile;
		};
		98F1AA55823456B86D121EB712D6D724 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = F78C7F12EF81AE51ADD9BA93D950D33A /* screen_retriever.release.xcconfig */;
			buildSettings = {
				ARCHS = "$(ARCHS_STANDARD_64_BIT)";
				CLANG_ENABLE_OBJC_WEAK = NO;
				CODE_SIGNING_ALLOWED = NO;
				CODE_SIGNING_REQUIRED = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				COMBINE_HIDPI_IMAGES = YES;
				CURRENT_PROJECT_VERSION = 1;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				FRAMEWORK_SEARCH_PATHS = (
					"\"/Users/<USER>/flutter/bin/cache/artifacts/engine/darwin-x64-release/FlutterMacOS.xcframework/macos-arm64_x86_64\"",
					"$(inherited)",
				);
				GCC_PREFIX_HEADER = "Target Support Files/screen_retriever/screen_retriever-prefix.pch";
				INFOPLIST_FILE = "Target Support Files/screen_retriever/screen_retriever-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
					"@loader_path/Frameworks",
				);
				MODULEMAP_FILE = "Target Support Files/screen_retriever/screen_retriever.modulemap";
				PRODUCT_MODULE_NAME = screen_retriever;
				PRODUCT_NAME = screen_retriever;
				SDKROOT = macosx;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
		9DE79C05CBAF61D91BBD460769B9F650 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 7A72BBC57029402D7B528E6875CDF5AC /* device_info_plus.release.xcconfig */;
			buildSettings = {
				ARCHS = "$(ARCHS_STANDARD_64_BIT)";
				CLANG_ENABLE_OBJC_WEAK = NO;
				CODE_SIGNING_ALLOWED = NO;
				CODE_SIGNING_REQUIRED = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				COMBINE_HIDPI_IMAGES = YES;
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				FRAMEWORK_SEARCH_PATHS = (
					"\"/Users/<USER>/flutter/bin/cache/artifacts/engine/darwin-x64-release/FlutterMacOS.xcframework/macos-arm64_x86_64\"",
					"$(inherited)",
				);
				GCC_PREFIX_HEADER = "Target Support Files/device_info_plus/device_info_plus-prefix.pch";
				INFOPLIST_FILE = "Target Support Files/device_info_plus/device_info_plus-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
					"@loader_path/Frameworks",
				);
				MODULEMAP_FILE = "Target Support Files/device_info_plus/device_info_plus.modulemap";
				PRODUCT_MODULE_NAME = device_info_plus;
				PRODUCT_NAME = device_info_plus;
				SDKROOT = macosx;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
		A4237582D764DB9ACDCAEB574198EFF6 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 9D6D73E8112E35933D84EE7261653ECE /* file_selector_macos.release.xcconfig */;
			buildSettings = {
				ARCHS = "$(ARCHS_STANDARD_64_BIT)";
				CLANG_ENABLE_OBJC_WEAK = NO;
				CODE_SIGNING_ALLOWED = NO;
				CODE_SIGNING_REQUIRED = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				COMBINE_HIDPI_IMAGES = YES;
				CURRENT_PROJECT_VERSION = 1;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				FRAMEWORK_SEARCH_PATHS = (
					"\"/Users/<USER>/flutter/bin/cache/artifacts/engine/darwin-x64-release/FlutterMacOS.xcframework/macos-arm64_x86_64\"",
					"$(inherited)",
				);
				GCC_PREFIX_HEADER = "Target Support Files/file_selector_macos/file_selector_macos-prefix.pch";
				INFOPLIST_FILE = "Target Support Files/file_selector_macos/file_selector_macos-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
					"@loader_path/Frameworks",
				);
				MACOSX_DEPLOYMENT_TARGET = 10.14;
				MODULEMAP_FILE = "Target Support Files/file_selector_macos/file_selector_macos.modulemap";
				PRODUCT_MODULE_NAME = file_selector_macos;
				PRODUCT_NAME = file_selector_macos;
				SDKROOT = macosx;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
		AF5B76B31CAF4546B42C34718BA67232 /* Profile */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"POD_CONFIGURATION_PROFILE=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				MACOSX_DEPLOYMENT_TARGET = 10.15;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				PRODUCT_NAME = "$(TARGET_NAME)";
				STRIP_INSTALLED_PRODUCT = NO;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_OPTIMIZATION_LEVEL = "-O";
				SWIFT_VERSION = 5.0;
				SYMROOT = "${SRCROOT}/../build";
			};
			name = Profile;
		};
		B2AC953410975D4FAC01D6592A2813C0 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 1DE543BB4BB414EEC87187798D0C8776 /* FlutterMacOS.release.xcconfig */;
			buildSettings = {
				ARCHS = "$(ARCHS_STANDARD_64_BIT)";
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CLANG_ENABLE_OBJC_WEAK = NO;
				COMBINE_HIDPI_IMAGES = YES;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
				);
				MACOSX_DEPLOYMENT_TARGET = 10.14;
				SDKROOT = macosx;
			};
			name = Release;
		};
		B581CBB3B038A6C8B6929FBD00B859F3 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = F07EA2824A3B75CA3E588A6770C81381 /* multi_window_macos.debug.xcconfig */;
			buildSettings = {
				ARCHS = "$(ARCHS_STANDARD_64_BIT)";
				CLANG_ENABLE_OBJC_WEAK = NO;
				CODE_SIGNING_ALLOWED = NO;
				CODE_SIGNING_REQUIRED = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				COMBINE_HIDPI_IMAGES = YES;
				CURRENT_PROJECT_VERSION = 1;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				FRAMEWORK_SEARCH_PATHS = (
					"\"/Users/<USER>/flutter/bin/cache/artifacts/engine/darwin-x64/FlutterMacOS.xcframework/macos-arm64_x86_64\"",
					"$(inherited)",
				);
				GCC_PREFIX_HEADER = "Target Support Files/multi_window_macos/multi_window_macos-prefix.pch";
				INFOPLIST_FILE = "Target Support Files/multi_window_macos/multi_window_macos-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
					"@loader_path/Frameworks",
				);
				MODULEMAP_FILE = "Target Support Files/multi_window_macos/multi_window_macos.modulemap";
				PRODUCT_MODULE_NAME = multi_window_macos;
				PRODUCT_NAME = multi_window_macos;
				SDKROOT = macosx;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		B5B76672AF3805C19A3F939A14547A2D /* Profile */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = B2DD4551467702121BC01091C6578ADC /* audioplayers_darwin.release.xcconfig */;
			buildSettings = {
				ARCHS = "$(ARCHS_STANDARD_64_BIT)";
				CLANG_ENABLE_OBJC_WEAK = NO;
				CODE_SIGNING_ALLOWED = NO;
				CODE_SIGNING_REQUIRED = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				COMBINE_HIDPI_IMAGES = YES;
				CURRENT_PROJECT_VERSION = 1;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				FRAMEWORK_SEARCH_PATHS = (
					"\"/Users/<USER>/flutter/bin/cache/artifacts/engine/darwin-x64-release/FlutterMacOS.xcframework/macos-arm64_x86_64\"",
					"$(inherited)",
				);
				GCC_PREFIX_HEADER = "Target Support Files/audioplayers_darwin/audioplayers_darwin-prefix.pch";
				INFOPLIST_FILE = "Target Support Files/audioplayers_darwin/audioplayers_darwin-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
					"@loader_path/Frameworks",
				);
				MODULEMAP_FILE = "Target Support Files/audioplayers_darwin/audioplayers_darwin.modulemap";
				PRODUCT_MODULE_NAME = audioplayers_darwin;
				PRODUCT_NAME = audioplayers_darwin;
				SDKROOT = macosx;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Profile;
		};
		B6C5E8B5655740DA37B9CF37BD758094 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 77D4AD8AA0942E5957F60AB59F00B4CD /* desktop_window.debug.xcconfig */;
			buildSettings = {
				ARCHS = "$(ARCHS_STANDARD_64_BIT)";
				CLANG_ENABLE_OBJC_WEAK = NO;
				CODE_SIGNING_ALLOWED = NO;
				CODE_SIGNING_REQUIRED = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				COMBINE_HIDPI_IMAGES = YES;
				CURRENT_PROJECT_VERSION = 1;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				FRAMEWORK_SEARCH_PATHS = (
					"\"/Users/<USER>/flutter/bin/cache/artifacts/engine/darwin-x64/FlutterMacOS.xcframework/macos-arm64_x86_64\"",
					"$(inherited)",
				);
				GCC_PREFIX_HEADER = "Target Support Files/desktop_window/desktop_window-prefix.pch";
				INFOPLIST_FILE = "Target Support Files/desktop_window/desktop_window-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
					"@loader_path/Frameworks",
				);
				MODULEMAP_FILE = "Target Support Files/desktop_window/desktop_window.modulemap";
				PRODUCT_MODULE_NAME = desktop_window;
				PRODUCT_NAME = desktop_window;
				SDKROOT = macosx;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		C04DE55C9D75AD7E1869B17C2FB3C559 /* Profile */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 5416DC5D27071DE6EB0828DCF50F50F4 /* path_provider_foundation.release.xcconfig */;
			buildSettings = {
				CLANG_ENABLE_OBJC_WEAK = NO;
				CODE_SIGNING_ALLOWED = NO;
				COMBINE_HIDPI_IMAGES = YES;
				CONFIGURATION_BUILD_DIR = "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/path_provider_foundation";
				IBSC_MODULE = path_provider_foundation;
				INFOPLIST_FILE = "Target Support Files/path_provider_foundation/ResourceBundle-path_provider_foundation_privacy-path_provider_foundation-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Bundles";
				MACOSX_DEPLOYMENT_TARGET = 10.14;
				PRODUCT_NAME = path_provider_foundation_privacy;
				SDKROOT = macosx;
				SKIP_INSTALL = YES;
				WRAPPER_EXTENSION = bundle;
			};
			name = Profile;
		};
		C5F92F36E2C66A329C2083582CA64FC2 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 37BE4354B66FC7EF515058A7756A93DC /* window_manager.release.xcconfig */;
			buildSettings = {
				ARCHS = "$(ARCHS_STANDARD_64_BIT)";
				CLANG_ENABLE_OBJC_WEAK = NO;
				CODE_SIGNING_ALLOWED = NO;
				CODE_SIGNING_REQUIRED = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				COMBINE_HIDPI_IMAGES = YES;
				CURRENT_PROJECT_VERSION = 1;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				FRAMEWORK_SEARCH_PATHS = (
					"\"/Users/<USER>/flutter/bin/cache/artifacts/engine/darwin-x64-release/FlutterMacOS.xcframework/macos-arm64_x86_64\"",
					"$(inherited)",
				);
				GCC_PREFIX_HEADER = "Target Support Files/window_manager/window_manager-prefix.pch";
				INFOPLIST_FILE = "Target Support Files/window_manager/window_manager-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
					"@loader_path/Frameworks",
				);
				MODULEMAP_FILE = "Target Support Files/window_manager/window_manager.modulemap";
				PRODUCT_MODULE_NAME = window_manager;
				PRODUCT_NAME = window_manager;
				SDKROOT = macosx;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
		CE2607477AA9216566EB17FD418AEC28 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 7B513B7B7C6A682534106DF17673EEF7 /* window_manager.debug.xcconfig */;
			buildSettings = {
				ARCHS = "$(ARCHS_STANDARD_64_BIT)";
				CLANG_ENABLE_OBJC_WEAK = NO;
				CODE_SIGNING_ALLOWED = NO;
				CODE_SIGNING_REQUIRED = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				COMBINE_HIDPI_IMAGES = YES;
				CURRENT_PROJECT_VERSION = 1;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				FRAMEWORK_SEARCH_PATHS = (
					"\"/Users/<USER>/flutter/bin/cache/artifacts/engine/darwin-x64/FlutterMacOS.xcframework/macos-arm64_x86_64\"",
					"$(inherited)",
				);
				GCC_PREFIX_HEADER = "Target Support Files/window_manager/window_manager-prefix.pch";
				INFOPLIST_FILE = "Target Support Files/window_manager/window_manager-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
					"@loader_path/Frameworks",
				);
				MODULEMAP_FILE = "Target Support Files/window_manager/window_manager.modulemap";
				PRODUCT_MODULE_NAME = window_manager;
				PRODUCT_NAME = window_manager;
				SDKROOT = macosx;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		D287DD577FF65A8C622423EED9376BDE /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 71C50C2C23D78D0BB660AA1C64ACC544 /* shared_preferences_foundation.release.xcconfig */;
			buildSettings = {
				CODE_SIGNING_ALLOWED = NO;
				COMBINE_HIDPI_IMAGES = YES;
				CONFIGURATION_BUILD_DIR = "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/shared_preferences_foundation";
				IBSC_MODULE = shared_preferences_foundation;
				INFOPLIST_FILE = "Target Support Files/shared_preferences_foundation/ResourceBundle-shared_preferences_foundation_privacy-shared_preferences_foundation-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Bundles";
				MACOSX_DEPLOYMENT_TARGET = 10.14;
				PRODUCT_NAME = shared_preferences_foundation_privacy;
				SDKROOT = macosx;
				SKIP_INSTALL = YES;
				WRAPPER_EXTENSION = bundle;
			};
			name = Release;
		};
		DE3522BD18406F17F8719EC967A26EB4 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = ACE63FC19F524D7E8EE79E7FA92DADB9 /* FlutterMacOS.debug.xcconfig */;
			buildSettings = {
				ARCHS = "$(ARCHS_STANDARD_64_BIT)";
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CLANG_ENABLE_OBJC_WEAK = NO;
				COMBINE_HIDPI_IMAGES = YES;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
				);
				MACOSX_DEPLOYMENT_TARGET = 10.14;
				SDKROOT = macosx;
			};
			name = Debug;
		};
		E9A207D28C86A5FE424E7E701A8F743A /* Profile */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 37BE4354B66FC7EF515058A7756A93DC /* window_manager.release.xcconfig */;
			buildSettings = {
				ARCHS = "$(ARCHS_STANDARD_64_BIT)";
				CLANG_ENABLE_OBJC_WEAK = NO;
				CODE_SIGNING_ALLOWED = NO;
				CODE_SIGNING_REQUIRED = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				COMBINE_HIDPI_IMAGES = YES;
				CURRENT_PROJECT_VERSION = 1;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				FRAMEWORK_SEARCH_PATHS = (
					"\"/Users/<USER>/flutter/bin/cache/artifacts/engine/darwin-x64-release/FlutterMacOS.xcframework/macos-arm64_x86_64\"",
					"$(inherited)",
				);
				GCC_PREFIX_HEADER = "Target Support Files/window_manager/window_manager-prefix.pch";
				INFOPLIST_FILE = "Target Support Files/window_manager/window_manager-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
					"@loader_path/Frameworks",
				);
				MODULEMAP_FILE = "Target Support Files/window_manager/window_manager.modulemap";
				PRODUCT_MODULE_NAME = window_manager;
				PRODUCT_NAME = window_manager;
				SDKROOT = macosx;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Profile;
		};
		EDE6D0BDC02E59711A2B3EC334628B1C /* Profile */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = F78C7F12EF81AE51ADD9BA93D950D33A /* screen_retriever.release.xcconfig */;
			buildSettings = {
				ARCHS = "$(ARCHS_STANDARD_64_BIT)";
				CLANG_ENABLE_OBJC_WEAK = NO;
				CODE_SIGNING_ALLOWED = NO;
				CODE_SIGNING_REQUIRED = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				COMBINE_HIDPI_IMAGES = YES;
				CURRENT_PROJECT_VERSION = 1;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				FRAMEWORK_SEARCH_PATHS = (
					"\"/Users/<USER>/flutter/bin/cache/artifacts/engine/darwin-x64-release/FlutterMacOS.xcframework/macos-arm64_x86_64\"",
					"$(inherited)",
				);
				GCC_PREFIX_HEADER = "Target Support Files/screen_retriever/screen_retriever-prefix.pch";
				INFOPLIST_FILE = "Target Support Files/screen_retriever/screen_retriever-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
					"@loader_path/Frameworks",
				);
				MODULEMAP_FILE = "Target Support Files/screen_retriever/screen_retriever.modulemap";
				PRODUCT_MODULE_NAME = screen_retriever;
				PRODUCT_NAME = screen_retriever;
				SDKROOT = macosx;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Profile;
		};
		F113E04586AB3DCE6DCC39D6BA55A87E /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 78262A3FFB3865B43720930E7C504395 /* record_macos.debug.xcconfig */;
			buildSettings = {
				ARCHS = "$(ARCHS_STANDARD_64_BIT)";
				CLANG_ENABLE_OBJC_WEAK = NO;
				CODE_SIGNING_ALLOWED = NO;
				CODE_SIGNING_REQUIRED = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				COMBINE_HIDPI_IMAGES = YES;
				CURRENT_PROJECT_VERSION = 1;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				FRAMEWORK_SEARCH_PATHS = (
					"\"/Users/<USER>/flutter/bin/cache/artifacts/engine/darwin-x64/FlutterMacOS.xcframework/macos-arm64_x86_64\"",
					"$(inherited)",
				);
				GCC_PREFIX_HEADER = "Target Support Files/record_macos/record_macos-prefix.pch";
				INFOPLIST_FILE = "Target Support Files/record_macos/record_macos-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
					"@loader_path/Frameworks",
				);
				MACOSX_DEPLOYMENT_TARGET = 10.15;
				MODULEMAP_FILE = "Target Support Files/record_macos/record_macos.modulemap";
				PRODUCT_MODULE_NAME = record_macos;
				PRODUCT_NAME = record_macos;
				SDKROOT = macosx;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		F573674CC0D105389B6D43355CEC0FDE /* Profile */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 5416DC5D27071DE6EB0828DCF50F50F4 /* path_provider_foundation.release.xcconfig */;
			buildSettings = {
				ARCHS = "$(ARCHS_STANDARD_64_BIT)";
				CLANG_ENABLE_OBJC_WEAK = NO;
				CODE_SIGNING_ALLOWED = NO;
				CODE_SIGNING_REQUIRED = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				COMBINE_HIDPI_IMAGES = YES;
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				FRAMEWORK_SEARCH_PATHS = (
					"\"/Users/<USER>/flutter/bin/cache/artifacts/engine/darwin-x64-release/FlutterMacOS.xcframework/macos-arm64_x86_64\"",
					"$(inherited)",
				);
				GCC_PREFIX_HEADER = "Target Support Files/path_provider_foundation/path_provider_foundation-prefix.pch";
				INFOPLIST_FILE = "Target Support Files/path_provider_foundation/path_provider_foundation-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
					"@loader_path/Frameworks",
				);
				MACOSX_DEPLOYMENT_TARGET = 10.14;
				MODULEMAP_FILE = "Target Support Files/path_provider_foundation/path_provider_foundation.modulemap";
				PRODUCT_MODULE_NAME = path_provider_foundation;
				PRODUCT_NAME = path_provider_foundation;
				SDKROOT = macosx;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Profile;
		};
		FD9DD0A2CCC807CE5B30BBCA3BFF34C4 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = D1F3864E5F87B5F5D65322EE84E77D26 /* file_selector_macos.debug.xcconfig */;
			buildSettings = {
				ARCHS = "$(ARCHS_STANDARD_64_BIT)";
				CLANG_ENABLE_OBJC_WEAK = NO;
				CODE_SIGNING_ALLOWED = NO;
				CODE_SIGNING_REQUIRED = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				COMBINE_HIDPI_IMAGES = YES;
				CURRENT_PROJECT_VERSION = 1;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				FRAMEWORK_SEARCH_PATHS = (
					"\"/Users/<USER>/flutter/bin/cache/artifacts/engine/darwin-x64/FlutterMacOS.xcframework/macos-arm64_x86_64\"",
					"$(inherited)",
				);
				GCC_PREFIX_HEADER = "Target Support Files/file_selector_macos/file_selector_macos-prefix.pch";
				INFOPLIST_FILE = "Target Support Files/file_selector_macos/file_selector_macos-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
					"@loader_path/Frameworks",
				);
				MACOSX_DEPLOYMENT_TARGET = 10.14;
				MODULEMAP_FILE = "Target Support Files/file_selector_macos/file_selector_macos.modulemap";
				PRODUCT_MODULE_NAME = file_selector_macos;
				PRODUCT_NAME = file_selector_macos;
				SDKROOT = macosx;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		01F75B0EB87FD91DD058EBC7A0DC9F76 /* Build configuration list for PBXNativeTarget "Pods-Runner" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				442BCE0CAFB4C5689B159534C2076082 /* Debug */,
				865BCEFEA5E95DF99F8E8D7C1320CE2B /* Profile */,
				694CACD890AAC61AD20D00951FE087F6 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		0F2183457B1DAFB5D6CB653F35D68427 /* Build configuration list for PBXNativeTarget "shared_preferences_foundation" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				047A6B53B864DB8EEA4C9FC281D68CA7 /* Debug */,
				57AF8E001ABF465EFEA43D67032E69E6 /* Profile */,
				6304CA9D3F6C23CB7283745DEF044804 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		104DB6226DCF4020B269AB371F539E01 /* Build configuration list for PBXNativeTarget "multi_window_macos" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				B581CBB3B038A6C8B6929FBD00B859F3 /* Debug */,
				6D5F00090E634D6D50F1084337CDEE94 /* Profile */,
				7A1B8EDCAE47C423C710C6B8E267C5AB /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		11B8F64E7F07AE14D8A120FFFB04FE52 /* Build configuration list for PBXNativeTarget "path_provider_foundation-path_provider_foundation_privacy" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				6ABF826C1DE3435CD7CC2135E4ACFB88 /* Debug */,
				C04DE55C9D75AD7E1869B17C2FB3C559 /* Profile */,
				443C22CC51DAC201D71D7E997A3E500D /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		201994AEDDFCF5DDDE70FFB5BABD1FC8 /* Build configuration list for PBXNativeTarget "window_manager" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				CE2607477AA9216566EB17FD418AEC28 /* Debug */,
				E9A207D28C86A5FE424E7E701A8F743A /* Profile */,
				C5F92F36E2C66A329C2083582CA64FC2 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		40D89E4919EEC2E7D1BD1B154FB8EC19 /* Build configuration list for PBXNativeTarget "file_selector_macos" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				FD9DD0A2CCC807CE5B30BBCA3BFF34C4 /* Debug */,
				8DA40B30B720EFA62A732AE1DAB3FF02 /* Profile */,
				A4237582D764DB9ACDCAEB574198EFF6 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		4821239608C13582E20E6DA73FD5F1F9 /* Build configuration list for PBXProject "Pods" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				808E544862838153672EA12FCC127D9A /* Debug */,
				AF5B76B31CAF4546B42C34718BA67232 /* Profile */,
				0AE172D8F2A4843BFACAD21A0ED26541 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		5B4D1DAC5888498B1D9D39C1753EDF91 /* Build configuration list for PBXNativeTarget "shared_preferences_foundation-shared_preferences_foundation_privacy" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				5BFB812ACA18FBBCBEA303D80A6EBE00 /* Debug */,
				6DBCB4F753EB2C366EC0B794FD84CBF6 /* Profile */,
				D287DD577FF65A8C622423EED9376BDE /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		624022EE1C2EAB1C3CC38699E1368C5F /* Build configuration list for PBXAggregateTarget "FlutterMacOS" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				DE3522BD18406F17F8719EC967A26EB4 /* Debug */,
				1D6BBE705B4E14F77C21AB0FECA78CC3 /* Profile */,
				B2AC953410975D4FAC01D6592A2813C0 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		9117DDCDE929C633A2FBE77C414A8FE0 /* Build configuration list for PBXNativeTarget "device_info_plus" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				48357C0EFE20EE0490FA7E48C10C62AE /* Debug */,
				504B36922CF2C96775FDB9CD58292EC0 /* Profile */,
				9DE79C05CBAF61D91BBD460769B9F650 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		934A983382D0A2204AEEE933D38034FB /* Build configuration list for PBXNativeTarget "audioplayers_darwin" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				39209A3F9ED03ADA6915BE4FBC26334C /* Debug */,
				B5B76672AF3805C19A3F939A14547A2D /* Profile */,
				296582F37FB1F307FD4118D752A80CF0 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		947DFA75529E210046DFA19CE2BBED17 /* Build configuration list for PBXNativeTarget "screen_retriever" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				03C4DAF46671167832926248FA7255EF /* Debug */,
				EDE6D0BDC02E59711A2B3EC334628B1C /* Profile */,
				98F1AA55823456B86D121EB712D6D724 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		9BE2E94BFE4C924B683A82AC505494B7 /* Build configuration list for PBXNativeTarget "path_provider_foundation" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				33119897E0AD455DF867C9E13C771819 /* Debug */,
				F573674CC0D105389B6D43355CEC0FDE /* Profile */,
				376901DDB874F6DB1B196DE7072B0934 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		C6997AA2C88E6B1E04ED9DFA5F87A44A /* Build configuration list for PBXNativeTarget "desktop_window" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				B6C5E8B5655740DA37B9CF37BD758094 /* Debug */,
				71458CD0B5E8B07BF6318E5127AD261A /* Profile */,
				4E2B0103A2A5E1EBA6AF3BFFF5A01BDB /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		E6FACCFE5374CDAD22D3057980C43315 /* Build configuration list for PBXNativeTarget "record_macos" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				F113E04586AB3DCE6DCC39D6BA55A87E /* Debug */,
				31EFF54624563223F5F565FB16E93B7F /* Profile */,
				269DF87F48D9EBF92C52DAA1BD43CFAE /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = BFDFE7DC352907FC980B868725387E98 /* Project object */;
}
