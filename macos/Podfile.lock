PODS:
  - audioplayers_darwin (0.0.1):
    - FlutterMacOS
  - desktop_window (0.0.1):
    - FlutterMacOS
  - device_info_plus (0.0.1):
    - FlutterMacOS
  - file_selector_macos (0.0.1):
    - FlutterMacOS
  - FlutterMacOS (1.0.0)
  - multi_window_macos (0.0.1):
    - FlutterMacOS
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - record_macos (0.2.0):
    - FlutterMacOS
  - screen_retriever (0.0.1):
    - FlutterMacOS
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - window_manager (0.2.0):
    - FlutterMacOS

DEPENDENCIES:
  - audioplayers_darwin (from `Flutter/ephemeral/.symlinks/plugins/audioplayers_darwin/macos`)
  - desktop_window (from `Flutter/ephemeral/.symlinks/plugins/desktop_window/macos`)
  - device_info_plus (from `Flutter/ephemeral/.symlinks/plugins/device_info_plus/macos`)
  - file_selector_macos (from `Flutter/ephemeral/.symlinks/plugins/file_selector_macos/macos`)
  - FlutterMacOS (from `Flutter/ephemeral`)
  - multi_window_macos (from `Flutter/ephemeral/.symlinks/plugins/multi_window_macos/macos`)
  - path_provider_foundation (from `Flutter/ephemeral/.symlinks/plugins/path_provider_foundation/darwin`)
  - record_macos (from `Flutter/ephemeral/.symlinks/plugins/record_macos/macos`)
  - screen_retriever (from `Flutter/ephemeral/.symlinks/plugins/screen_retriever/macos`)
  - shared_preferences_foundation (from `Flutter/ephemeral/.symlinks/plugins/shared_preferences_foundation/darwin`)
  - window_manager (from `Flutter/ephemeral/.symlinks/plugins/window_manager/macos`)

EXTERNAL SOURCES:
  audioplayers_darwin:
    :path: Flutter/ephemeral/.symlinks/plugins/audioplayers_darwin/macos
  desktop_window:
    :path: Flutter/ephemeral/.symlinks/plugins/desktop_window/macos
  device_info_plus:
    :path: Flutter/ephemeral/.symlinks/plugins/device_info_plus/macos
  file_selector_macos:
    :path: Flutter/ephemeral/.symlinks/plugins/file_selector_macos/macos
  FlutterMacOS:
    :path: Flutter/ephemeral
  multi_window_macos:
    :path: Flutter/ephemeral/.symlinks/plugins/multi_window_macos/macos
  path_provider_foundation:
    :path: Flutter/ephemeral/.symlinks/plugins/path_provider_foundation/darwin
  record_macos:
    :path: Flutter/ephemeral/.symlinks/plugins/record_macos/macos
  screen_retriever:
    :path: Flutter/ephemeral/.symlinks/plugins/screen_retriever/macos
  shared_preferences_foundation:
    :path: Flutter/ephemeral/.symlinks/plugins/shared_preferences_foundation/darwin
  window_manager:
    :path: Flutter/ephemeral/.symlinks/plugins/window_manager/macos

SPEC CHECKSUMS:
  audioplayers_darwin: dcad41de4fbd0099cb3749f7ab3b0cb8f70b810c
  desktop_window: fb7c4f12c1129f947ac482296b6f14059d57a3c3
  device_info_plus: 5401765fde0b8d062a2f8eb65510fb17e77cf07f
  file_selector_macos: 54fdab7caa3ac3fc43c9fac4d7d8d231277f8cf2
  FlutterMacOS: 8f6f14fa908a6fb3fba0cd85dbd81ec4b251fb24
  multi_window_macos: dffe0ffd4274adcc78c8482323d3742fada1aed7
  path_provider_foundation: 2b6b4c569c0fb62ec74538f866245ac84301af46
  record_macos: 937889e0f2a7a12b6fc14e97a3678e5a18943de6
  screen_retriever: 59634572a57080243dd1bf715e55b6c54f241a38
  shared_preferences_foundation: fcdcbc04712aee1108ac7fda236f363274528f78
  window_manager: 3a1844359a6295ab1e47659b1a777e36773cd6e8

PODFILE CHECKSUM: 9ebaf0ce3d369aaa26a9ea0e159195ed94724cf3

COCOAPODS: 1.15.2
