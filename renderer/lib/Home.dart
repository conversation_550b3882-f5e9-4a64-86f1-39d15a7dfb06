import 'dart:typed_data';

import 'package:archive/archive.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
// import 'package:simsushare_player/pages/Preview.dart';
import 'package:renderer/utils/web_parser.dart';
import 'package:simsushare_player/controllers/SimController.dart';
import 'package:simsushare_player/pages/Preview.dart';

class Home extends StatelessWidget {
  Home({Key? key}) : super(key: key);

  final dio = Dio();

  final error = "".obs;
  final ready = false.obs;

  initialize() async {
    final archiveUrl = Uri.base.queryParameters['url'];
    if (archiveUrl == null) {
      error.value = "No archive URL provided";
      return;
    }
    // Load the archive from the URL
    final response = await dio.get(archiveUrl, options: Options(responseType: ResponseType.bytes));
    if (response.statusCode != 200) {
      error.value = "Failed to load archive: ${response.statusCode}";
      return;
    }
    // Load the archive into the renderer
    final archive = response.data as Uint8List;
    // print("Archive data type: ${archive.codeUnits.runtimeType}"); // should be List<int>
    // final archiveData = ZipDecoder().decodeBytes(archive.codeUnits);
    // final archiveData = ZipDecoder().decodeBytes(const Utf8Encoder().convert(archive).toList());
    final archiveData = ZipDecoder().decodeBytes(archive);
    for (var file in archiveData.files) {
      print("Archive File: ${file.name}");
    }
    final sim = await parseWebSimDef(archiveData);
    final simController = Get.find<SimController>();
    simController.currentSim.value = sim;
    simController.currentLocation.value = 0;
    simController.currentState.value = 0;
    await Future.delayed(const Duration(seconds: 1), () {
      ready.value = true;
    });
  }

  @override
  Widget build(BuildContext context) {
    initialize();
    return Scaffold(
      body: Center(
        child: Obx(() {
          if (error.value.isNotEmpty) {
            return Text(error.value);
          } else {
            if (ready.value) {
              return Preview();
            }
            return const CircularProgressIndicator(color: Colors.amber);
          }
        }),
      ),
    );
  }
}
