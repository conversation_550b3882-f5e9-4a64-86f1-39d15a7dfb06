# SUS Renderer

New Flutter Renderer project.

## Getting Started

This project is intended to be used as the new renderer for SUS.

## Gotchas

- `images` & `svg` folders need to be copied to the `assets` folder from the root (editor) project.
- SimPlayerWeb is _not_ 100% equivelant to the regular SimPlayer. Get.size (and Get.width & Get.height) break in SimPlayerWeb. Use the flutter provided `size` instead.
- Sprites, Containers, etc. are loaded from the cloud not locally available
- There are 2 instances of dioWebAssets, one in `simsushare/lib/component/SimWebPlayer.dart` and the other in `renderer/lib/utils/web_parser.dart`. Both are used to load assets from the cloud and both should be exactly the same. If you need to change the dioAssets, make sure to change it in both places.
- Building web version creates a huge directory folder under `packages/simsushare_player` which in turns contains all assets from the parent. We don't need that actually because we are loading the assets from the S3 server. So, we can safely delete that folder after building the web version or ignore it when uploading to the renderer server/bucket.
- During deployment of the web version locally on CTC, you will have to update the base attribute to have an href="/flutter-player/" (trailing slash is important).
- debug mode won't work on mobile browsers. You will have to use the release mode to test on mobile browsers.
- this line `<meta flt-viewport="" name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">` in `index.html` is important for mobile browsers. It will prevent the user from zooming in/out but it also prevents the flutter renderer from canvas and shifting all data to the bottom.
