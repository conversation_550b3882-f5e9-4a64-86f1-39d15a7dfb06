{"frames": {"flashover_smoke_cycle_7_0095_large": {"frame": {"x": 0, "y": 0, "w": 480, "h": 480}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 480, "h": 480}, "sourceSize": {"w": 480, "h": 480}}, "flashover_smoke_cycle_7_0096_large": {"frame": {"x": 480, "y": 0, "w": 480, "h": 480}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 480, "h": 480}, "sourceSize": {"w": 480, "h": 480}}, "flashover_smoke_cycle_7_0097_large": {"frame": {"x": 960, "y": 0, "w": 480, "h": 480}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 480, "h": 480}, "sourceSize": {"w": 480, "h": 480}}, "flashover_smoke_cycle_7_0098_large": {"frame": {"x": 1440, "y": 0, "w": 480, "h": 480}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 480, "h": 480}, "sourceSize": {"w": 480, "h": 480}}, "flashover_smoke_cycle_7_0099_large": {"frame": {"x": 1920, "y": 0, "w": 480, "h": 480}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 480, "h": 480}, "sourceSize": {"w": 480, "h": 480}}, "flashover_smoke_cycle_7_0100_large": {"frame": {"x": 2400, "y": 0, "w": 480, "h": 480}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 480, "h": 480}, "sourceSize": {"w": 480, "h": 480}}, "flashover_smoke_cycle_7_0101_large": {"frame": {"x": 2880, "y": 0, "w": 480, "h": 480}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 480, "h": 480}, "sourceSize": {"w": 480, "h": 480}}, "flashover_smoke_cycle_7_0102_large": {"frame": {"x": 3360, "y": 0, "w": 480, "h": 480}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 480, "h": 480}, "sourceSize": {"w": 480, "h": 480}}, "flashover_smoke_cycle_7_0103_large": {"frame": {"x": 3840, "y": 0, "w": 480, "h": 480}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 480, "h": 480}, "sourceSize": {"w": 480, "h": 480}}, "flashover_smoke_cycle_7_0104_large": {"frame": {"x": 4320, "y": 0, "w": 480, "h": 480}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 480, "h": 480}, "sourceSize": {"w": 480, "h": 480}}, "flashover_smoke_cycle_7_0105_large": {"frame": {"x": 4800, "y": 0, "w": 480, "h": 480}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 480, "h": 480}, "sourceSize": {"w": 480, "h": 480}}, "flashover_smoke_cycle_7_0106_large": {"frame": {"x": 5280, "y": 0, "w": 480, "h": 480}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 480, "h": 480}, "sourceSize": {"w": 480, "h": 480}}, "flashover_smoke_cycle_7_0107_large": {"frame": {"x": 5760, "y": 0, "w": 480, "h": 480}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 480, "h": 480}, "sourceSize": {"w": 480, "h": 480}}, "flashover_smoke_cycle_7_0108_large": {"frame": {"x": 6240, "y": 0, "w": 480, "h": 480}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 480, "h": 480}, "sourceSize": {"w": 480, "h": 480}}, "flashover_smoke_cycle_7_0109_large": {"frame": {"x": 6720, "y": 0, "w": 480, "h": 480}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 480, "h": 480}, "sourceSize": {"w": 480, "h": 480}}, "flashover_smoke_cycle_7_0110_large": {"frame": {"x": 7200, "y": 0, "w": 480, "h": 480}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 480, "h": 480}, "sourceSize": {"w": 480, "h": 480}}, "flashover_smoke_cycle_7_0111_large": {"frame": {"x": 7680, "y": 0, "w": 480, "h": 480}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 480, "h": 480}, "sourceSize": {"w": 480, "h": 480}}, "flashover_smoke_cycle_7_0112_large": {"frame": {"x": 8160, "y": 0, "w": 480, "h": 480}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 480, "h": 480}, "sourceSize": {"w": 480, "h": 480}}, "flashover_smoke_cycle_7_0113_large": {"frame": {"x": 8640, "y": 0, "w": 480, "h": 480}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 480, "h": 480}, "sourceSize": {"w": 480, "h": 480}}, "flashover_smoke_cycle_7_0114_large": {"frame": {"x": 9120, "y": 0, "w": 480, "h": 480}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 480, "h": 480}, "sourceSize": {"w": 480, "h": 480}}, "flashover_smoke_cycle_7_0115_large": {"frame": {"x": 9600, "y": 0, "w": 480, "h": 480}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 480, "h": 480}, "sourceSize": {"w": 480, "h": 480}}, "flashover_smoke_cycle_7_0116_large": {"frame": {"x": 10080, "y": 0, "w": 480, "h": 480}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 480, "h": 480}, "sourceSize": {"w": 480, "h": 480}}, "flashover_smoke_cycle_7_0117_large": {"frame": {"x": 10560, "y": 0, "w": 480, "h": 480}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 480, "h": 480}, "sourceSize": {"w": 480, "h": 480}}, "flashover_smoke_cycle_7_0118_large": {"frame": {"x": 11040, "y": 0, "w": 480, "h": 480}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 480, "h": 480}, "sourceSize": {"w": 480, "h": 480}}, "flashover_smoke_cycle_7_0119_large": {"frame": {"x": 11520, "y": 0, "w": 480, "h": 480}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 480, "h": 480}, "sourceSize": {"w": 480, "h": 480}}, "flashover_smoke_cycle_7_0120_large": {"frame": {"x": 12000, "y": 0, "w": 480, "h": 480}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 480, "h": 480}, "sourceSize": {"w": 480, "h": 480}}, "flashover_smoke_cycle_7_0121_large": {"frame": {"x": 12480, "y": 0, "w": 480, "h": 480}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 480, "h": 480}, "sourceSize": {"w": 480, "h": 480}}, "flashover_smoke_cycle_7_0122_large": {"frame": {"x": 12960, "y": 0, "w": 480, "h": 480}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 480, "h": 480}, "sourceSize": {"w": 480, "h": 480}}, "flashover_smoke_cycle_7_0123_large": {"frame": {"x": 13440, "y": 0, "w": 480, "h": 480}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 480, "h": 480}, "sourceSize": {"w": 480, "h": 480}}, "flashover_smoke_cycle_7_0124_large": {"frame": {"x": 13920, "y": 0, "w": 480, "h": 480}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 480, "h": 480}, "sourceSize": {"w": 480, "h": 480}}}, "meta": {"app": "https://www.codeandweb.com/texturepacker", "version": "1.0", "image": "Output.png", "format": "RGBA8888", "size": {"w": 14400, "h": 480}, "scale": "1", "smartupdate": "$TexturePacker:SmartUpdate:6f5c79c1651a4c4c4de309beb70ef7d7:52dd321edf7df27c187930136c2da80e:ab242ca87e7871abc98cd1fb098a82c9$"}}