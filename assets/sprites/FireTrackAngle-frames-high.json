{"frames": {"3": {"frame": {"x": 0, "y": 0, "w": 692, "h": 503}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 692, "h": 503}, "sourceSize": {"w": 692, "h": 503}}, "5": {"frame": {"x": 0, "y": 503, "w": 692, "h": 503}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 692, "h": 503}, "sourceSize": {"w": 692, "h": 503}}, "7": {"frame": {"x": 0, "y": 1006, "w": 692, "h": 503}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 692, "h": 503}, "sourceSize": {"w": 692, "h": 503}}, "9": {"frame": {"x": 0, "y": 1509, "w": 692, "h": 503}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 692, "h": 503}, "sourceSize": {"w": 692, "h": 503}}, "11": {"frame": {"x": 0, "y": 2012, "w": 692, "h": 503}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 692, "h": 503}, "sourceSize": {"w": 692, "h": 503}}, "13": {"frame": {"x": 0, "y": 2515, "w": 692, "h": 503}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 692, "h": 503}, "sourceSize": {"w": 692, "h": 503}}, "15": {"frame": {"x": 0, "y": 3018, "w": 692, "h": 503}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 692, "h": 503}, "sourceSize": {"w": 692, "h": 503}}, "17": {"frame": {"x": 0, "y": 3521, "w": 692, "h": 503}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 692, "h": 503}, "sourceSize": {"w": 692, "h": 503}}, "19": {"frame": {"x": 0, "y": 4024, "w": 692, "h": 503}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 692, "h": 503}, "sourceSize": {"w": 692, "h": 503}}, "21": {"frame": {"x": 0, "y": 4527, "w": 692, "h": 503}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 692, "h": 503}, "sourceSize": {"w": 692, "h": 503}}, "23": {"frame": {"x": 0, "y": 5030, "w": 692, "h": 503}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 692, "h": 503}, "sourceSize": {"w": 692, "h": 503}}, "25": {"frame": {"x": 0, "y": 5533, "w": 692, "h": 503}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 692, "h": 503}, "sourceSize": {"w": 692, "h": 503}}, "27": {"frame": {"x": 0, "y": 6036, "w": 692, "h": 503}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 692, "h": 503}, "sourceSize": {"w": 692, "h": 503}}, "29": {"frame": {"x": 0, "y": 6539, "w": 692, "h": 503}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 692, "h": 503}, "sourceSize": {"w": 692, "h": 503}}, "31": {"frame": {"x": 0, "y": 7042, "w": 692, "h": 503}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 692, "h": 503}, "sourceSize": {"w": 692, "h": 503}}, "33": {"frame": {"x": 0, "y": 7545, "w": 692, "h": 503}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 692, "h": 503}, "sourceSize": {"w": 692, "h": 503}}, "35": {"frame": {"x": 0, "y": 8048, "w": 692, "h": 503}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 692, "h": 503}, "sourceSize": {"w": 692, "h": 503}}, "37": {"frame": {"x": 0, "y": 8551, "w": 692, "h": 503}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 692, "h": 503}, "sourceSize": {"w": 692, "h": 503}}, "39": {"frame": {"x": 0, "y": 9054, "w": 692, "h": 503}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 692, "h": 503}, "sourceSize": {"w": 692, "h": 503}}, "41": {"frame": {"x": 0, "y": 9557, "w": 692, "h": 503}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 692, "h": 503}, "sourceSize": {"w": 692, "h": 503}}, "43": {"frame": {"x": 0, "y": 10060, "w": 692, "h": 503}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 692, "h": 503}, "sourceSize": {"w": 692, "h": 503}}, "45": {"frame": {"x": 0, "y": 10563, "w": 692, "h": 503}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 692, "h": 503}, "sourceSize": {"w": 692, "h": 503}}, "47": {"frame": {"x": 0, "y": 11066, "w": 692, "h": 503}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 692, "h": 503}, "sourceSize": {"w": 692, "h": 503}}, "49": {"frame": {"x": 0, "y": 11569, "w": 692, "h": 503}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 692, "h": 503}, "sourceSize": {"w": 692, "h": 503}}, "51": {"frame": {"x": 0, "y": 12072, "w": 692, "h": 503}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 692, "h": 503}, "sourceSize": {"w": 692, "h": 503}}, "53": {"frame": {"x": 0, "y": 12575, "w": 692, "h": 503}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 692, "h": 503}, "sourceSize": {"w": 692, "h": 503}}, "55": {"frame": {"x": 0, "y": 13078, "w": 692, "h": 503}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 692, "h": 503}, "sourceSize": {"w": 692, "h": 503}}, "57": {"frame": {"x": 0, "y": 13581, "w": 692, "h": 503}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 692, "h": 503}, "sourceSize": {"w": 692, "h": 503}}, "59": {"frame": {"x": 0, "y": 14084, "w": 692, "h": 503}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 692, "h": 503}, "sourceSize": {"w": 692, "h": 503}}, "61": {"frame": {"x": 0, "y": 14587, "w": 692, "h": 503}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 692, "h": 503}, "sourceSize": {"w": 692, "h": 503}}}, "meta": {"app": "https://www.codeandweb.com/texturepacker", "version": "1.0", "image": "Output.png", "format": "RGBA8888", "size": {"w": 692, "h": 15090}, "scale": "1", "smartupdate": "$TexturePacker:SmartUpdate:698a2994307dcaaca581d59fe6a3c1df:53b6b43639d91c122b35b3f65fe481d0:ab242ca87e7871abc98cd1fb098a82c9$"}}