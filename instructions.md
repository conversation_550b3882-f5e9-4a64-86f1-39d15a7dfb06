# SimDef XML Generation Instructions for LLM

## Overview
You are tasked with generating SimDef XML files for simulation scenarios based on client requests. These XML files define interactive simulations with locations, sprites, objects, and navigation elements.

## Core Structure
Every SimDef XML follows this hierarchical structure:

```xml
<sim id="SCENARIO_ID" title="SCENARIO_NAME" width="WIDTH" height="HEIGHT" backgroundColor="0x222222" NavClusterXPct="X_PERCENT" NavClusterYPct="Y_PERCENT">
  <header>...</header>
  <varTable>...</varTable>
  <environ>...</environ>
  <environstates>...</environstates>
  <simFrames>...</simFrames>
  <plugins>...</plugins>
  <triggeractions>...</triggeractions>
</sim>
```

## Required Elements

### 1. Header Section
Always include these elements (can be empty):
```xml
<header>
  <summary></summary>
  <description></description>
  <attachments></attachments>
  <keywords></keywords>
  <categories id="CATEGORY_ID"></categories>
  <history></history>
</header>
```

### 2. Variable Table
Define simulation state variables:
```xml
<varTable>
  <variable id="CURRENT_SIM_STATE" type="string" default="STATE_ID" desc="the current sim state"/>
  <variable id="CURRENT_LOCATION" type="string" default="LOCATION_ID" desc="the current sim location"/>
</varTable>
```

### 3. Environment Structure
```xml
<environ>
  <locations>
    <!-- Location elements go here -->
  </locations>
  <navigations>
    <!-- Navigation elements go here -->
  </navigations>
</environ>
```

### 4. States Definition
```xml
<environstates>
  <states>
    <state id="STATE_ID" name="STATE_NAME"/>
    <!-- Add more states as needed -->
  </states>
</environstates>
```

## Location Elements

### Background Image
Every location should have a background:
```xml
<element type="CSPic" id="background" file="BK0.png" background="true" x="CENTER_X" y="CENTER_Y" scaleX="1.0" scaleY="1.0" offset="0x0"/>
```

### Sprites
For each sprite from the asset library:
```xml
<element type="SPRITE_TYPE" id="UNIQUE_ID" name="SPRITE_NAME" x="X_POSITION" y="Y_POSITION" scale="1.0" scaleX="1.0" scaleY="1.0" rotation="0.0" priority="1"/>
```

### Images/Pictures
```xml
<element type="CSPic" id="UNIQUE_ID" x="X_POSITION" y="Y_POSITION" file="FILENAME" scaleX="1.0" scaleY="1.0" rotation="0.0" priority="1"/>
```

### Text Elements
```xml
<element type="CSText" id="UNIQUE_ID" x="X_POSITION" y="Y_POSITION" scaleX="1.0" scaleY="1.0" rotation="0.0" priority="1" text="TEXT_CONTENT" color="COLOR_VALUE"/>
```

### Shapes
```xml
<element type="CSShape" id="UNIQUE_ID" x="X_POSITION" y="Y_POSITION" scaleX="1.0" scaleY="1.0" rotation="0.0" priority="1" shape="SHAPE_TYPE"/>
```

### Navigation Jumpers
```xml
<element type="LocJumper" id="UNIQUE_ID" x="X_POSITION" y="Y_POSITION" scaleY="1.0"/>
```

### People/Victims
```xml
<element type="VictimTYPE" id="UNIQUE_ID" name="PERSON_NAME" x="X_POSITION" y="Y_POSITION" scaleX="1.0" scaleY="1.0" rotation="0.0" priority="1" hideOnStart="false"/>
```

### Containers
```xml
<element type="CONTAINER_TYPE" id="UNIQUE_ID" x="X_POSITION" y="Y_POSITION" scaleX="1.0" scaleY="1.0" rotation="0.0" priority="1"/>
```

### Audio
```xml
<element type="AudioClip" id="UNIQUE_ID" file="AUDIO_FILE" loop="false" priority="1" x="X_POSITION" y="Y_POSITION" scaleX="1.0" scaleY="1.0" rotation="0.0"/>
```

### Timers
```xml
<element type="CSTimer" id="UNIQUE_ID" x="X_POSITION" y="Y_POSITION" scaleX="1.0" scaleY="1.0" rotation="0.0" priority="1"/>
```

### Masks
```xml
<element type="CSMask" id="UNIQUE_ID" name="MASK_NAME" x="0" y="0">MASK_DEFINITION</element>
```

## SimFrames Section
For each location, create a simFrame with elementVal entries:
```xml
<simFrame states="STATE_ID" locs="LOCATION_ID">
  <elementVal id="ELEMENT_ID">ELEMENT_PARAMETERS</elementVal>
  <!-- Repeat for each element in the location -->
</simFrame>
```

## Element Parameters Format

### Sprite Parameters
Format: `out,0,0,1,COLOR_VALUE,COLOR_OPACITY,OPACITY,FRAMERATE,FADE_IN_WHEN,FADE_IN_DURATION,MIRROR_X,MIRROR_Y,WIDTH_SCALE,FADE_OUT,FADE_OUT_WHEN,FADE_OUT_DURATION,TIMING_TRIGGER`

### Image Parameters
Format: `out,0,0,1,COLOR_VALUE,COLOR_OPACITY,OPACITY,FADE_IN_WHEN,FADE_IN_DURATION,MOVABLE,MIRROR_X,MIRROR_Y,SCALE,FADE_OUT,FADE_OUT_WHEN,FADE_OUT_DURATION,BLUR,TIMING_TRIGGER,CLICK_TO_TOGGLE,0,0`

### Text Parameters
Format: `ENCODED_TEXT,BACKGROUND_COLOR,FADE_IN_WHEN,FADE_IN_DURATION,FADE_OUT,FADE_OUT_WHEN,FADE_OUT_DURATION,TIMING_TRIGGER`

### Shape Parameters
Format: `out,0,0,1,COLOR_VALUE,COLOR_OPACITY,OPACITY,FADE_IN_WHEN,FADE_IN_DURATION,null,MIRROR_X,MIRROR_Y,WIDTH_SCALE,FADE_OUT,FADE_OUT_WHEN,FADE_OUT_DURATION,BLUR,TIMING_TRIGGER,0,0,0`

### Location Jumper Parameters
Format: `in,TO_LOCATION,SHAPE_NUMBER,DELAY,HAS_DELAY,CLICKABLE,LIGHTNESS,HUE,SATURATION,ALPHA,MIRROR_Y,MIRROR_X,WIDTH_SCALE,FADE_OUT,FADE_OUT_WHEN,FADE_OUT_DURATION,BLUR,TIMING_TRIGGER,HAS_FADE_IN,FADE_IN_WHEN,FADE_IN_DURATION`

## Navigation Elements
```xml
<nav dir="DIRECTION" fromID="FROM_LOCATION" toID="TO_LOCATION"/>
```

## Common Values and Mappings

### Timing Triggers
- `0` = scenario
- `1` = location  
- `2` = state

### Shapes for Location Jumpers
- `0` = arrow-1
- `1` = arrow-2
- `2` = arrow-3
- `3` = arrow-4
- `4` = rectangle
- `5` = square
- `6` = circle
- `7` = triangle
- `8` = rounded-rectangle

### Shape Types
- rectangle
- square
- circle
- triangle
- rounded-rectangle

### Priority Values
Higher priority elements render on top (default: 1)

### Color Format
Colors are represented as integer values (e.g., 16777215 for white, 0 for black)

## Plugins Section
Include plugins for used element types:
```xml
<plugins>
  <plugin lib="CSMask_v1_lib.swf" assetIDs="CSMask"/>
  <plugin lib="LocJumper_v1_lib.swf" assetIDs="LocJumper"/>
  <plugin lib="CSPic_v1_lib.swf" assetIDs="CSPic"/>
  <plugin lib="CSShape_v1_lib.swf" assetIDs="CSShape"/>
  <plugin lib="AudioClip_v1_lib.swf" assetIDs="AudioClip"/>
  <plugin lib="SPRITE_NAME_v1_lib.swf" assetIDs="SPRITE_NAME"/>
</plugins>
```

## Processing Client Requests

### 1. Scenario Setup
- Generate unique IDs for scenario, locations, and states
- Set appropriate width/height (default: screen dimensions)
- Create initial location and state

### 2. Asset Placement
- Use provided asset descriptions to select appropriate sprites/elements
- Position elements based on client requirements
- Set realistic scales and rotations
- Assign appropriate priorities for layering

### 3. Navigation Setup
- Create location jumpers for navigation between locations
- Set up navigation elements in the navigations section
- Ensure all locations are accessible

### 4. Interactive Elements
- Add text elements for instructions or information
- Include timers if time-based scenarios are needed
- Add audio elements for ambient sounds or effects
- Implement masks for showing/hiding elements based on conditions

### 5. State Management
- Define different states for scenario progression
- Link elements to appropriate states using trigger conditions
- Set up fade in/out effects for dynamic content

## Quality Guidelines

1. **Unique IDs**: Every element must have a unique ID within its location
2. **Consistent Positioning**: Use coordinate system where (0,0) is top-left
3. **Reasonable Scales**: Keep scale values between 0.1 and 3.0 for usability
4. **Plugin Dependencies**: Include all necessary plugins for used element types
5. **State Consistency**: Ensure all referenced states exist in environstates
6. **File References**: Use relative file paths for assets
7. **Priority Management**: Higher priority elements appear on top
8. **Parameter Completeness**: Provide all required parameters for each element type

## Error Prevention

- Validate all ID references between elements
- Check that all file references exist in asset library
- Ensure coordinate values are within scenario dimensions
- Verify state and location references are valid
- Include fallback values for optional parameters

## Output Format

Generate clean, properly indented XML with:
- Proper XML declaration
- Consistent attribute ordering
- Appropriate whitespace and indentation
- Complete parameter strings for all elementVal entries
- All required plugins listed

Remember to adapt the scenario complexity based on the client's specific requirements while maintaining the structural integrity of the SimDef format.

---

## Dynamic Output Template

Use this template structure for generating SimDef XML files. Replace placeholders with appropriate values:

```xml
<sim id="{{SCENARIO_ID}}" title="{{SCENARIO_TITLE}}" width="{{SCENARIO_WIDTH}}" height="{{SCENARIO_HEIGHT}}" backgroundColor="0x222222" NavClusterXPct="{{NAV_CLUSTER_X}}" NavClusterYPct="{{NAV_CLUSTER_Y}}">
  <header>
    <summary>{{SCENARIO_SUMMARY}}</summary>
    <description>{{SCENARIO_DESCRIPTION}}</description>
    <attachments></attachments>
    <keywords>{{SCENARIO_KEYWORDS}}</keywords>
    <categories id="{{CATEGORY_ID}}"></categories>
    <history></history>
  </header>
  <varTable>
    <variable id="CURRENT_SIM_STATE" type="string" default="{{INITIAL_STATE_ID}}" desc="the current sim state"/>
    <variable id="CURRENT_LOCATION" type="string" default="{{INITIAL_LOCATION_ID}}" desc="the current sim location"/>
  </varTable>
  <environ>
    <locations>
      {{#LOCATIONS}}
      <location id="{{LOCATION_ID}}" name="{{LOCATION_NAME}}" brightness="{{LOCATION_BRIGHTNESS}}" color="{{LOCATION_COLOR}}">
        <element type="CSPic" id="background" file="{{BACKGROUND_FILE}}" background="true" x="{{CENTER_X}}" y="{{CENTER_Y}}" scaleX="{{BG_SCALE_X}}" scaleY="{{BG_SCALE_Y}}" offset="{{BG_OFFSET}}"/>
        {{#SPRITES}}
        <element type="{{SPRITE_TYPE}}" id="{{SPRITE_ID}}" name="{{SPRITE_NAME}}" x="{{SPRITE_X}}" y="{{SPRITE_Y}}" scale="{{SPRITE_SCALE}}" scaleX="{{SPRITE_SCALE_X}}" scaleY="{{SPRITE_SCALE_Y}}" rotation="{{SPRITE_ROTATION}}" priority="{{SPRITE_PRIORITY}}"{{#MOVABLE}} movable="true"{{/MOVABLE}}{{#TRIGGER_ONCE}} triggerOnce="true"{{/TRIGGER_ONCE}}/>
        {{/SPRITES}}
        {{#IMAGES}}
        <element type="CSPic" id="{{IMAGE_ID}}" x="{{IMAGE_X}}" y="{{IMAGE_Y}}" file="{{IMAGE_FILE}}" scaleX="{{IMAGE_SCALE_X}}" scaleY="{{IMAGE_SCALE_Y}}" rotation="{{IMAGE_ROTATION}}" priority="{{IMAGE_PRIORITY}}"{{#IMAGE_TO}} to="{{IMAGE_TO}}"{{/IMAGE_TO}}{{#SYNC_VAR}} syncVar="{{SYNC_VAR}}"{{/SYNC_VAR}}/>
        {{/IMAGES}}
        {{#TEXTS}}
        <element type="CSText" id="{{TEXT_ID}}" x="{{TEXT_X}}" y="{{TEXT_Y}}" scaleX="{{TEXT_SCALE_X}}" scaleY="{{TEXT_SCALE_Y}}" rotation="{{TEXT_ROTATION}}" priority="{{TEXT_PRIORITY}}" text="{{TEXT_CONTENT}}" color="{{TEXT_COLOR}}"{{#TRIGGER_ONCE}} triggerOnce="true"{{/TRIGGER_ONCE}}/>
        {{/TEXTS}}
        {{#SHAPES}}
        <element type="CSShape" id="{{SHAPE_ID}}" x="{{SHAPE_X}}" y="{{SHAPE_Y}}" scaleX="{{SHAPE_SCALE_X}}" scaleY="{{SHAPE_SCALE_Y}}" rotation="{{SHAPE_ROTATION}}" priority="{{SHAPE_PRIORITY}}" shape="{{SHAPE_TYPE}}"{{#TRIGGER_ONCE}} triggerOnce="true"{{/TRIGGER_ONCE}}/>
        {{/SHAPES}}
        {{#JUMPERS}}
        <element type="LocJumper" id="{{JUMPER_ID}}" x="{{JUMPER_X}}" y="{{JUMPER_Y}}" scaleY="{{JUMPER_SCALE_Y}}"{{#TRIGGER_ONCE}} triggerOnce="true"{{/TRIGGER_ONCE}}{{#JUMPER_NAME}} name="{{JUMPER_NAME}}"{{/JUMPER_NAME}}/>
        {{/JUMPERS}}
        {{#PEOPLE}}
        <element type="Victim{{PERSON_TYPE}}" id="{{PERSON_ID}}" name="{{PERSON_NAME}}" x="{{PERSON_X}}" y="{{PERSON_Y}}" scaleX="{{PERSON_SCALE_X}}" scaleY="{{PERSON_SCALE_Y}}" rotation="{{PERSON_ROTATION}}" priority="{{PERSON_PRIORITY}}" hideOnStart="{{PERSON_HIDE_START}}"{{#MOVABLE}} movable="true"{{/MOVABLE}}{{#TRIGGER_ONCE}} triggerOnce="true"{{/TRIGGER_ONCE}}/>
        {{/PEOPLE}}
        {{#CONTAINERS}}
        <element type="{{CONTAINER_TYPE}}" id="{{CONTAINER_ID}}" x="{{CONTAINER_X}}" y="{{CONTAINER_Y}}" scaleX="{{CONTAINER_SCALE_X}}" scaleY="{{CONTAINER_SCALE_Y}}" rotation="{{CONTAINER_ROTATION}}" priority="{{CONTAINER_PRIORITY}}"{{#MOVABLE}} movable="true"{{/MOVABLE}}{{#TRIGGER_ONCE}} triggerOnce="true"{{/TRIGGER_ONCE}}/>
        {{/CONTAINERS}}
        {{#LABELS}}
        <element type="Label{{LABEL_TYPE}}" id="{{LABEL_ID}}" x="{{LABEL_X}}" y="{{LABEL_Y}}" scaleX="{{LABEL_SCALE_X}}" scaleY="{{LABEL_SCALE_Y}}" rotation="{{LABEL_ROTATION}}"{{#LABEL_COLOR}} color="{{LABEL_COLOR}}"{{/LABEL_COLOR}} priority="{{LABEL_PRIORITY}}"{{#TRIGGER_ONCE}} triggerOnce="true"{{/TRIGGER_ONCE}}/>
        {{/LABELS}}
        {{#TIMERS}}
        <element type="CSTimer" id="{{TIMER_ID}}" x="{{TIMER_X}}" y="{{TIMER_Y}}" scaleX="{{TIMER_SCALE_X}}" scaleY="{{TIMER_SCALE_Y}}" rotation="{{TIMER_ROTATION}}" priority="{{TIMER_PRIORITY}}"{{#TRIGGER_ONCE}} triggerOnce="true"{{/TRIGGER_ONCE}}/>
        {{/TIMERS}}
        {{#SOUNDS}}
        <element type="AudioClip" id="{{SOUND_ID}}" file="{{SOUND_FILE}}" loop="{{SOUND_LOOP}}" priority="{{SOUND_PRIORITY}}" x="{{SOUND_X}}" y="{{SOUND_Y}}" scaleX="{{SOUND_SCALE_X}}" scaleY="{{SOUND_SCALE_Y}}" rotation="{{SOUND_ROTATION}}"{{#TRIGGER_ONCE}} triggerOnce="true"{{/TRIGGER_ONCE}}/>
        {{/SOUNDS}}
        {{#MASKS}}
        <element type="CSMask" id="{{MASK_ID}}" name="{{MASK_NAME}}" x="0" y="0">{{MASK_DEFINITION}}</element>
        {{/MASKS}}
      </location>
      {{/LOCATIONS}}
    </locations>
    <navigations>
      {{#NAVIGATIONS}}
      <nav dir="{{NAV_DIRECTION}}" fromID="{{NAV_FROM}}" toID="{{NAV_TO}}"/>
      {{/NAVIGATIONS}}
    </navigations>
  </environ>
  <environstates>
    <states>
      {{#STATES}}
      <state id="{{STATE_ID}}" name="{{STATE_NAME}}"/>
      {{/STATES}}
    </states>
  </environstates>
  <simFrames>
    {{#LOCATIONS}}
    <simFrame states="{{LOCATION_STATE}}" locs="{{LOCATION_ID}}">
      {{#ELEMENT_VALS}}
      <elementVal id="{{ELEMENT_ID}}">{{ELEMENT_PARAMETERS}}</elementVal>
      {{/ELEMENT_VALS}}
    </simFrame>
    {{/LOCATIONS}}
  </simFrames>
  <plugins>
    {{#PLUGINS}}
    <plugin lib="{{PLUGIN_LIB}}" assetIDs="{{PLUGIN_ASSET_IDS}}"/>
    {{/PLUGINS}}
  </plugins>
  <triggeractions>
    <actions></actions>
    <triggers></triggers>
    <mappings></mappings>
  </triggeractions>
</sim>
```

## Placeholder Definitions

### Scenario Level
- `{{SCENARIO_ID}}` - Unique identifier for the scenario
- `{{SCENARIO_TITLE}}` - Human-readable title
- `{{SCENARIO_WIDTH}}` - Canvas width in pixels
- `{{SCENARIO_HEIGHT}}` - Canvas height in pixels
- `{{SCENARIO_SUMMARY}}` - Brief description
- `{{SCENARIO_DESCRIPTION}}` - Detailed description
- `{{SCENARIO_KEYWORDS}}` - Comma-separated keywords
- `{{CATEGORY_ID}}` - Category identifier
- `{{NAV_CLUSTER_X}}` - Navigation cluster X percentage (0-1)
- `{{NAV_CLUSTER_Y}}` - Navigation cluster Y percentage (0-1)
- `{{INITIAL_STATE_ID}}` - Default state ID
- `{{INITIAL_LOCATION_ID}}` - Default location ID

### Location Level
- `{{LOCATION_ID}}` - Unique location identifier
- `{{LOCATION_NAME}}` - Location display name
- `{{LOCATION_BRIGHTNESS}}` - Background brightness (0-1)
- `{{LOCATION_COLOR}}` - Location color hex code
- `{{LOCATION_STATE}}` - Associated state ID
- `{{BACKGROUND_FILE}}` - Background image filename
- `{{CENTER_X}}` - Center X coordinate
- `{{CENTER_Y}}` - Center Y coordinate
- `{{BG_SCALE_X}}` - Background X scale
- `{{BG_SCALE_Y}}` - Background Y scale
- `{{BG_OFFSET}}` - Background offset (format: "XxY")

### Element Level (Common)
- `{{ELEMENT_ID}}` - Unique element identifier
- `{{ELEMENT_X}}` - X position coordinate
- `{{ELEMENT_Y}}` - Y position coordinate
- `{{ELEMENT_SCALE_X}}` - X scale factor
- `{{ELEMENT_SCALE_Y}}` - Y scale factor
- `{{ELEMENT_ROTATION}}` - Rotation in degrees
- `{{ELEMENT_PRIORITY}}` - Render priority (higher = front)
- `{{ELEMENT_PARAMETERS}}` - Comma-separated parameter string

### Sprite Specific
- `{{SPRITE_TYPE}}` - Asset name from sprite library
- `{{SPRITE_NAME}}` - Display name
- `{{SPRITE_SCALE}}` - Uniform scale factor

### Image Specific
- `{{IMAGE_FILE}}` - Image filename
- `{{IMAGE_TO}}` - Target location for navigation
- `{{SYNC_VAR}}` - Synchronization variable

### Text Specific
- `{{TEXT_CONTENT}}` - Text content to display
- `{{TEXT_COLOR}}` - Text color integer value

### Shape Specific
- `{{SHAPE_TYPE}}` - Shape type (rectangle, circle, etc.)

### Navigation Specific
- `{{NAV_DIRECTION}}` - Navigation direction
- `{{NAV_FROM}}` - Source location ID
- `{{NAV_TO}}` - Target location ID

### Plugin Specific
- `{{PLUGIN_LIB}}` - Plugin library filename
- `{{PLUGIN_ASSET_IDS}}` - Associated asset identifiers

## Conditional Placeholders

Use these for optional attributes:
- `{{#MOVABLE}}...{{/MOVABLE}}` - Include if element is movable
- `{{#TRIGGER_ONCE}}...{{/TRIGGER_ONCE}}` - Include if triggers only once
- `{{#IMAGE_TO}}...{{/IMAGE_TO}}` - Include if image has navigation target
- `{{#SYNC_VAR}}...{{/SYNC_VAR}}` - Include if has sync variable
- `{{#JUMPER_NAME}}...{{/JUMPER_NAME}}` - Include if jumper has name
- `{{#LABEL_COLOR}}...{{/LABEL_COLOR}}` - Include if label has color

## Array Placeholders

Use these for repeating sections:
- `{{#LOCATIONS}}...{{/LOCATIONS}}` - Loop through all locations
- `{{#SPRITES}}...{{/SPRITES}}` - Loop through sprites in location
- `{{#IMAGES}}...{{/IMAGES}}` - Loop through images in location
- `{{#TEXTS}}...{{/TEXTS}}` - Loop through texts in location
- `{{#SHAPES}}...{{/SHAPES}}` - Loop through shapes in location
- `{{#JUMPERS}}...{{/JUMPERS}}` - Loop through jumpers in location
- `{{#PEOPLE}}...{{/PEOPLE}}` - Loop through people in location
- `{{#CONTAINERS}}...{{/CONTAINERS}}` - Loop through containers in location
- `{{#LABELS}}...{{/LABELS}}` - Loop through labels in location
- `{{#TIMERS}}...{{/TIMERS}}` - Loop through timers in location
- `{{#SOUNDS}}...{{/SOUNDS}}` - Loop through sounds in location
- `{{#MASKS}}...{{/MASKS}}` - Loop through masks in location
- `{{#NAVIGATIONS}}...{{/NAVIGATIONS}}` - Loop through navigation elements
- `{{#STATES}}...{{/STATES}}` - Loop through all states
- `{{#ELEMENT_VALS}}...{{/ELEMENT_VALS}}` - Loop through element values
- `{{#PLUGINS}}...{{/PLUGINS}}` - Loop through required plugins 