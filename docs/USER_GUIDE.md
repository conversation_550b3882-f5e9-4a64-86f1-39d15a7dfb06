# SimShare Player Parser - User Guide

## Table of Contents

1. [Getting Started](#getting-started)
2. [Basic Usage](#basic-usage)
3. [Advanced Features](#advanced-features)
4. [Performance Optimization](#performance-optimization)
5. [Error Handling](#error-handling)
6. [Best Practices](#best-practices)
7. [Troubleshooting](#troubleshooting)

## Getting Started

### Prerequisites

- Flutter 3.0 or higher
- Dart 3.0 or higher
- Access to simulation asset files

### Installation

The parser is included as part of the SimShare Player package. No additional installation is required.

### Quick Start

```dart
import 'package:simsushare_player/utils/parsers.dart';
import 'package:simsushare_player/utils/element_parsers/element_parser_factory.dart';

// Initialize the parser system
ElementParserFactory.initialize();

// Parse a simulation
final xmlContent = await File('simulation.xml').readAsString();
final assetDirectory = Directory('assets/simulation');
final scenario = await parseSimDef(xmlContent, assetDirectory);

print('Parsed simulation: ${scenario.name}');
print('Locations: ${scenario.locations.length}');
```

## Basic Usage

### Parsing Simulations

#### 1. Parse SimDef XML to Scenario

```dart
try {
  final scenario = await parseSimDef(xmlContent, assetDirectory);
  
  // Access parsed data
  print('Simulation ID: ${scenario.id}');
  print('Dimensions: ${scenario.width}x${scenario.height}');
  print('Locations: ${scenario.locations.length}');
  print('States: ${scenario.states.length}');
  
} catch (e) {
  print('Parsing failed: $e');
}
```

#### 2. Generate XML from Scenario

```dart
try {
  final xmlString = generateSimDef(scenario, cleanup: true);
  await File('output.xml').writeAsString(xmlString);
  print('XML generated successfully');
  
} catch (e) {
  print('Generation failed: $e');
}
```

### Working with Elements

#### Parse Individual Masks

```dart
// Parse a mask definition
final mask = parseMask(
  'mask1', 
  'showOutside|true|10,20,100,200', 
  'location1'
);

print('Mask type: ${mask.type}');
print('Coordinates: ${mask.coordinates}');
```

#### Extract Sprite IDs from Masks

```dart
final spriteIds = parseMaskSprites('sprite1,sprite2,sprite3');
print('Found sprites: $spriteIds');
```

## Advanced Features

### Performance Monitoring

#### Enable Performance Tracking

```dart
import 'package:simsushare_player/utils/performance/performance_monitor.dart';

final monitor = PerformanceMonitor();

// Time an operation
final result = await monitor.timeAsyncOperation('parse_simulation', () async {
  return await parseSimDef(xmlContent, assetDirectory);
});

// Get statistics
final stats = monitor.getStats('parse_simulation');
print('Average time: ${stats?.averageTimeMs}ms');
```

#### Monitor Parser Performance

```dart
// Get parser performance statistics
final stats = ElementParserFactory.getPerformanceStats();

print('Total parsed elements: ${stats['totalParsedElements']}');
print('Most used parser: ${stats['mostUsedParser']}');
print('Cache hit rate: ${stats['cacheStats']['hitRate']}');
```

### Asset Caching

#### Preload Common Assets

```dart
import 'package:simsushare_player/utils/performance/asset_cache.dart';

final cache = AssetCache();

// Preload frequently used assets
await cache.preloadCommonAssets(['sprite1', 'sprite2', 'background1']);

// Check cache statistics
final stats = cache.statistics;
print('Cache hit rate: ${stats['hitRate']}');
print('Memory usage: ${stats['memoryUsage']} bytes');
```

#### Smart Preloading

```dart
// Preload based on usage patterns
await ElementParserFactory.preloadCommonAssets();
```

### Lazy Loading

#### Priority-Based Loading

```dart
import 'package:simsushare_player/utils/performance/lazy_loader.dart';

final loader = LazyLoader();

// Load with high priority
final spriteData = await loader.loadSpriteData(
  'important_sprite',
  priority: LoadPriority.high
);

// Load in background
await loader.loadSpriteData(
  'background_sprite',
  priority: LoadPriority.background
);
```

## Performance Optimization

### Best Practices for Performance

#### 1. Initialize Once

```dart
// Initialize at app startup
ElementParserFactory.initialize();

// Don't reinitialize repeatedly
```

#### 2. Preload Common Assets

```dart
// Identify frequently used assets
final commonAssets = ['ui_sprite', 'character_sprite', 'background'];

// Preload them
await cache.preloadCommonAssets(commonAssets);
```

#### 3. Monitor Performance

```dart
// Regular performance monitoring
final stats = ElementParserFactory.getPerformanceStats();

if (stats['cacheStats']['hitRate'] < 0.8) {
  print('Warning: Low cache hit rate');
  // Consider preloading more assets
}
```

#### 4. Batch Operations

```dart
// Process multiple simulations efficiently
final futures = xmlFiles.map((file) async {
  final content = await file.readAsString();
  return await parseSimDef(content, assetDirectory);
});

final scenarios = await Future.wait(futures);
```

### Memory Management

#### Monitor Memory Usage

```dart
final memoryUsage = cache.statistics['memoryUsage'];
if (memoryUsage > 100 * 1024 * 1024) { // 100MB
  cache.clearAll(); // Clear cache if too much memory used
}
```

#### Periodic Cleanup

```dart
// Clear performance data periodically
ElementParserFactory.clearPerformanceStats();

// Clear cache when appropriate
cache.clearAll();
```

## Error Handling

### Common Error Types

#### 1. Invalid XML Format

```dart
try {
  final scenario = await parseSimDef(invalidXml, assetDirectory);
} catch (e) {
  if (e is ArgumentError && e.message.contains('XML')) {
    print('Invalid XML format: ${e.message}');
    // Handle XML parsing error
  }
}
```

#### 2. Missing Asset Files

```dart
try {
  final scenario = await parseSimDef(xmlContent, missingDirectory);
} catch (e) {
  if (e is ArgumentError && e.message.contains('directory')) {
    print('Asset directory not found: ${e.message}');
    // Handle missing directory
  }
}
```

#### 3. Invalid Scenario Data

```dart
try {
  final xml = generateSimDef(invalidScenario);
} catch (e) {
  if (e is ArgumentError) {
    print('Invalid scenario data: ${e.message}');
    // Handle validation error
  }
}
```

### Error Recovery Strategies

#### Graceful Degradation

```dart
Future<Scenario?> parseWithFallback(String xml, Directory dir) async {
  try {
    return await parseSimDef(xml, dir);
  } catch (e) {
    print('Primary parsing failed: $e');
    
    // Try with simplified parsing
    try {
      return await parseSimDefSimplified(xml, dir);
    } catch (e2) {
      print('Fallback parsing also failed: $e2');
      return null;
    }
  }
}
```

#### Partial Success Handling

```dart
// The parser continues processing even if some elements fail
// Check logs for warnings about skipped elements
final scenario = await parseSimDef(xmlWithSomeInvalidElements, dir);

// Scenario will contain successfully parsed elements
print('Successfully parsed ${scenario.locations.length} locations');
```

## Best Practices

### 1. Initialization

```dart
// ✅ Good: Initialize once at app startup
void initializeApp() {
  ElementParserFactory.initialize();
}

// ❌ Bad: Initialize repeatedly
void parseSimulation() {
  ElementParserFactory.initialize(); // Don't do this
  // ... parsing code
}
```

### 2. Error Handling

```dart
// ✅ Good: Comprehensive error handling
try {
  final scenario = await parseSimDef(xml, dir);
  return scenario;
} catch (e) {
  logger.error('Parsing failed', error: e);
  return null;
}

// ❌ Bad: No error handling
final scenario = await parseSimDef(xml, dir); // May crash
```

### 3. Performance Monitoring

```dart
// ✅ Good: Regular monitoring
void checkPerformance() {
  final stats = ElementParserFactory.getPerformanceStats();
  if (stats['cacheStats']['hitRate'] < 0.7) {
    // Take action to improve performance
  }
}

// ❌ Bad: No performance monitoring
// Parse without knowing performance characteristics
```

### 4. Memory Management

```dart
// ✅ Good: Proactive memory management
void manageMemory() {
  final usage = cache.statistics['memoryUsage'];
  if (usage > maxMemoryThreshold) {
    cache.clearAll();
  }
}

// ❌ Bad: No memory management
// Let memory usage grow indefinitely
```

### 5. Asset Management

```dart
// ✅ Good: Preload common assets
await cache.preloadCommonAssets(frequentlyUsedAssets);

// ✅ Good: Use appropriate priorities
await loader.loadSpriteData('critical_sprite', priority: LoadPriority.high);
await loader.loadSpriteData('background_sprite', priority: LoadPriority.low);

// ❌ Bad: Load everything with same priority
// All assets loaded with default priority
```

## Troubleshooting

### Common Issues

#### 1. Slow Parsing Performance

**Symptoms:** Parsing takes longer than expected

**Solutions:**
- Check cache hit rate: `cache.statistics['hitRate']`
- Preload common assets: `cache.preloadCommonAssets(assets)`
- Monitor memory usage: `cache.statistics['memoryUsage']`
- Use performance monitoring to identify bottlenecks

#### 2. High Memory Usage

**Symptoms:** App uses too much memory

**Solutions:**
- Clear cache periodically: `cache.clearAll()`
- Monitor memory usage: `cache.statistics['memoryUsage']`
- Reduce cache size limits in configuration
- Use lazy loading for non-critical assets

#### 3. Parsing Errors

**Symptoms:** Parsing fails with errors

**Solutions:**
- Validate XML format before parsing
- Check asset directory exists and is accessible
- Review error messages for specific issues
- Use try-catch blocks for error handling

#### 4. Cache Misses

**Symptoms:** Low cache hit rate

**Solutions:**
- Identify frequently used assets
- Preload common assets at startup
- Check asset naming consistency
- Monitor usage patterns with performance stats

### Debug Mode

Enable detailed logging for troubleshooting:

```dart
// Enable debug logging (implementation-specific)
void enableDebugMode() {
  // Set debug flags
  // Enable verbose logging
  // Monitor all operations
}
```

### Performance Analysis

```dart
// Analyze performance bottlenecks
void analyzePerformance() {
  final stats = ElementParserFactory.getPerformanceStats();
  
  print('Performance Analysis:');
  print('- Total operations: ${stats['totalParsedElements']}');
  print('- Cache hit rate: ${stats['cacheStats']['hitRate']}');
  print('- Memory usage: ${stats['cacheStats']['memoryUsage']}');
  print('- Most used parser: ${stats['mostUsedParser']}');
  
  // Identify optimization opportunities
  if (stats['cacheStats']['hitRate'] < 0.8) {
    print('Recommendation: Improve asset preloading');
  }
}
```

## Support

For additional support:

1. Check the API documentation for detailed function references
2. Review the test files for usage examples
3. Monitor performance statistics for optimization opportunities
4. Use comprehensive error handling for production deployments

## Version Information

- **User Guide Version:** 2.0
- **Last Updated:** 2025-01-14
- **Compatible with:** SimShare Player Parser API 2.0+
