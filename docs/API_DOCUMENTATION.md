# SimShare Player Parser API Documentation

## Overview

The SimShare Player Parser is a comprehensive system for parsing and generating simulation definition (SimDef) XML files. It provides high-performance parsing with caching, lazy loading, and real-time performance monitoring.

## Table of Contents

1. [Core Parser Functions](#core-parser-functions)
2. [Element Parser Factory](#element-parser-factory)
3. [Performance System](#performance-system)
4. [Error Handling](#error-handling)
5. [Usage Examples](#usage-examples)
6. [Performance Optimization](#performance-optimization)

## Core Parser Functions

### `parseSimDef(String simdefXml, Directory dir) -> Future<Scenario>`

**Description:** Main entry point for parsing SimDef XML into a Scenario object.

**Parameters:**
- `simdefXml` (String): The XML content of the simulation definition file
- `dir` (Directory): The directory containing simulation assets and files

**Returns:** `Future<Scenario>` - Complete scenario object with all parsed simulation data

**Throws:**
- `ArgumentError` - If XML is invalid or required data is missing
- `FileSystemException` - If asset files cannot be accessed

**Example:**
```dart
final xmlContent = await File('simulation.xml').readAsString();
final assetDir = Directory('assets/simulation');
final scenario = await parseSimDef(xmlContent, assetDir);
```

### `generateSimDef(Scenario simValue, {bool cleanup = false}) -> String`

**Description:** Generate SimDef XML from a Scenario object.

**Parameters:**
- `simValue` (Scenario): The scenario object to convert to XML
- `cleanup` (bool, optional): Whether to perform cleanup of unused sound files

**Returns:** `String` - Formatted XML string representing the complete simulation definition

**Throws:**
- `ArgumentError` - If scenario data is invalid or incomplete

**Example:**
```dart
final xmlString = generateSimDef(scenario, cleanup: true);
await File('output.xml').writeAsString(xmlString);
```

### `parseMask(String id, String definition, String locationId) -> Mask`

**Description:** Parse a mask definition string into a Mask object.

**Parameters:**
- `id` (String): The unique identifier for the mask
- `definition` (String): The mask definition string containing type and coordinates
- `locationId` (String): The ID of the location this mask belongs to

**Returns:** `Mask` - Parsed mask object with properties

**Throws:**
- `ArgumentError` - If definition format is invalid

**Example:**
```dart
final mask = parseMask('mask1', 'showOutside|true|10,20,100,200', 'location1');
```

### `parseMaskSprites(String definition) -> List<String>`

**Description:** Retrieve sprite IDs from a mask definition string.

**Parameters:**
- `definition` (String): The mask definition string to parse

**Returns:** `List<String>` - List of sprite IDs found in the mask definition

**Example:**
```dart
final spriteIds = parseMaskSprites('sprite1,sprite2,sprite3');
```

### `parseSimObjectsInMask(String definition) -> List<String>`

**Description:** Retrieve simulation object IDs from a mask definition string.

**Parameters:**
- `definition` (String): The mask definition string to parse

**Returns:** `List<String>` - List of simulation object IDs

**Example:**
```dart
final objectIds = parseSimObjectsInMask('object1,object2,object3');
```

### `parseTimingTrigger(String trigger) -> String`

**Description:** Parse and normalize a timing trigger value.

**Parameters:**
- `trigger` (String): The timing trigger value to parse

**Returns:** `String` - Normalized timing trigger string

**Example:**
```dart
final normalizedTrigger = parseTimingTrigger('5');
```

## Element Parser Factory

### `ElementParserFactory.initialize()`

**Description:** Initialize all element parsers with performance monitoring.

**Example:**
```dart
ElementParserFactory.initialize();
```

### `ElementParserFactory.parseElement(xml.XmlElement element, xml.XmlDocument simdef, Map<String, dynamic> context) -> Future<SimObject?>`

**Description:** Parse an element using the appropriate parser with performance monitoring.

**Parameters:**
- `element` (xml.XmlElement): The XML element to parse
- `simdef` (xml.XmlDocument): The complete SimDef document
- `context` (Map<String, dynamic>): Parsing context with metadata

**Returns:** `Future<SimObject?>` - Parsed simulation object or null if parsing fails

**Example:**
```dart
final context = {
  "locationId": "loc1",
  "directory": assetDir,
  "metadata": metadata,
};
final simObject = await ElementParserFactory.parseElement(element, simdef, context);
```

### `ElementParserFactory.getPerformanceStats() -> Map<String, dynamic>`

**Description:** Get comprehensive parser performance statistics.

**Returns:** `Map<String, dynamic>` - Performance statistics including usage, timing, and cache data

**Example:**
```dart
final stats = ElementParserFactory.getPerformanceStats();
print('Total parsed elements: ${stats['totalParsedElements']}');
print('Most used parser: ${stats['mostUsedParser']}');
```

### `ElementParserFactory.preloadCommonAssets() -> Future<void>`

**Description:** Preload commonly used assets based on usage patterns.

**Example:**
```dart
await ElementParserFactory.preloadCommonAssets();
```

## Performance System

### AssetCache

**Description:** High-performance asset cache with memory management.

#### `AssetCache().loadSpriteImage(String assetName) -> Future<ui.Image>`

Load sprite image with caching.

#### `AssetCache().loadSpriteMetadata(String assetName) -> Future<Map<String, dynamic>>`

Load sprite metadata with caching.

#### `AssetCache().statistics -> Map<String, dynamic>`

Get cache statistics including hit rates and memory usage.

### PerformanceMonitor

**Description:** Performance monitoring and profiling system.

#### `PerformanceMonitor().timeOperation<T>(String operationName, T Function() operation) -> T`

Time a synchronous operation.

#### `PerformanceMonitor().timeAsyncOperation<T>(String operationName, Future<T> Function() operation) -> Future<T>`

Time an asynchronous operation.

#### `PerformanceMonitor().getStats(String operationName) -> PerformanceStats?`

Get performance statistics for a specific operation.

### LazyLoader

**Description:** Lazy loading system for assets with priority-based loading.

#### `LazyLoader().loadSpriteData(String assetName, {LoadPriority priority}) -> Future<Map<String, dynamic>>`

Load sprite data with lazy loading and priority.

#### Load Priorities:
- `LoadPriority.immediate` - Load immediately, bypass queue
- `LoadPriority.high` - Load as soon as possible
- `LoadPriority.normal` - Standard loading priority
- `LoadPriority.low` - Load when resources are available
- `LoadPriority.background` - Load in background when idle

## Error Handling

All parser functions implement comprehensive error handling:

### Common Error Types:
- `ArgumentError` - Invalid input parameters or data format
- `FileSystemException` - File access or directory issues
- `FormatException` - XML parsing or data format errors

### Error Recovery:
- Graceful degradation on non-critical errors
- Meaningful error messages with context
- Continuation of processing when possible

### Best Practices:
```dart
try {
  final scenario = await parseSimDef(xmlContent, assetDir);
  // Process scenario
} catch (e) {
  print('Parsing failed: $e');
  // Handle error appropriately
}
```

## Usage Examples

### Complete Parsing Workflow:
```dart
// Initialize the parser system
ElementParserFactory.initialize();

// Load and parse simulation
final xmlFile = File('simulation.xml');
final assetDir = Directory('assets');
final xmlContent = await xmlFile.readAsString();

try {
  final scenario = await parseSimDef(xmlContent, assetDir);
  print('Parsed ${scenario.locations.length} locations');
  
  // Get performance statistics
  final stats = ElementParserFactory.getPerformanceStats();
  print('Cache hit rate: ${stats['cacheStats']['hitRate']}');
  
} catch (e) {
  print('Error parsing simulation: $e');
}
```

### Performance Monitoring:
```dart
final monitor = PerformanceMonitor();

final result = await monitor.timeAsyncOperation('custom_operation', () async {
  // Your operation here
  return await someAsyncOperation();
});

final stats = monitor.getStats('custom_operation');
print('Average time: ${stats?.averageTimeMs}ms');
```

## Performance Optimization

### Recommended Practices:

1. **Preload Assets:** Use `ElementParserFactory.preloadCommonAssets()` for frequently used assets
2. **Monitor Performance:** Regularly check performance statistics
3. **Cache Management:** Monitor cache hit rates and memory usage
4. **Error Handling:** Implement proper error handling for production use
5. **Batch Processing:** Process multiple elements in batches for better performance

### Performance Metrics:

- **Cache Hit Rate:** Target 80-95% for optimal performance
- **Memory Usage:** Monitor to prevent memory bloat
- **Parse Time:** Track parsing time for performance optimization
- **Element Count:** Monitor elements per second for throughput

## Version Information

- **API Version:** 2.0
- **Last Updated:** 2025-01-14
- **Compatibility:** Flutter 3.0+, Dart 3.0+
