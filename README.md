# simsushare_player

SimsUShare Builder App

## Structure

There are 2 applications at the moment. We have the core/main application which is following standard flutter structure and then we have an embedded web flutter player that is inherits some components from the core app.

## Notes

- When building the app, if the signing is enabled from XCode, make sure to keep the "code-sign" option in appdmg-config.json. Otherwise, comment it out if you don't have the signing certificate so that signing can be skipped.

- In scenario locations, the `image` variable is primarily a string, but it adapts its type based on the platform and import method.

    1. **Desktop Version**:
        - The `image` variable holds the string value of the image file path.

    2. **Web Version**:
        - When creating a new simulation, the `image` variable contains a base64 data string of the image.
        - For existing simulations imported from a repository, the images are parsed as bytes and converted to a string using `String.fromCharCodes` to store in the `image` variable.


